{
  "extends": ["airbnb", "airbnb/hooks", "prettier"],
  "parser": "@babel/eslint-parser",
  "parserOptions": {
    "requireConfigFile": false,
    "babelOptions": { "presets": ["@babel/preset-react"] }
  },
  "env": { "node": true },
  "rules": {
    "no-plusplus": "off",
    "no-use-before-define": "off",
    "import/extensions": ["error", "ignorePackages"],

    // React is automatically resolved, import is not required
    "react/react-in-jsx-scope": "off",

    // Overwritten to be consistent with Prettier
    "object-curly-newline": "off",
    "operator-linebreak": "off",
    "react/require-default-props": "off",

    //new

    // for react component
    "react/function-component-definition": [
      "error",
      {
        "namedComponents": ["function-declaration", "arrow-function"],
        "unnamedComponents": ["function-expression", "arrow-function"]
      }
    ]
  }
}
