NODE_ENV=development
SHOPIFY_API_KEY=
SHOPIFY_PRODUCT_OPTIONS_ID=

#GCP
GOOGLE_CLOUD_PROJECT_ID=    #GCP project id
# DATASTORE
DATASTORE_NAMESPACE= #DATASTORE Database namespace

# track log
MIXPANEL_ENABLED=false # Enable or Disable Mixpanel log. note: if enable "In GCP Secret Manager API needs to enabled and a api key needs to generate.  
MIX_PANEL_API_KEY=productOptionsMixpanelAPIKey #GCP Secret Manager API key nameing this.

# sentry
SENTRY_ENABLED=false # Enable or Sentry. note: if enable "In GCP Secret Manager API needs to enabled and a api key needs to generate.
SENTRY_DSN=productOptionsSentryDSN #GCP Secret Manager API key nameing this.

# FluentCRM
FLUENT_CRM_ENABLE=false
FLUENT_CRM_API_BASE_URL=https://easy-flow.app/wp-json/fluent-crm/v2
FLUENT_CRM_API_USER=
FLUENT_CRM_API_PASSWORD=

#prisma
DATABASE_URL="file:./db/dev.db"

# subscription test mode
SHOPIFY_SUBSCRIPTION_TEST_MODE=false

# shopify
APP_HANDLE=
VITE_APP_HANDLE=

# shopify api version 
SHOPIFY_API_VERSION=2025-01

# Clarity script 
LOAD_CLARITY_SCRIPT=false
VITE_LOAD_CLARITY_SCRIPT=false

#Theme app extension
THEME_APP_EXTENSION_APP_BLOCK_ID=
VITE_THEME_APP_EXTENSION_APP_BLOCK_ID=