service: product-options
runtime: nodejs20
env: standard

instance_class: F4

automatic_scaling:
  min_instances: 1
  max_instances: 2 # Allows scaling
  target_cpu_utilization: 0.75
  target_throughput_utilization: 0.75

inbound_services:
  - warmup

handlers:
  # - url: /assets
  #   static_dir: web/frontend/dist/assets

  # - url: /static/index.html
  #   static_files: web/frontend/dist/assets/index-7a30c8d2.js
  #   upload: web/frontend/dist/assets/index-7a30c8d2.js
  - url: /.*
    secure: always
    redirect_http_response_code: 301
    script: auto
