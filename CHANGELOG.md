# Changelog

All notable changes to this project will be documented in this file.

## [1.0.0]

- Initial APP after the takeover 🎉

## [1.1.0]

`Revamped`

- Pricing page
- Pricing plans

`Feature`

- Ability to migrate free plan / cancel subscription
- Coupon code support for all plans

`Improvements`

- Static skeleton loader for better user experience and CLS & LCP optimization

`Bug-fix`

- Some minor bug fixes

## [1.1.1]

`Added`

- Clarity script for analytics

`Improvements`

- Dynamic Theme App Extension App Block ID , App Handle , extension deep link path

`Bug-fix`

- Some minor bug fixes

## [1.2.0]

`Improvements`

- Update Sentry configuration with environment-based DNS
- Moved Sentry DSN to centralized configs
- Added fallback Sentry DNS in configuration
- Updated Sentry initialization in frontend and utilities
- Introduced configurable Sentry secret key retrieval

`Bug-fix`

- Some minor bug fixes

## [1.2.1]

`Improvements`

- BFS feedbacks from Shopify
- CLS improvements
- Added fallback Sentry DNS in configuration
- Updated Sentry initialization in frontend and utilities
- Implements validation for option values

`Bug-fix`

- Some minor bug fixes

## [1.3.0]

`Improvements`

- React router implementation for better seamless navigation
- Option refresh after add to cart product once ~ `Theme app extension`
- Option template with preview

`Bug-fix`

- Some minor bug fixes

## [1.3.1]

`Improvements`

- Embedded HTML New option added
