{% # theme-check-disable LiquidTag %}

{% liquid
  # Get values that will be initially hidden
  assign option_id_number = option.optionId | plus: 0
  assign option_actions = actions | where: 'optionId', option_id_number

  assign hidden_values = '' | split: ''

  # If there are no shown values, hide the option
  assign is_show_option = true

  for action in option_actions
    if action.initiallyHiddenValues == true
      assign is_show_option = false
    else
      assign hidden_values = hidden_values | concat: action.initiallyHiddenValues
    endif
  endfor

  if is_show_option
    assign is_show_option = false

    for value in optionValues
      unless hidden_values contains value.title
        assign is_show_option = true
      endunless
    endfor
  endif
%}

{% capture class_name -%}
  ef__product-option
  {%- if option.type != 'Image swatch' and option.type != 'Color swatch' and option.type != 'Radio button' %}
    product-form__input
  {% endif -%}
{% endcapture %}

{% assign product_option_customizations = shop.metafields.product_options.preferences.value %}

<div
  class='{{ class_name }}'
  style='margin-bottom: 1rem;{% unless is_show_option %} display: none;{% endunless %}'

  data-option-title='{{ option.title | escape }}'
  data-option-type='{{ option.type }}'

  {% if option.isShowSelectedValues %}
    data-option-show-selected-values='true'
  {% endif %}

  {% if option.inCartName %}
    data-option-in-cart-name='{{ option.inCartName | escape }}'
  {% endif %}

  {% if option.isRequired %}
    data-is-required='true'
  {% endif %}
>
  {% assign single_value = optionValues[0] %}

  {% assign single_value_types = 'Text box, Multi-line text box, Number field, Date picker, File upload, HTML'
    | split: ', '
  %}

  <div class='form__label ef__option-title ef__option-title-{{ option.type | handle }}'>
    {{ option.title }}

    {% # Calculate price for each value %}
    {% liquid
      assign value_prices = ''

      for value in optionValues
        if product_option_customizations.isGetPricesOnStorefront
          assign variant_id = value.addonProduct.variantId | remove: 'gid://shopify/ProductVariant/' | plus: 0
          assign added_product_variant = all_products[value.addonProduct.handle].variants | where: 'id', variant_id | first

          assign value_prices = value_prices | append: added_product_variant.price
        else
          assign price_value = value.price | times: 100
          assign value_prices = value_prices | append: price_value
        endif

        assign value_prices = value_prices | append: ','
      endfor

      assign value_prices = value_prices | split: ','
    %}

    {% # Add the price or default value prices to the title %}
    {% if single_value_types contains option.type %}
      <span class='ef__option-value-price'>{{ value_prices[0] }}</span>
    {% elsif option.isShowSelectedValues %}
      {% assign default_values = optionValues | where: 'isDefault', true %}

      <span class='ef__option-selected-values'>
        {% if default_values != blank %}- {% endif %}

        {% for value in optionValues %}
          {% if value.isDefault %}
            {% assign default_value_title = value.title %}

            {% if value_prices[forloop.index0] %}
              {% assign default_value_title = default_value_title
                | append: "<span class='ef__option-selected-values-price'>"
                | append: value_prices[forloop.index0]
                | append: '</span>'
              %}
            {% endif %}

            {% assign default_value_title = default_value_title | sort %}
            {% assign default_value_titles = default_value_titles | concat: default_value_title %}
          {% endif %}
        {% endfor %}

        {{ default_value_titles | join: ', ' }}
      </span>
    {% endif %}

    {% if option.isRequired %}<span class="ef_option-title-required">*</span>{% endif %}
  </div>

  <div class='caption ef__option-description ef__option-description-{{ option.type | handle }}'>
    {{ option.description }}
  </div>

  {% assign check_types = 'Checkbox, Button, Image swatch, Color swatch, Radio button'
    | split: ', '
  %}

  {% if check_types contains option.type %}
    <div
      class='ef__option-values ef__option-values-{{ option.type | handle }} {% if option.type == 'Button' %}product-form__input--pill{% endif %}'

      data-option-multi-select='{{ option.isMultiSelect }}'
      data-option-minimum-selections='{{ option.minimumSelectionCount }}'
      data-option-maximum-selections='{{ option.maximumSelectionCount }}'
    >
      {% for value in optionValues %}
        {% assign random_number = 'now' | date: '%N' %}

        {% capture id %}option-{{ option.optionId }}-{{ random_number }}{% endcapture %}

        {% capture title %}
          {{- value.title -}}

          <span class='ef__option-value-price'>{{ value_prices[forloop.index0] }}</span>
        {% endcapture %}

        <div
          class='ef__option-value ef__option-value-{{ option.type | handle }}'

          {% if hidden_values contains value.title %}
            style='display: none;'
          {% elsif option.type == 'Button'
            or option.type == 'Image swatch'
            or option.type == 'Color swatch'
          %}
            style='display: inline-block; margin-bottom: 0.5rem;'
          {% else %}
            style='display: block;'
          {% endif %}
        >
          {% capture input_class -%}
            ef__product-option-input
            {%- if option.type == 'Image swatch' or option.type == 'Color swatch' %} ef__swatch-input{% endif -%}
          {% endcapture %}

          <input
            {% if option.isMultiSelect
              or option.type != 'Button'
              and option.type != 'Radio button'
            %}
              type='checkbox'
            {% else %}
              type='radio'
            {% endif %}

            name='{{ option.optionId }}'
            id='{{ id }}'
            class='{{ input_class }}'

            {% if value.isDefault %}
              checked
            {% endif %}

            data-value-title='{{ value.title | escape }}'
            data-value-price='{{ value_prices[forloop.index0] }}'
            data-option-id='{{ option.optionId }}'
            data-option-type='{{ option.type }}'

            {% if option.type == 'Image swatch' or option.type == 'Color swatch' %}
              style='display: none'
            {% endif %}

            {% if value.addonProduct %}
              data-variant-id='{{ value.addonProduct.variantId }}'
            {% endif %}
          >

          {% capture label_class -%}
            {%- if option.type == 'Image swatch' %} ef__swatch-image{% endif -%}
            {%- if option.type == 'Color swatch' %} ef__swatch-color{% endif -%}
          {% endcapture %}

          <label
            for='{{ id }}'
            class='{{ label_class }}'

            {% if option.type == 'Image swatch' %}
              style='background: url("{{ value.image.url }}") no-repeat center center; background-size: contain;'

              {% if value.isEnlargeOnHover %}
                data-enlarge-on-hover='true'
              {% endif %}
            {% endif %}

            {% if option.type == 'Color swatch' %}
              style='background-color: {{ value.color }};'
            {% endif %}
          >
            {% if option.type != 'Image swatch' and option.type != 'Color swatch' -%}
              {{- title -}}
            {% else %}
              <span class='ef__hover-text'>{{- title -}}</span>
            {%- endif %}

            {% if option.type == 'Checkbox' and value.image.url %}
              {% assign image_url = value.image.url %}

              {% # theme-check-disable RemoteAsset %}
              <img
                src='{{ image_url }}'
                width='48'
                height='48'
                style='display: block; margin: 0.5rem 0;'
                loading='lazy'
              >
              {% # theme-check-enable RemoteAsset %}
            {% endif %}
          </label>

          {% if option.type != 'Image swatch' and option.type != 'Color swatch' %}
            <em
              class='ef__option-value-description ef__option-value-description-{{ option.type | handle }}'
            >
              {{- value.description -}}
            </em>
          {% endif %}
        </div>
      {% endfor %}
    </div>
  {% elsif option.type == 'Dropdown' %}
    <div class='select ef__option-values ef__option-values-{{ option.type | handle }}'>
      <select class='select__select' data-option-id='{{ option.optionId }}'>
        {% assign dropdown_placeholder = product_option_customizations.dropdownPlaceholder
          | default: product_option_customizations.dropdown_placeholder
        %}

        <option value='' class='ef__option-value ef__option-value-{{ option.type | handle }}'>
          {% if dropdown_placeholder %}
            {{
              dropdown_placeholder
              | default: 'Choose %option-title%'
              | replace: '%option-title%', option.title
            }}
          {% else %}
            {{ option.title }}
          {% endif %}
        </option>

        {% for value in optionValues %}
          <option
            value='{{ value.title | escape }}'
            name='{{ value.title | escape }}'
            class='ef__product-option-input ef__option-value ef__option-value-{{ option.type | handle }}'

            data-value-title='{{ value.title | escape }}'
            data-value-price='{{ value_prices[forloop.index0] }}'
            data-option-id='{{ option.optionId }}'

            {% if value.isDefault %}
              selected
            {% endif %}

            {% if hidden_values contains value.title %}
              style='display: none;'
            {% endif %}

            {% if value.addonProduct %}
              data-variant-id='{{ value.addonProduct.variantId }}'
            {% endif %}
          >
            {{- value.title -}}
          </option>
        {% endfor %}
      </select>

      {% # Dropdown caret icon %}
      <svg aria-hidden='true' focusable='false' class='icon icon-caret' viewBox='0 0 10 6'>
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M9.354.646a.5.5 0 00-.708 0L5 4.293 1.354.646a.5.5 0 00-.708.708l4 4a.5.5 0 00.708 0l4-4a.5.5 0 000-.708z"
          fill="currentColor"
        ></path>
      </svg>
    </div>
  {% elsif option.type == 'HTML' %}
    {% assign value = single_value %}
    <div class='ef__option-value ef__option-value-html'>
      <div
        class='ef__html-content'
        data-option-id='{{ option.optionId }}'
        data-value-price='{{ value_prices[0] }}'
      >
        {{ value.htmlContent }}
      </div>
    </div>
  {% elsif single_value_types contains option.type and option.type != 'HTML' %}
    {% assign value = single_value %}

    <div
      class='field ef__option-value ef__option-value-{{ option.type | handle }}'
      style='position: relative;'
    >
      {% assign input_tag_types = 'Text box, Number field, Date picker, File upload'
        | split: ', '
      %}

      {% if input_tag_types contains option.type %}
        {% liquid
          capture input_type
            if option.type == 'Number field'
              echo 'number'
            elsif option.type == 'Date picker'
              echo 'date'
            elsif option.type == 'File upload'
              echo 'file'
            else
              echo 'text'
            endif
          endcapture

          if option.allowedFileTypes.value
            assign allowed_file_types = option.allowedFileTypes.value
          else
            assign allowed_file_types = option.allowedFileTypes
          endif

          assign accept = ''

          for fileType in allowed_file_types
            unless fileType contains '.' or fileType contains '/'
              assign accept = accept | append: '.' | append: fileType
            else
              assign accept = accept | append: fileType
            endunless

            assign accept = accept | append: ','
          endfor

          assign accept = accept | split: ',' | slice: 0, accept.size | join: ', '
        %}

        <label for='option-{{ option.optionId }}' class='ef__product-option-single-value-label'>
          <input
            type='{{ input_type }}'
            name='{{ option.title | escape }}'
            id='option-{{ option.optionId }}'
            class='ef__product-option-input field__input'
            autocomplete='{{ option.title | escape }}'

            {% if option.placeholderText %}
              placeholder='{{ option.placeholderText | escape }}'
            {% endif %}

            data-option-id='{{ option.optionId }}'
            data-value-price='{{ value_prices[0] }}'

            {% if value.addonProduct %}
              data-variant-id='{{ value.addonProduct.variantId }}'
            {% endif %}

            {% if option.type == 'Number field' %}
              min='{{ option.minimumValue }}'
              max='{{ option.maximumValue }}'
            {% elsif option.type == 'Text box' %}
              minlength='{{ option.minimumValue }}'
              maxlength='{{ option.maximumValue }}'
            {% endif %}

            {% if option.type == 'File upload' %}
              accept='{{ accept }}'
            {% endif %}
          >

          {% if option.type == 'File upload' %}
            <div class='ef__upload-input' style='text-align: center;'>
              <input
                type='button'
                value='{{ product_option_customizations.fileUploadButtonText | default: 'Choose file' }}'
              >

              <span>
                {{-
                  product_option_customizations.dragAndDropText
                  | default: 'or drop files to upload'
                -}}
              </span>
            </div>

            <div class='ef__upload-loading' style='display:none;'>
              <div class='ef__loading'></div>
              <div style='margin-top: 0.5rem'>
                {{ product_option_customizations.uploadLoadingText | default: 'Uploading...' }}
              </div>
            </div>

            <div class='ef__upload-content' style='display:none;'>
              {% # theme-check-disable ImgWidthAndHeight %}
              <img alt='uploaded file' loading='eager'>
              {% # theme-check-enable ImgWidthAndHeight %}

              <span></span>

              {% # x icon %}
              <svg
                fill='#000000'
                width='2rem'
                height='2rem'
                viewBox='0 0 256 256'
                id='Flat'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d="M202.82861,197.17188a3.99991,3.99991,0,1,1-5.65722,5.65624L128,133.65723,58.82861,202.82812a3.99991,3.99991,0,0,1-5.65722-5.65624L122.343,128,53.17139,58.82812a3.99991,3.99991,0,0,1,5.65722-5.65624L128,122.34277l69.17139-69.17089a3.99991,3.99991,0,0,1,5.65722,5.65624L133.657,128Z"
                />
              </svg>
            </div>
          {% endif %}
        </label>
      {% else %}
        <textarea
          name='{{ option.title | escape }}'
          id='option-{{ option.optionId }}'
          class='ef__product-option-input field__input'
          autocomplete='{{ option.title | escape }}'
          rows='3'
          style='height: auto;'

          {% if option.placeholderText %}
            placeholder='{{ option.placeholderText | escape }}'
          {% endif %}

          data-option-id='{{ option.optionId }}'
          data-value-price='{{ value_prices[0] }}'

          minlength='{{ option.minimumValue }}'
          maxlength='{{ option.maximumValue }}'

          {% if value.addonProduct %}
            data-variant-id='{{ value.addonProduct.variantId }}'
          {% endif %}
        ></textarea>
      {% endif %}

      {% if option.maximumValue > 0
        and option.type == 'Text box'
        or option.type == 'Multi-line text box'
      %}
        <span class='ef__character-counter'>0/{{ option.maximumValue }}</span>
      {% endif %}
    </div>
  {% endif %}

  <p class='ef__product-option-error caption-large' style='color: red;'></p>
</div>

{% # theme-check-enable LiquidTag %}
