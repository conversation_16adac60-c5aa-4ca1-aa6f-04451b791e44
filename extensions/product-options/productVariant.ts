export interface ProductVariant {
  id:                       number;
  title:                    string;
  option1:                  string;
  option2:                  null;
  option3:                  null;
  sku:                      string;
  requires_shipping:        boolean;
  taxable:                  boolean;
  featured_image:           null;
  available:                boolean;
  name:                     string;
  public_title:             string;
  options:                  string[];
  price:                    number;
  weight:                   number;
  compare_at_price:         number | null;
  inventory_management:     string;
  barcode:                  null | string;
  requires_selling_plan:    boolean;
  selling_plan_allocations: any[];
  quantity_rule:            QuantityRule;
}

export interface QuantityRule {
  min:       number;
  max:       null;
  increment: number;
}
