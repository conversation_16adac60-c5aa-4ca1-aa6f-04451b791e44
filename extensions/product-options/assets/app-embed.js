"use strict";

/* eslint-disable no-underscore-dangle */

/** @typedef {{ line?: number, id: string, quantity: number }} CartChangeData */

(function () {
  const originalFetch = fetch;
  const originalXhrSend = window.XMLHttpRequest.prototype.send;

  const shopifyRootRoute = window.Shopify.routes.root;

  const productOptionCustomizationsElement = document.getElementById("ef__product-option-customizations");

  const productOptionCustomizationsText =
    productOptionCustomizationsElement && productOptionCustomizationsElement.textContent;

  /**
   * @type {Partial<
   *   typeof import('../../../web/defaults').defaultStyleOptions & LegacyCustomizationFields
   * >}
   */
  const productOptionCustomizations = JSON.parse(productOptionCustomizationsText || "[]");

  // This will hide the add-on product quantity field if enabled in the app settings.
  hideAddonFields();

  if (window.location.pathname === "/cart") {
    // We only want to run this on the cart page because
    // some themes do not use XHR to delete items from the cart.
    removeAddonIfBaseProductNotExists();
  }

  if (typeof fetch === "function") {
    window.fetch = function () {
      let url = arguments[0] instanceof URL ? arguments[0] : new URL(arguments[0], window.location.href);
      url = arguments[0] instanceof Request ? new URL(arguments[0].url, window.location.href) : url;

      // Listen for cart change events and update the add-on product quantities
      if (
        url.origin === window.location.origin &&
        (url.pathname === `${shopifyRootRoute}cart/change` || url.pathname === `${shopifyRootRoute}cart/change.js`) &&
        (arguments[1].body instanceof FormData || typeof arguments[1].body === "string")
      ) {
        const originalThis = this;
        const originalArguments = arguments;

        return window.EasyFlowProductOptions.parseFetchBody(originalArguments)
          .then(function (body) {
            return changeAddonItems(body);
          })
          .then(function (updatedCartData) {
            return originalFetch.apply(originalThis, /** @type {any} */ (originalArguments)).then(function (response) {
              if (updatedCartData) {
                hideAddonFields(updatedCartData);
              }

              return response;
            });
          });
      }

      // Listen for cart add events and hide the add-on product field quantities if necessary.
      if (
        url.origin === window.location.origin &&
        (url.pathname === `${shopifyRootRoute}cart/add` || url.pathname === `${shopifyRootRoute}cart/add.js`)
      ) {
        return originalFetch.apply(this, /** @type {any} */ (arguments)).then(function (response) {
          hideAddonFields();
          return response;
        });
      }

      return originalFetch.apply(this, /** @type {any} */ (arguments));
    };
  }

  // Listen for cart change link clicks and update the add-on products
  document
    .querySelectorAll(
      `a[href^="${shopifyRootRoute}cart/change"], a[href^="${window.location.origin + shopifyRootRoute}cart/change"]`
    )
    .forEach(function (element) {
      if (!(element instanceof HTMLAnchorElement)) {
        return;
      }

      element.addEventListener("click", function (event) {
        if (!element.href) {
          return;
        }

        event.preventDefault();
        event.stopPropagation();

        const url = new URL(element.href, window.location.href);

        const searchParams = new URLSearchParams(url.search);

        // Check if `id` or `line` exists in the search parameters
        const hasId = searchParams.has("id");
        const hasLine = searchParams.has("line");

        console.log("delete link clicked", element.href);
        console.log("hasId", hasId);
        console.log("hasLine", hasLine);
        changeAddonItems(new URL(element.href, window.location.href).search).then(function (updatedData) {
          // window.location.href = element.href;
          console.log("final updated", updatedData);

          // added by rumi
          // window.location.reload();

          if (hasId) {
            window.location.reload();
            // Reload the page
            // setTimeout(() => {
            //   window.location.reload();
            // }, 500); // 500 milliseconds = 0.5 seconds
          } else if (hasLine) {
            // Redirect to the new href
            // window.location.href = element.href;
            // Add a 0.5-second delay before redirecting
            setTimeout(() => {
              window.location.href = element.href;
            }, 500); // 500 milliseconds = 0.5 seconds
          } else {
            // Default action if neither `id` nor `line` exists
            console.log("No id or line found in search parameters.");
            window.location.reload();
          }

          // window.location.assign(url.toString());
          // changeAddonItems(url.search).then(function () {
          //   window.location.reload();
          // });
        });
      });
    });

  // Override XMLHttpRequest to intercept cart updates.

  window.XMLHttpRequest.prototype.send = function () {
    // @ts-ignore
    const url = new URL(this._url, window.location.href);

    const originalThis = this;
    const originalArguments = arguments;

    if (
      url.origin === window.location.origin &&
      (url.pathname === `${shopifyRootRoute}cart/change` || url.pathname === `${shopifyRootRoute}cart/change.js`) &&
      (arguments[0] instanceof FormData || typeof arguments[0] === "string")
    ) {
      changeAddonItems(arguments[0]).then(function (updatedCartData) {
        if (updatedCartData) {
          originalThis.addEventListener("load", function () {
            hideAddonFields(updatedCartData);
          });
        }

        originalXhrSend.apply(originalThis, /** @type {any} */ (originalArguments));
      });

      return;
    }

    // Listen for cart add events and hide the add-on product field quantities if necessary.
    if (
      url.origin === window.location.origin &&
      (url.pathname === `${shopifyRootRoute}cart/add` || url.pathname === `${shopifyRootRoute}cart/add.js`)
    ) {
      originalThis.addEventListener("load", function () {
        hideAddonFields();
      });
    }

    originalXhrSend.apply(this, /** @type {any} */ (arguments));
  };

  /**
   * @param {FormData | string} rawCartChangeData
   */
  function changeAddonItems(rawCartChangeData) {
    /** @type {CartChangeData} */
    let cartChangeData = {};
    let isLink = false;

    if (typeof rawCartChangeData === "string") {
      try {
        // `rawCartChangeData` is a JSON string
        cartChangeData = JSON.parse(rawCartChangeData);
      } catch (_) {
        // `rawCartChangeData` is a query string
        // Handle array and object values by building a FormData object first,
        // then converting it to an object
        const formData = new FormData();

        new URLSearchParams(rawCartChangeData).forEach(function (value, name) {
          formData.append(name, value);
        });

        cartChangeData = /** @type {CartChangeData} */ (
          /** @type {unknown} */ (window.EasyFlowProductOptions.formDataToObject(formData))
        );

        isLink = true;
      }
    } else if (rawCartChangeData instanceof FormData) {
      // `rawCartChangeData` is a FormData object
      cartChangeData = /** @type {CartChangeData} */ (
        /** @type {unknown} */ (window.EasyFlowProductOptions.formDataToObject(rawCartChangeData))
      );
    } else {
      throw new Error("Invalid cart data type");
    }

    cartChangeData.quantity = parseInt(cartChangeData.quantity.toString(), 10);
    cartChangeData.line = cartChangeData.line && parseInt(cartChangeData.line.toString(), 10);

    return originalFetch(`${shopifyRootRoute}cart.js`, {
      method: "GET",
      headers: { Accept: "application/json" },
    })
      .then(function (response) {
        return response.json();
      })
      .then(function (/** @type {{ items: CartItem[] }} */ previousCartData) {
        // Some requests do not return the id, so we need to find the updatedCartLine
        // based on the line number

        const updatedCartItem = cartChangeData.id
          ? previousCartData.items.filter(function (item) {
              return item.key === cartChangeData.id;
            })[0]
          : typeof cartChangeData.line !== "undefined" && previousCartData.items[cartChangeData.line - 1];

        // console.log('root', updatedCartItem);
        // console.log('isLink_first', isLink);

        if (!updatedCartItem) {
          return undefined;
        }

        // Remove deleted items from the cart data
        const updatedCartData =
          cartChangeData.quantity === 0
            ? Object.assign({}, previousCartData, {
                items: previousCartData.items.filter(function (item) {
                  return (
                    item.key !== updatedCartItem.key &&
                    (!item.properties._base_product_id ||
                      item.properties._base_product_id !== updatedCartItem.properties._id)
                  );
                }),
              })
            : previousCartData;

        // Check if the updated item is an add-on product
        if (updatedCartItem.properties._base_product_id) {
          // Find the base product of the updated add-on product
          const baseProduct = updatedCartData.items.filter(function (item) {
            return item.properties._id === updatedCartItem.properties._base_product_id;
          })[0];

          // console.log('change_first', cartChangeData);
          // console.log('baseProduct_first', baseProduct);

          // Remove the property from the base product if add-on product is removed
          if (cartChangeData.quantity === 0 && baseProduct) {
            const removedItemPropertyName = updatedCartItem.properties._property_name;
            if (!removedItemPropertyName) {
              return updatedCartData;
            }

            const updatedValueTitles = updatedCartItem.properties._value_titles
              ? updatedCartItem.properties._value_titles.filter(function (title) {
                  return title !== updatedCartItem.properties._value_title;
                })
              : [];

            /** @type {CartItem[]} */
            let updatedItems = [];

            if (updatedValueTitles.length) {
              baseProduct.properties[removedItemPropertyName] = updatedValueTitles.join(", ");

              updatedItems = updatedCartData.items.filter(function (item) {
                return (
                  item.properties._base_product_id &&
                  item.properties._base_product_id === baseProduct.properties._id &&
                  item.properties._property_name === removedItemPropertyName
                );
              });

              // Update the value titles on all the related add-ons
              updatedItems.forEach(function (addon) {
                // eslint-disable-next-line no-param-reassign
                addon.properties._value_titles = updatedValueTitles;
              });
            } else {
              delete baseProduct.properties[removedItemPropertyName];
            }

            updatedItems.push(baseProduct);

            // console.log('updatedItems', updatedItems);

            /** @type {Promise<void | Response>} */
            let updatePromise = Promise.resolve();

            // Update the addons and base product with the new properties
            // We need to do these in sequence, otherwise one update will overwrite the other
            updatedItems.forEach(function (updatedItem) {
              updatePromise = updatePromise.then(function () {
                return originalFetch(`${shopifyRootRoute}cart/change.js`, {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    id: updatedItem.key,
                    properties: updatedItem.properties,
                  }),
                });
              });
            });

            // console.log('updatedCartData', updatedCartData);
            // console.log('isLink', isLink);
            // console.log('previousCartData', previousCartData);
            // console.log('updatedCartData', updatedCartData);
            // console.log('updatedCartItem', updatedCartItem);
            // console.log('change', cartChangeData);

            return updatePromise.then(function () {
              return isLink ? forceUpdateCartItem(cartChangeData, updatedCartData, updatedCartItem) : updatedCartData;
            });
          }

          return isLink ? forceUpdateCartItem(cartChangeData, updatedCartData, updatedCartItem) : updatedCartData;
        }

        // Change add-on quantity (or remove addon) if base product quantity is changed in the cart
        const isMatchAddonQuantity =
          productOptionCustomizations.isMatchAddonQuantity || productOptionCustomizations.is_match_addon_quantity;

        /** @type {Record<string, number>} */
        const updates = {};

        // console.log('updatedCartData_1', updatedCartData);
        // console.log('isLink_1', isLink);
        // console.log('previousCartData_1', previousCartData);
        // console.log('updatedCartData_1', updatedCartData);
        // console.log('updatedCartItem_1', updatedCartItem);
        // console.log('change_1', cartChangeData);

        previousCartData.items.forEach(function (item) {
          if (
            item.properties._base_product_id &&
            item.properties._base_product_id === updatedCartItem.properties._id &&
            (isMatchAddonQuantity || cartChangeData.quantity === 0)
          ) {
            updates[item.key] = cartChangeData.quantity;
          }
        });
        // console.log('update original', updates);

        if (Object.keys(updates).length) {
          // Update the add-on product quantities
          // We need to wait for the update to finish before resolving the original fetch
          return originalFetch(`${shopifyRootRoute}cart/update.js`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ updates: updates }),
          })
            .then(function (res) {
              return res.json();
            })
            .then(function (/** @type {{ items: CartItem[] }} */ newCartData) {
              return isLink ? forceUpdateCartItem(cartChangeData, newCartData, updatedCartItem) : newCartData;
            });
        }

        return isLink ? forceUpdateCartItem(cartChangeData, updatedCartData, updatedCartItem) : updatedCartData;
      });
  }

  /**
   * @param {{ items: CartItem[] }} [initialCartData]
   */
  function hideAddonFields(initialCartData) {
    const isEnableRemoveAddonFromCart =
      productOptionCustomizations.isEnableRemoveAddonFromCart ||
      productOptionCustomizations.is_enable_remove_addon_from_cart;

    const isEnableOpenAddonProductPage =
      productOptionCustomizations.isEnableOpenAddonProductPage ||
      productOptionCustomizations.is_enable_open_addon_product_page;

    if (isEnableRemoveAddonFromCart && isEnableOpenAddonProductPage) {
      return;
    }

    new Promise(function (resolve) {
      if (initialCartData) {
        resolve(initialCartData);
      } else {
        originalFetch(`${shopifyRootRoute}cart.js`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        })
          .then(function (response) {
            return response.json();
          })
          .then(resolve);
      }
    }).then(function (/** @type {{ items: CartItem[] }} */ cartData) {
      // Add styles for hiding the quantity field
      // (the elements' `style` property gets overwritten sometimes, so we can't use that)
      const styleElement = document.getElementById("ef__hide-addon-field-styles");
      if (!styleElement) {
        return;
      }

      styleElement.innerHTML = "";

      cartData.items.forEach(function (cartItem) {
        if (!cartItem.properties._base_product_id) {
          return;
        }

        const addOnCartLine =
          cartData.items.indexOf(
            cartData.items.filter(function (item) {
              return item.key === cartItem.key;
            })[0]
          ) + 1;

        const cartLineSelectors =
          productOptionCustomizations.cartLineSelector &&
          productOptionCustomizations.cartLineSelector.replace(/%line-number%/g, addOnCartLine.toString()).split(",");

        const customSelector =
          cartLineSelectors &&
          cartLineSelectors.length &&
          productOptionCustomizations.quantityAndRemoveButtonSelector &&
          productOptionCustomizations.quantityAndRemoveButtonSelector
            .replace(/%line-number%/g, addOnCartLine.toString())
            .split(",")
            .reduce(function (selectors, quantitySelector) {
              return selectors.concat(
                cartLineSelectors.map(function (cartLineSelector) {
                  return `${cartLineSelector} ${quantitySelector}`;
                })
              );
            }, /** @type {string[]} */ ([]))
            .join(",");

        // One selector per line, parent selector until the first space
        const selectors =
          customSelector ||
          `
            #CartDrawer-Item-${addOnCartLine} .cart-item__quantity-wrapper,
            #CartItem-${addOnCartLine} .cart-item__quantity-wrapper,
            .cart__item[data-item-index="${addOnCartLine}"] .cart__quantity-counter,
            .cart-item:nth-child(${addOnCartLine}):not(#CartItem-${addOnCartLine}) .cart-item__quantity,
            .cart-item:nth-child(${addOnCartLine}) button[id^="remove"],
            #CartDrawerItem-${addOnCartLine} quantity-selector,
            #CartDrawerItem-${addOnCartLine} .remove,
            #CartItem-${addOnCartLine} quantity-selector,
            #CartItem-${addOnCartLine} .remove,
            #shop-cart tr:nth-child(${addOnCartLine}) .ef_hide
          `;

        const parentSelectors = (
          cartLineSelectors && cartLineSelectors.length
            ? cartLineSelectors
            : selectors
                .trim()
                .split("\n")
                .map(function (selector) {
                  return selector.trim().split(" ")[0];
                })
        ).map(function (selector) {
          return `${selector} a[href^="/products/"]`;
        });

        if (!isEnableRemoveAddonFromCart) {
          styleElement.innerHTML += `
            ${selectors} {
              display: none !important;
            }
          `;
        }

        if (!isEnableOpenAddonProductPage) {
          styleElement.innerHTML += `
            ${parentSelectors.join(",\n")} {
              pointer-events: none;
              cursor: default;
              text-decoration: none;
            }
          `;
        }
      });
    });
  }

  function forceUpdateCartItem(cartChangeData, updatedCartData, updatedCartItem) {
    if (cartChangeData) {
      /** @type {Record<string, number>} */
      const updates = {};
      if (cartChangeData.id) {
        updates[cartChangeData.id] = cartChangeData.quantity;
      } else if (updatedCartItem && !cartChangeData.line) {
        updates[updatedCartItem.key] = cartChangeData.quantity;
      }

      // console.log("update original test", updates);

      if (Object.keys(updates).length) {
        // Wait for 500 milliseconds before performing the fetch
        return new Promise((resolve) => {
          setTimeout(() => {
            // Update the add-on product quantities
            // We need to wait for the update to finish before resolving the original fetch
            originalFetch(`${shopifyRootRoute}cart/update.js`, {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ updates: updates }),
            })
              .then(function (response) {
                return response.json();
              })
              .then(function (/** @type {{ items: CartItem[] }} */ cartData) {
                resolve(cartData);
              })
              .catch(function (err) {
                console.error("forceRemoveAddon: ", err);
                resolve(updatedCartData); // Resolve with the original data in case of error
              });
          }, 500); // 500 milliseconds = .5 seconds
        });
      }
    }
    return Promise.resolve(updatedCartData); // Return a resolved promise with the updatedCartData if no updates are needed
  }

  /**
   * Removes add-on products from the cart if their base products no longer exist
   */
  function removeAddonIfBaseProductNotExists() {
    return originalFetch(`${shopifyRootRoute}cart.js`, {
      method: "GET",
      headers: { Accept: "application/json" },
    })
      .then((response) => response.json())
      .then((/** @type {{ items: CartItem[] }} */ cartData) => {
        // Find add-on items whose base products are no longer in the cart
        const removedBaseItems = cartData.items.filter((cartItem) => {
          const hasBaseProductId = cartItem.properties._base_product_id;
          const baseProductExists = cartData.items.some(
            (item) => item.properties._id === cartItem.properties._base_product_id
          );
          return hasBaseProductId && !baseProductExists;
        });

        if (removedBaseItems.length === 0) {
          return;
        }

        // Create updates object to remove the items
        const updates = /** @type {Record<string, number>} */ ({});
        removedBaseItems.forEach((removedItem) => {
          updates[removedItem.key] = 0;
        });

        // Remove the items from the cart
        originalFetch(`${shopifyRootRoute}cart/update.js`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ updates: updates }),
        }).then(() => {
          window.location.reload();
        });
      });
  }
})();
