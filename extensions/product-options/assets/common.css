.ef__product-option-root {
  --ef-foreground-color: var(--color-foreground, 0, 0, 0);
  --ef-background-color: var(--color-background, 255, 255, 255);
  --ef-border-width: var(--inputs-border-width, 1px);
  --ef-border-opacity: var(--inputs-border-opacity, 0.1);
  --ef-border-color: var(--colorBorder, 0, 0, 0);
}

/* Hide the caret on stores where the caret class doesn't exist. */
.ef__product-option-root .icon-caret {
  display: block;
  height: 0;
}

.ef__product-option-root .select__select {
  background-image: unset;
  appearance: auto;
  --webkit-appearance: auto;
}

.ef__product-option-root .form__label {
  font-size: 14px;
  font-weight: bold;
}

.ef__product-option-root .ef__option-description {
  font-size: 12px;
}

.ef__product-option-root .field:after {
  content: '';
  display: block;

  box-shadow: 0 0 0 var(--ef-border-width) rgba(var(--ef-border-color), var(--ef-border-opacity));

  position: absolute;
  top: var(--ef-border-width);
  right: var(--ef-border-width);
  bottom: var(--ef-border-width);
  left: var(--ef-border-width);
  border: 0.1rem solid transparent;
  pointer-events: none;
}

.ef__option-value-text-box:after,
.ef__option-value-number-field:after,
.ef__option-value-date-picker:after {
  width: 100%;
}

.ef__option-value-file-upload:after {
  box-shadow: unset;
}

.ef__product-option-root input:focus,
.ef__product-option-root input,
.ef__product-option-root textarea,
.ef__product-option-root textarea:focus {
  display: block;
  width: 100%;
  font-size: 14px;
  padding: 1rem !important;
  overflow: hidden;
}

.ef__product-option-root textarea {
  resize: vertical;
}

.ef__product-option-root input[type='radio'],
.ef__product-option-root input[type='checkbox'] {
  display: inline-block;
  position: relative;
  width: auto;
  height: auto;
  min-height: auto;
  clip: unset;
}

.ef__product-option-root input[type='radio'] + label,
.ef__product-option-root input[type='checkbox'] + label {
  display: inline-block;
}

label.ef__product-option-single-value-label {
  display: block;
  width: 100%;
  position: relative;
  height: auto;
  padding: 0;
  pointer-events: auto;
}

.ef__option-values-dropdown {
  padding: 0;
}

.ef__product-option-root select {
  width: 100%;
  padding: 1rem;
}

.ef__product-option-root select:focus-visible {
  outline: none;
}

.ef__character-counter {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  opacity: 0.4;
  font-size: 12px;
}

.ef__swatch-image,
.ef__swatch-color {
  --ef-color-foreground-selected: var(--color-foreground, 18, 18, 18);
  --ef-border-opacity-selected: var(--inputs-border-opacity, 1);
  --ef-radius-selected: var(--inputs-radius, 5px);
}

.ef__swatch-image,
.ef__swatch-color {
  display: block;
  box-sizing: content-box;
  width: 48px;
  height: 48px;
  padding: 0.5rem;
  margin-right: 0.8rem;
  border-radius: var(--ef-radius-selected);
}

.ef__swatch-input:checked + label {
  box-shadow: 0 0 0 2px rgba(var(--ef-color-foreground-selected), var(--ef-border-opacity-selected));
  border-radius: var(--ef-radius-selected);
}

/* Exclude border opacity for .ef__swatch-color */
.ef__swatch-color:checked + .ef__swatch-input:checked + label {
  box-shadow: 0 0 0 2px rgb(var(--ef-color-foreground-selected));
}

/* Enlarge on hover */

/* Enlarge label background */
.ef__swatch-image {
  transition: transform 0.3s ease; /* Smooth transformation */
}

/* Enlarge label background */
.ef__swatch-image[data-enlarge-on-hover='true']:hover {
  transform: scale(1.5); /* Adjust scaling factor to desired enlargement */
  font-size: 0.5em;
  z-index: 1;
}

.ef__swatch-image,
.ef__swatch-color {
  margin-top: 1rem;
  position: relative;
  display: inline-block; /* Adjust according to your layout */
  font-size: 0.8em;
  cursor: pointer;
}

.ef__swatch-image > .ef__hover-text,
.ef__swatch-color > .ef__hover-text {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(100%); /* Adjust to position below the image */
  text-align: center;
  white-space: nowrap; /* Prevents text wrapping */
  width: 100%; /* Adjust as needed */
  visibility: hidden;
  opacity: 0;
}

.ef__swatch-image:hover > .ef__hover-text,
.ef__swatch-color:hover > .ef__hover-text {
  visibility: visible;
  opacity: 1;
  transform: translateX(-50%) translateY(100%); /* Adjust to position below the image */
}

/* We need to change the opacity to 1 because it's hidden by default in some themes */
.ef__product-option-input.field__input::placeholder {
  opacity: 1;
}

/* File upload styles */

.ef__option-value-file-upload label {
  width: 100%;

  border-style: dashed;
  border: 1px dashed #7d7d7d;
  border-radius: 3px;
}

.ef__option-value-file-upload input[type='file'] {
  position: relative;
  height: 9rem;
  opacity: 0;
  z-index: 1;
}

.ef__option-value-file-upload input[type='button'] {
  background-color: #e5effd;
  color: #1f5199;
  font-weight: bold;
  line-height: normal;
  border: none;
  padding: 6px 10px;
  border-radius: 2px;
}

.ef__option-value-file-upload input[type='file']:not([disabled]),
.ef__option-value-file-upload input[type='file']:not([disabled])::-webkit-file-upload-button {
  cursor: pointer;
}

.ef__option-value-file-upload label > div {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ef__option-value-file-upload label > div:not(.ef__upload-content) > * {
  display: block;
  margin: 1rem auto 0 auto;
}

.ef__upload-content {
  width: 100%;
  display: flex;
  align-items: center;
  z-index: 2;
}

.ef__upload-content > img {
  width: 6rem;
  margin-left: 2rem;
  cursor: pointer;
}

.ef__upload-content > span {
  width: 28rem;
  margin: 2rem;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ef__upload-content > svg {
  margin-right: 2rem;
  cursor: pointer;
  z-index: 2;
}

/* Loading animation */
.ef__loading {
  display: block !important;
  width: 2rem;
  padding: 0.3rem;
  margin: auto;
  margin-top: 0.3rem;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #000;
  --_m: conic-gradient(#0000 10%, #000), linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
  mask: var(--_m);
  -webkit-mask-composite: source-out;
  mask-composite: subtract;
  animation: l3 1s infinite linear;
}

@keyframes l3 {
  to {
    transform: rotate(1turn);
  }
}

/* End of file upload styles */

/* Pill styles (copied and modified from `product-variant-picker.css` of the Dawn 13.0.1 theme */

.ef__product-option-root variant-selects {
  display: block;
}

.ef__product-option-root .product--no-media .product-form__input--pill,
.ef__product-option-root .product--no-media .product-form__input--swatch,
.ef__product-option-root .product--no-media .product-form__input--dropdown {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.ef__product-option-root .product--no-media .product-form__input.product-form__input--pill,
.ef__product-option-root .product--no-media .product-form__input.product-form__input--swatch {
  flex-wrap: wrap;
  margin: 0 auto 1.2rem auto;
}

.ef__product-option-root .product--no-media .product-form__input--dropdown {
  flex-direction: column;
  max-width: 100%;
}

.ef__product-option-root
  :is(.product-form__input--pill, .product-form__input--swatch)
  .form__label {
  margin-bottom: 0.2rem;
}

.ef__product-option-root .product-form__input input[type='radio'] {
  clip: rect(0, 0, 0, 0);
  overflow: hidden;
  position: absolute;
  height: 1px;
  width: 1px;
}

.ef__product-option-root
  .product-form__input
  input[type='radio']:not(.disabled)
  + label
  > .label-unavailable {
  display: none;
}

.ef__product-option-root .product-form__input--dropdown {
  --swatch-input--size: 2rem;
  margin-bottom: 1.6rem;
}

.ef__product-option-root .product-form__input--dropdown .dropdown-swatch + select {
  padding-left: calc(2.4rem + var(--swatch-input--size));
}

.ef__product-option-root .product-form__input--dropdown .dropdown-swatch {
  position: absolute;
  left: 1.6rem;
  top: calc(50% - var(--swatch-input--size) / 2);
  width: var(--swatch-input--size);
  height: var(--swatch-input--size);
  z-index: 1;
}

.ef__product-option-root .product-form__input--pill {
  --ef-pill-border-radius: var(--variant-pills-radius, 5px);
  --ef-pill-border-width: var(--variant-pills-border-width, 1px);
  --ef-pill-border-opacity: var(--variant-pills-border-opacity, 0.55);
}

/* Custom styles for Pill display type */
.ef__product-option-root .product-form__input--pill input[type='radio'] + label {
  border: var(--ef-pill-border-width) solid
    rgba(var(--ef-foreground-color), var(--ef-pill-border-opacity));
  background-color: rgb(var(--ef-background-color));
  color: rgba(var(--ef-foreground-color));
  border-radius: var(--ef-pill-border-radius);
  color: rgb(var(--ef-foreground-color));
  display: inline-block;
  margin: 0.7rem 0.5rem 0.2rem 0;
  padding: 1rem 2rem;
  font-size: 14px;
  line-height: 1;
  text-align: center;
  transition: border var(--duration-short) ease;
  cursor: pointer;
  position: relative;
}

.ef__product-option-root .product-form__input--pill input[type='radio'] + label:before {
  content: '';
  position: absolute;
  top: calc(var(--ef-pill-border-width) * -1);
  right: calc(var(--ef-pill-border-width) * -1);
  bottom: calc(var(--ef-pill-border-width) * -1);
  left: calc(var(--ef-pill-border-width) * -1);
  z-index: -1;
  border-radius: var(--ef-pill-border-radius);
  box-shadow: var(--variant-pills-shadow-horizontal-offset)
    var(--variant-pills-shadow-vertical-offset) var(--variant-pills-shadow-blur-radius)
    rgba(var(--color-shadow), var(--variant-pills-shadow-opacity));
}

.ef__product-option-root .product-form__input--pill input[type='radio'] + label:hover {
  border-color: rgb(var(--ef-foreground-color));
}

.ef__product-option-root .product-form__input--pill input[type='radio']:checked + label {
  background-color: rgb(var(--ef-foreground-color));
  color: rgb(var(--ef-background-color));
}

@media screen and (forced-colors: active) {
  .ef__product-option-root .product-form__input--pill input[type='radio']:checked + label {
    text-decoration: underline;
  }

  .ef__product-option-root .product-form__input--pill input[type='radio']:focus-visible + label {
    outline: transparent solid 1px;
    outline-offset: 2px;
  }
}

.ef__product-option-root .product-form__input--pill input[type='radio']:checked + label::selection {
  background-color: rgba(var(--ef-background-color), 0.3);
}

.ef__product-option-root .product-form__input--pill input[type='radio']:disabled + label,
.ef__product-option-root .product-form__input--pill input[type='radio'].disabled + label {
  border-color: rgba(var(---ef-foreground-color), 0.1);
  color: rgba(var(--ef-foreground-color), 0.6);
  text-decoration: line-through;
}

.ef__product-option-root .product-form__input--pill input[type='radio'].disabled:checked + label,
.ef__product-option-root .product-form__input--pill input[type='radio']:disabled:checked + label {
  color: rgba(var(--ef-background-color), 0.6);
}

.ef__product-option-root .product-form__input--pill input[type='radio']:focus-visible + label {
  box-shadow:
    0 0 0 0.3rem rgb(var(--ef-background-color)),
    0 0 0 0.5rem rgba(var(--ef-foreground-color), 0.55);
}

/* Fallback */
.ef__product-option-root .product-form__input--pill input[type='radio'].focused + label {
  box-shadow:
    0 0 0 0.3rem rgb(var(--ef-background-color)),
    0 0 0 0.5rem rgba(var(--ef-foreground-color), 0.55);
}

/* No outline when focus-visible is available in the browser */
.ef__product-option-root
  .no-js
  .product-form__input--pill
  input[type='radio']:focus:not(:focus-visible)
  + label {
  box-shadow: none;
}
/* End custom styles for Pill display type  */

/* Custom styles for Swatch display type */
.ef__product-option-root .product-form__input--swatch {
  display: flex;
  flex-wrap: wrap;
}

.ef__product-option-root .product-form__input--swatch .swatch-input__input + .swatch-input__label {
  margin: 0.7rem 1.2rem 0.2rem 0;
}

@media screen and (min-width: 750px) {
  .ef__product-option-root
    .product-form__input--swatch
    .swatch-input__input
    + .swatch-input__label {
    --swatch-input--size: 2.8rem;
  }
}
/* End custom styles for Swatch display type */

/* End of pill styles */
