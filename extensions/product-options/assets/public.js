"use strict";

// Warning: This script is blocking, so do *NOT* include here any code that's executed immadiately!

window.EasyFlowProductOptions = {
  objectToFormData: function (data, rootKey = "", formData = new FormData()) {
    const objectToFormData = window.EasyFlowProductOptions.objectToFormData;

    if (Array.isArray(data)) {
      data.forEach(function (element) {
        objectToFormData(element, `${rootKey}[]`, formData);
      });
    } else if (typeof data === "object" && data && !(data instanceof File)) {
      Object.keys(data).forEach(function (key) {
        if (rootKey === "") {
          objectToFormData(data[key], key, formData);
        } else {
          objectToFormData(data[key], `${rootKey}[${key}]`, formData);
        }
      });
    } else if (data !== null) {
      // Changed by rumi
      formData.append(rootKey, data);
    }

    return formData;
  },
  formDataToObject: function (formData, initialObject) {
    /** @type {FormDataObject} */
    const object = initialObject || {};

    Array.from(formData).forEach(function (entry) {
      const keys = entry[0].split("[").map((key) => key.replace(/\]$/, ""));
      const value = entry[1];

      /** @type {any} */
      let currentObject = object;

      /** @type {keyof typeof currentObject} */
      let previousKey = keys[0];

      // Build the object structure based on the keys
      for (let i = 1; i < keys.length; i++) {
        if (keys[i] === "" && !Array.isArray(currentObject[previousKey])) {
          // If the key is empty, it's an array
          currentObject[previousKey] = [];
        } else if (
          keys[i] !== "" &&
          (typeof currentObject[previousKey] !== "object" || Array.isArray(currentObject[previousKey]))
        ) {
          // Otherwise it's an object
          currentObject[previousKey] = {};
        }

        currentObject = currentObject[previousKey];
        previousKey = keys[i] === "" ? currentObject.length : keys[i];
      }

      // Assign the value to the object
      if (Array.isArray(currentObject)) {
        currentObject.push(value);
      } else {
        currentObject[keys[keys.length - 1]] = value;
      }
    });

    return object;
  },
  parseFetchBody: function (fetchArguments) {
    return new Promise(function (resolve) {
      if (fetchArguments[0] instanceof Request) {
        const clonedRequest = fetchArguments[0].clone();

        fetchArguments[0]
          .formData()
          .then(function (formData) {
            resolve(formData);
          })
          .catch(function () {
            clonedRequest.text().then(function (text) {
              resolve(text);
            });
          });
      } else {
        resolve(fetchArguments[1].body);
      }
    });
  },
};

window.EasyFlowHelpers = {
  reloadOptionsBlockInitialized: false,
  initialBlock: null,
  themeCartInfo: {
    cartType: document.getElementById("easyflow-cart-info")?.dataset.cartType || "page",
    cartUrl: document.getElementById("easyflow-cart-info")?.dataset.cartUrl || "/cart",
  },
  getOriginalOptionsBlock: function () {
    const optionRoot = document.querySelector(".ef__product-option-root");
    const originalOptionRootHTML = optionRoot ? optionRoot.outerHTML : null;
    return originalOptionRootHTML;
  },
  reloadOptionsBlock: function () {
    // public js run multiple times, so we need to block the initial load
    if (!window.EasyFlowHelpers.reloadOptionsBlockInitialized) {
      console.log("reloadOptionsBlockInitialized is false");
      return;
    }
    const html = window.EasyFlowHelpers.initialBlock;
    if (html) {
      const optionRoot = document.querySelector(".ef__product-option-root");
      if (optionRoot) {
        console.log("Reloading options block - replacing DOM");
        optionRoot.outerHTML = html;
      }
    }
  },
};

window.EasyFlowHelpers.initialBlock = window.EasyFlowHelpers.getOriginalOptionsBlock();
