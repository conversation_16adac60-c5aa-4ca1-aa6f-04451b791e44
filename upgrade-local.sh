#!/bin/bash

# Define your local packages
local_packages=("easyflow-enums")

# Define your workspace packages
workspace_packages=("web" "frontend") # "cloud/functions/shopifyPubSubWebhooks"

# Loop through each local package and update it in each workspace package
for workspace_package in "${workspace_packages[@]}"; do
  cd "$workspace_package"
  
  for local_package in "${local_packages[@]}"; do
    # lerna add "$local_package" --scope="$workspace_package"
    pnpm upgrade "$local_package";
    echo "✅ $local_package Upgraded"
  done
done