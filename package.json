{"private": true, "name": "product-options", "version": "1.0.0", "author": "2023 Projects", "main": "web/main.js", "license": "UNLICENSED", "scripts": {"start": "pnpm --filter product-options-web serve", "shopify": "shopify", "build": "env-cmd -f .env shopify app build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "deploy": "gcloud app deploy --project=szamlazz-hu-app", "shopify:deploy": "shopify app deploy", "lint": "eslint . --ext=.js,.jsx --fix", "getOrder": "env-cmd -f .env.production node web/scripts/getOrder.js", "getSubscription": "env-cmd -f .env.production node web/scripts/getSubscription.js", "fixSubscriptions": "env-cmd -f .env.production node web/scripts/fixSubscriptions.js", "collectEmails": "env-cmd -f .env.production node web/scripts/collectEmails.js", "getMetafield": "env-cmd -f .env.production node web/scripts/getMetafield.js", "updateDefinitions": "env-cmd -f .env node web/scripts/updateDefinitions.js", "setExistingShopsProperty": "env-cmd -f .env.production node web/scripts/setExistingShopsProperty.js", "watermarkFreeUsers": "env-cmd -f .env.production node web/scripts/watermarkFreeUsers.js", "updateWatermarks": "env-cmd -f .env.production node web/scripts/updateWatermarks.js", "registerMissingWebhooks": "env-cmd -f .env.production node web/scripts/registerMissingWebhooks.js", "unregisterUnnecessaryWebhooks": "env-cmd -f .env.production node web/scripts/unregisterUnnecessaryWebhooks.js", "updateAllWebhooks": "env-cmd -f .env node web/scripts/updateWebhooksForAllShops.js", "cancelShopSubscription": "env-cmd -f .env.production node web/scripts/cancelSubscriptions.js", "updateAllShopInfo": "env-cmd -f .env.production node web/scripts/updateAllShopInfo.js", "updateFluentCRMForExistingShops": "env-cmd -f .env.production node web/scripts/updateFluentCRMForExistingShops.js", "updateAllShopStatus": "env-cmd -f .env.production node web/scripts/updateAllShopStatus.js", "staging:start": "pnpm --filter product-options-web staging-serve", "staging:deploy": "env-cmd -f .env.staging -- gcloud app deploy staging.yaml --project=szamlazz-hu-app", "db:seed": "env-cmd -f .env.production node web/prisma/seeders/index.js", "scriptForWebhookTable": "env-cmd -f .env.production node web/scripts/scriptForWebhookTable.js", "register:missing-webhooks": "env-cmd -f .env.production node web/scripts/registerMissingWebhooksForAllShops.js", "updateShopSubscriptionData": "env-cmd -f .env.production node web/scripts/updateShopSubscriptionData.js", "updateAppSubscriptionTrail": "env-cmd -f .env.production node web/scripts/updateAppSubscriptionTrail.js"}, "dependencies": {"env-cmd": "^10.1.0", "easyflow-enums": "file:web/packages/easyflow-enums"}, "devDependencies": {"@babel/eslint-parser": "^7.24.7", "@babel/preset-react": "^7.24.7", "@shopify/app": "^3.58.2", "@shopify/cli": "3.67.1", "@shopify/theme": "^3.58.2", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.3.1"}}