# Installation Guide

## Requirements
- **Node.js**: Ensure you have Node.js installed on your machine.
- **Shopify Partner Account**: Create a Shopify partner account if you don't have one.
- **Development Store**: Set up a development store or use a Shopify Plus sandbox store.

### Steps

##### 1. Clone the Repository
   Clone the Shopify app repository to your local environment.
```shell
git clone <your-repo-url>
```

##### 2. Environment Configuration
Copy the base environment configuration file and rename it for development.

```shell
cp .env.development.base .env.development
```

##### 3. Install Root Packages
Install required packages for the root using `pnpm`.

```shell
pnpm install
```

##### 4. Install Backend Packages
Move to web folder and install required packages for the backend using `pnpm`.

```shell
cd web && pnpm install && cd ../
```

##### 5. Install Frontend Packages
Move to frontend folder under web and install required packages for the frontend using `pnpm`.

```shell
cd web/frontend && pnpm install && cd ../../
```

##### 6. Google Cloud Setup
Set up your Google Cloud project to enable database and logging services.

  1. **Create a Google Cloud Project**

        Create a new project in Google Cloud.


  2. **Create Datastore Database**

        In Google Cloud, create a database in Datastore.


  3. **Login to Google Cloud**

     Use the Google Cloud CLI to log in.
  ```shell
  gcloud auth login
  ```
or
  ```shell
  gcloud auth application-default login
  ```
then set your Project ID to gcloud config
 ```shell
  gcloud config set project <your-google-cloud-project-id>
  ```


  4. **Set Environment Variables**

     Add the following to your `.env.development` file:

        - Google Project ID
        - Database Namespace

  ```
  GOOGLE_CLOUD_PROJECT_ID=<your-google-cloud-project-id>
  DATASTORE_NAMESPACE=product-options-dev
  ```
  5. **Optional: Configure Logging**

     If you wish to enable logging with `Mixpanel` or `Sentry`, add these configurations to your `.env.development` file.

        - **Mixpanel**
     
            To enable Mixpanel logging, set the following environment variables. 
         
             **Note**: If enabled, the GCP Secret Manager API must be enabled, and an API key must be generated.
            ````
             MIXPANEL_ENABLED=false
             MIX_PANEL_API_KEY=productOptionsMixpanelAPIKey
           ````

       - **Sentry**

         For Sentry logging, set the following variables. Similarly, the GCP Secret Manager API needs to be enabled and an API key generated.
          ````
         SENTRY_ENABLED=false
          SENTRY_DSN=productOptionsSentryDSN
         ````
##### 7. Add Index to DataStore

To add index to datastore run the following command:
  ```` shell
  gcloud datastore indexes create index.yaml
  ````
Note: we can see or validate current indexes by run the following command:
  ```` shell
  gcloud datastore indexes list
  ````

##### 8. Start Development Server 

  To start the development server:

  If a Shopify app is already created and connected, use:
  ```` shell
  pnpm run dev
  ````
 If not, refer to the [Shopify app creation guide](https://www.shopify.com/partners/blog/how-to-build-a-shopify-app) in the Shopify documentation.


## Additional Resources

 - **Shopify CLI**: To manage app configurations and environments.
 - **Google Cloud Documentation**: For creating projects, configuring databases, and managing secrets.
