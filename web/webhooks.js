import { PropertyFilter } from "@google-cloud/datastore";
import { DeliveryMethod } from "@shopify/shopify-api";

// eslint-disable-next-line import/no-extraneous-dependencies
import { shopStatus } from "easyflow-enums";
import lodash from "lodash";
import { subscriptionUpdate } from "./api/services/subscription.service.js";
import { captureException } from "./utilities/analytics.js";
import datastore, {
  getShopProperties,
  optionFromStored,
  optionToStored,
  setShopProperties,
} from "./utilities/datastore.js";
import { createOrUpdateFluentCRMContact, FLUENT_CRM_CONFIGS, FLUENT_CRM_TAGS } from "./utilities/fluentCrm.js";
import {
  setCollectionRules,
  updateGlobalOptionsMetafield,
  updateMetafields,
  updateMetaobjects,
} from "./utilities/productOptions.js";
import { addonProductType, handleSubscriptionChange, loadSession } from "./utilities/shopify.js";

const { get } = lodash;

/**
 * @typedef {Exclude<
 *   IProductOption['values'][number]['addonProduct'], undefined>
 * } AddonProduct
 */

/**
 * @type {import("@shopify/shopify-app-express").WebhookHandlersParam}
 */
export default {
  APP_SUBSCRIPTIONS_UPDATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop, body) => {
      try {
        const { app_subscription: appSubscription } = JSON.parse(body);
        const { status, name: planSlug } = appSubscription;
        await handleSubscriptionChange(status, shop);

        // Call async function without await to return webhook response immediately
        (async () => {
          const shopProperties = await getShopProperties(shop);
          if (shopProperties && planSlug) {
            await subscriptionUpdate(shop, shopProperties, appSubscription);
          }
          if (FLUENT_CRM_CONFIGS.isEnabled) {
            createOrUpdateFluentCRMContact(
              { ...shopProperties, ...{ isExtensionEnabled: false } },
              FLUENT_CRM_TAGS.SUBSCRIPTION_UPDATE,
              { status }
            );
          }
        })();
      } catch (error) {
        captureException(error, shop, JSON.parse(body));
      }
    },
  },

  APP_UNINSTALLED: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop) => {
      try {
        // Call async function without await to return webhook response immediately
        (async () => {
          await handleSubscriptionChange("CANCELLED", shop, false);

          const shopKey = datastore.key(["Shop", shop]);

          /** @type {GetResponse<IShop>} */
          const [shopProperties] = await datastore.get(shopKey);

          await setShopProperties(
            { isExtensionEnabled: false, status: shopStatus.UNINSTALLED },
            shopProperties,
            shop,
            true
          );
          if (FLUENT_CRM_CONFIGS.isEnabled) {
            createOrUpdateFluentCRMContact(
              {
                ...shopProperties,
                ...{ isExtensionEnabled: false, status: shopStatus.UNINSTALLED },
              },
              FLUENT_CRM_TAGS.UNINSTALLED
            );
          }
        })();
      } catch (error) {
        captureException(error, shop);
      }
    },
  },

  PRODUCTS_UPDATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop, body) => {
      /** @type {import('./types/product').Product} */
      const { variants, id: productId } = JSON.parse(body);

      try {
        // Call async function without await to return webhook response immediately
        (async () => {
          /** @type {QueryResponse<IProductOption>} */
          const [optionsWithAddon] = await datastore
            .createQuery("Option")
            .hasAncestor(datastore.key(["Shop", shop]))
            .filter(new PropertyFilter("values.addonProduct.id", "=", `gid://shopify/Product/${productId}`))
            .run();

          // Find all options that have values with modified addon products
          const changedOptions = optionsWithAddon.flatMap((option) => {
            const changedVariants = option.values.map(
              (value) =>
                variants?.find(({ id }) => `gid://shopify/ProductVariant/${id}` === value.addonProduct?.variantId) ||
                (value.addonProduct?.id === `gid://shopify/ProductVariant/${productId}`
                  ? /** @type {const} */ ("deleted")
                  : undefined)
            );

            const isVariantsUpdated = changedVariants.some(
              (variant, index) =>
                variant === "deleted" || (variant && Number(variant.price) !== option.values[index]?.price)
            );

            return isVariantsUpdated
              ? optionFromStored(
                  {
                    ...option,
                    values: option.values.map((value, index) => {
                      const variant = changedVariants[index];

                      if (variant === "deleted") {
                        const { addonProduct, addonType, ...newValue } = value;
                        return newValue;
                      }

                      const updatedPrice = variant?.price ? Number(variant?.price) : undefined;

                      return {
                        ...value,
                        price: variant && !Number.isNaN(updatedPrice) ? updatedPrice : value.price,
                      };
                    }),
                  },
                  shop
                ) || []
              : [];
          });

          if (!changedOptions.length) {
            return;
          }

          const session = await loadSession(shop, "handling addon product change");
          if (!session) {
            return;
          }

          // Update metaobjects
          const optionsWithMetaobjectId = await updateMetaobjects(changedOptions, optionsWithAddon, [], session);

          const changedOptionsAndSets = {
            update: { optionSets: [], options: optionsWithMetaobjectId },
          };

          await Promise.all([
            // Update metafield for the options that are added automatically to the products
            updateGlobalOptionsMetafield(changedOptionsAndSets, session),
            // Update metafields of the changed products
            updateMetafields(
              optionsWithMetaobjectId.flatMap(({ productIds }) => productIds),
              changedOptionsAndSets,
              session
            ),
          ]);

          // Store changed options
          const updatedOptions = optionsWithMetaobjectId.map((option) => ({
            key: datastore.key(["Shop", session.shop, "Option", option.id]),
            data: optionToStored(option),
          }));

          await datastore.update(updatedOptions);
        })();
      } catch (error) {
        captureException(error, shop, JSON.parse(body));
      }
    },
  },

  COLLECTIONS_UPDATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop, body) => {
      /** @type {import('./types/collection').Collection} */
      const { id, disjunctive, rules } = JSON.parse(body);

      try {
        // Do not proceed because it's a manual collection
        if (disjunctive === undefined) {
          return;
        }

        const hasAddonProductType = rules?.some((rule) => rule.condition === addonProductType);

        // If it's a disjunctive collection, we need to remove the rule if it exists
        if (disjunctive === true && !hasAddonProductType) {
          return;
        }

        // If it's a conjunctive collection, we need to add the rule if it doesn't exist yet
        if (disjunctive === false && hasAddonProductType) {
          return;
        }

        // Call async function without await to return webhook response immediately
        (async () => {
          const session = await loadSession(shop, "setting collection condition");
          if (!session) {
            return;
          }

          await setCollectionRules(
            [
              {
                id: `gid://shopify/Collection/${id}`,
                ruleSet: {
                  appliedDisjunctively: disjunctive,
                  rules:
                    rules?.map((rule) => ({
                      column: rule.column.toUpperCase(),
                      condition: rule.condition,
                      relation: rule.relation.toUpperCase(),
                    })) || [],
                },
              },
            ],
            session
          );
        })();
      } catch (error) {
        captureException(error, shop, JSON.parse(body));
      }
    },
  },

  /**
   * shop update webhook
   */
  SHOP_UPDATE: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop, body) => {
      try {
        // Call async function without await to return webhook response immediately
        (async () => {
          const session = await loadSession(shop, "get session for shop update webhook");
          if (!session) {
            return;
          }

          const payload = JSON.parse(body);
          const shopKey = datastore.key(["Shop", shop]);

          /** @type {GetResponse<IShop>} */
          const [shopProperties] = await datastore.get(shopKey);

          const inActivePlans = ["cancelled", "Inactive", "frozen"];

          const updatedShopData = {
            name: get(payload, "name"),
            email: get(payload, "email"),
            phone: get(payload, "phone"),
          };

          if (updatedShopData && Object.keys(updatedShopData).length > 0) {
            await setShopProperties(updatedShopData, shopProperties, session?.shop, true);

            if (inActivePlans.includes(payload?.plan_name || "")) {
              if (FLUENT_CRM_CONFIGS.isEnabled) {
                createOrUpdateFluentCRMContact(
                  {
                    ...{ ...shopProperties, ...updatedShopData },
                    ...{ isExtensionEnabled: false, status: shopStatus.UNINSTALLED },
                  },
                  FLUENT_CRM_TAGS.UNINSTALLED
                );
              }
            }
          }
        })();
      } catch (error) {
        captureException(error, shop, JSON.parse(body));
      }
    },
  },

  /**
   * Customers can request their data from a store owner. When this happens,
   * Shopify invokes this webhook.
   *
   * https://shopify.dev/docs/apps/webhooks/configuration/mandatory-webhooks#customers-data_request
   */
  CUSTOMERS_DATA_REQUEST: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async () => {
      // No customer data is stored, so nothing to do here.
    },
  },

  /**
   * Store owners can request that data is deleted on behalf of a customer. When
   * this happens, Shopify invokes this webhook.
   *
   * https://shopify.dev/docs/apps/webhooks/configuration/mandatory-webhooks#customers-redact
   */
  CUSTOMERS_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async () => {
      // No customer data is stored, so nothing to do here.
    },
  },

  /**
   * 48 hours after a store owner uninstalls your app, Shopify invokes this
   * webhook.
   *
   * https://shopify.dev/docs/apps/webhooks/configuration/mandatory-webhooks#shop-redact
   */
  SHOP_REDACT: {
    deliveryMethod: DeliveryMethod.Http,
    callbackUrl: "/api/webhooks",
    callback: async (_, shop) => {
      try {
        // Call async function without await to return webhook response immediately
        (async () => {
          // We don't delete the options and option sets, because the metaobjects and definitions are
          // not deleted by shopify, and so it would cause inconsistencies if the app is reinstalled

          /** @type {QueryResponse<{}>} */
          const [sessionRecords] = await datastore
            .createQuery("Session")
            .filter(new PropertyFilter("shop", "=", shop))
            .select("__key__")
            .run();

          await datastore.delete([
            // Shop properties
            datastore.key(["Shop", shop]),
            // Sessions
            ...sessionRecords.map((record) => record[datastore.KEY]),
            // Styling preferences
            datastore.key(["Shop", shop, "Styles", "default"]),
          ]);
        })();
      } catch (error) {
        captureException(error, shop);
      }
    },
  },
};
