generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["strictUndefinedChecks"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model coupon {
  id                      BigInt               @id @default(autoincrement())
  name                    String               @db.VarChar(255)
  code                    String               @unique @db.VarChar(255)
  discount_type           String               @db.VarChar(100)
  amount                  Float                @default(0.00)
  discount_limit_duration Int?
  start_date              DateTime
  end_date                DateTime
  max_limit               Int                  @default(9999)
  redeem_count            Int                  @default(0)
  plans                   Json                 @default("[]")
  status                  Boolean              @default(true)
  created_at              DateTime             @default(now())
  updated_at              DateTime             @updatedAt
  subscription_plans      subscription_plans[]
}

model subscription_plans {
  id                        BigInt                      @id @default(autoincrement())
  name                      String                      @db.VarChar(255)
  slug                      String                      @unique @db.VarChar(255)
  status                    Boolean                     @default(true)
  type                      String                      @db.VarChar(100)
  interval                  String                      @db.VarChar(100)
  package_info              Json                        @default("[]")
  meta                      Json                        @default("{}")
  trial_days                Int                         @default(0)
  currency                  String                      @default("USD") @db.VarChar(100)
  coupon_id                 BigInt?
  test                      Boolean                     @default(false)
  price                     Float                       @default(0.00)
  discount_type             String?                     @db.VarChar(100)
  discount                  Float                       @default(0.00)
  final_price               Float                       @default(0.00)
  created_at                DateTime                    @default(now())
  updated_at                DateTime                    @updatedAt
  coupon                    coupon?                     @relation(fields: [coupon_id], references: [id])
  subscription_transactions subscription_transactions[]
}

model shop_webhooks {
  id              BigInt   @id @default(autoincrement())
  shop_domain     String   @db.VarChar(255)
  wh_subs_id      String   @unique
  topic           String   @db.VarChar(100)
  delivery_method String   @db.VarChar(50)
  response        Json?
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt
}

model subscription_transactions {
  id                       BigInt             @id @default(autoincrement())
  shop_domain              String             @db.VarChar(255)
  plan_slug                String             @db.VarChar(255)
  name                     String             @db.VarChar(255)
  slug                     String             @db.VarChar(255)
  status                   Boolean            @default(true)
  type                     String             @default("free") @db.VarChar(100)
  interval                 String             @default("monthly") @db.VarChar(100)
  transaction_type         String             @default("new") @db.VarChar(255)
  transaction_narration    String             @default("new subscription") @db.VarChar(255)
  plan_data                Json
  requested_data           Json
  subscription_expire_date DateTime
  is_paid                  Boolean            @default(false)
  trial_days               Int                @default(0)
  trial_ends_on            DateTime?
  currency                 String             @default("USD") @db.VarChar(100)
  test                     Boolean            @default(false)
  price                    Float              @default(0.00)
  discount_type            String?            @db.VarChar(100)
  discount                 Float              @default(0.00)
  final_price              Float              @default(0.00)
  coupon_code              String?            @db.VarChar(255)
  created_at               DateTime           @default(now())
  updated_at               DateTime           @updatedAt
  meta                     Json?
  plan                     subscription_plans @relation(fields: [plan_slug], references: [slug])
}

model coupons_applied {
  id                  BigInt    @id @default(autoincrement())
  shop_domain         String    @db.VarChar(255)
  app_subscription_id String?   @db.VarChar(255)
  name                String    @db.VarChar(255)
  code                String    @db.VarChar(255)
  discount_type       String    @db.VarChar(100)
  amount              Float     @default(0.00)
  status              Boolean   @default(true)
  created_at          DateTime? @default(now())
  updated_at          DateTime? @updatedAt
}

model subscription_usage {
  id                   BigInt    @id @default(autoincrement())
  shop_domain          String    @db.VarChar(255)
  plan_slug            String    @db.VarChar(255)
  subscription_id      String    @db.VarChar(255)
  subscription_line_id String    @db.VarChar(255)
  description          String    @db.VarChar(255)
  key                  String    @default(dbgenerated("gen_random_uuid()"))
  price                Float     @default(0.00)
  meta                 Json?     @default("{}")
  occurrence           Int       @default(1)
  created_at           DateTime? @default(now())
  updated_at           DateTime? @updatedAt
}
