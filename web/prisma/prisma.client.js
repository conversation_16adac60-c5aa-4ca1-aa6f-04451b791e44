// eslint-disable-next-line import/no-extraneous-dependencies
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient().$extends({
  result: {
    subscription_plans: {
      id: {
        // @ts-ignore
        compute(plan) {
          return Number(plan.id);
        },
      },
      coupon_id: {
        // @ts-ignore
        compute(plan) {
          return Number(plan.coupon_id);
        },
      },
    },
    subscription_transactions: {
      id: {
        // @ts-ignore
        compute(subTrans) {
          return Number(subTrans.id);
        },
      },
    },
    coupon: {
      id: {
        // @ts-ignore
        compute(coupon) {
          return Number(coupon.id);
        },
      },
    },
    coupons_applied: {
      id: {
        // @ts-ignore
        compute(coupon) {
          return Number(coupon.id);
        },
      },
    },
  },
});

export default prisma;
