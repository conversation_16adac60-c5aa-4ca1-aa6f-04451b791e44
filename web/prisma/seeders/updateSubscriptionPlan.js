// eslint-disable-next-line import/no-extraneous-dependencies
import { discountTypes, subscriptionIntervals, subscriptionTypes } from "easyflow-enums";
import prisma from "../prisma.client.js";

const getPlanPackageInfo = [
  {
    slug: `pro-${subscriptionIntervals.MONTHLY}`,
    benefits: [
      {
        name: "all_option_types",
        display_name: "All option types",
        value: true,
      },
      {
        display_name: "Unlimited variant options",
        value: true,
        name: "unlimited_variant_options",
      },
      {
        display_name: "Conditional logic (including  Shopify variants) ",
        value: true,
        name: "conditional_logic_including_shopify_variants",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
      },
      {
        display_name: "File upload",
        value: true,
        name: "file_upload",
      },
      {
        display_name: "Price add-ons (multi-currency support)",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },
  {
    slug: `pro-${subscriptionIntervals.YEARLY}`,
    benefits: [
      {
        display_name: "All option types",
        name: "all_option_types",
        value: true,
      },
      {
        display_name: "Unlimited variant options",
        value: true,
        name: "unlimited_variant_options",
      },
      {
        display_name: "Conditional logic (including  Shopify variants) ",
        value: true,
        name: "conditional_logic_including_shopify_variants",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
      },
      {
        display_name: "File upload",
        value: true,
        name: "file_upload",
      },
      {
        display_name: "Price add-ons (multi-currency support)",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },

  {
    slug: `visionary-${subscriptionIntervals.USAGE}`,
    benefits: [
      {
        display_name: "All option types (except file upload)",
        value: true,
        name: "all_option_types_without_file_upload",
      },
      {
        display_name: "Unlimited variants options",
        value: true,
        name: "unlimited_variants_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Unlimited option sets",
        value: true,
        name: "unlimited_option_sets",
      },
      {
        display_name: "File upload",
        value: true,
        name: "file_upload",
      },
      {
        display_name: "Price add-ons, multi-currency support",
        value: true,
        name: "price_Add-ons_multi_currency_support",
      },
      {
        display_name: "Conditional logic – Shopify Variants",
        value: true,
        name: "conditional_logic_shopify_variants",
      },
      {
        display_name: "Priority support",
        value: true,
        name: "priority_support",
      },
    ],
  },
  {
    slug: `free-${subscriptionIntervals.LIFETIME}`,
    benefits: [
      {
        display_name: "All option types (except file upload)",
        value: true,
        name: "all_option_types_without_file_upload",
      },
      {
        display_name: "Unlimited variant options",
        value: true,
        name: "unlimited_variant_options",
      },
      {
        display_name: "Conditional logic",
        value: true,
        name: "conditional_logic",
      },
      {
        display_name: "Bulk edit/apply",
        value: true,
        name: "bulk_edit_apply",
      },
      {
        display_name: "Remove watermark",
        value: true,
        name: "remove_watermark",
      },
      {
        display_name: "Live chat",
        value: true,
        name: "live_chat",
      },
      {
        display_name: "5 option sets",
        value: true,
        name: "5_option_sets",
      },
      {
        display_name: "Price add-ons (multi-currency support)",
        value: false,
        name: "Price_add_ons_with_multi-currency support",
      },
      {
        display_name: "File upload",
        value: false,
        name: "file_upload",
      },
      {
        display_name: "Priority support",
        value: false,
        name: "priority_support",
      },
    ],
  },
];

const planMetaData = [
  {
    slug: `free-${subscriptionIntervals.LIFETIME}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `pro-${subscriptionIntervals.MONTHLY}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `pro-${subscriptionIntervals.YEARLY}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "save 20% on yearly", // individual plan save description if any
      capped_amount: null,
      terms: "",
      duration: 1, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
  {
    slug: `visionary-${subscriptionIntervals.USAGE}`,
    meta: {
      description: "", // individual plan description if any
      price_save_description: "save 50% on visionary", // individual plan save description if any
      capped_amount: { amount: 20.0, currencyCode: "USD" },
      terms: "",
      duration: 18, // charge by month count
      coupon_code: null, // if any coupon applied
    },
  },
];

const plans = [
  {
    name: "Free",
    slug: `free-${subscriptionIntervals.LIFETIME}`,
    status: true,
    type: subscriptionTypes.FREE,
    interval: subscriptionIntervals.LIFETIME,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `free-${subscriptionIntervals.LIFETIME}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `free-${subscriptionIntervals.LIFETIME}`)?.meta,
    trial_days: 0,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 0.0,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 0.0,
  },
  {
    name: "Pro",
    slug: `pro-${subscriptionIntervals.MONTHLY}`,
    status: true,
    type: subscriptionTypes.PRO,
    interval: subscriptionIntervals.MONTHLY,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `pro-${subscriptionIntervals.MONTHLY}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `pro-${subscriptionIntervals.MONTHLY}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 9.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 9.99,
  },
  {
    name: "Pro - yearly",
    slug: `pro-${subscriptionIntervals.YEARLY}`,
    status: true,
    type: subscriptionTypes.PRO,
    interval: subscriptionIntervals.YEARLY,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `pro-${subscriptionIntervals.YEARLY}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `pro-${subscriptionIntervals.YEARLY}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 99.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 99.99,
  },
  {
    name: "Visionary",
    slug: `visionary-${subscriptionIntervals.USAGE}`,
    status: false,
    type: subscriptionTypes.PRO,
    interval: subscriptionIntervals.USAGE,
    package_info: getPlanPackageInfo.find((plan) => plan.slug === `visionary-${subscriptionIntervals.USAGE}`)?.benefits,
    meta: planMetaData.find((plan) => plan.slug === `visionary-${subscriptionIntervals.USAGE}`)?.meta,
    trial_days: 7,
    currency: "USD",
    coupon_id: null,
    test: false,
    price: 2.99,
    discount_type: discountTypes.AMOUNT,
    discount: 0.0,
    final_price: 53.82,
  },
];

const seed = async () => {
  try {
    plans.map(async (plan) => {
      await prisma.subscription_plans.upsert({
        where: {
          slug: plan?.slug,
        },
        update: {
          ...plan,
        },
        create: {
          ...plan,
        },
      });
    });

    // Replace console.log with a logging utility if available
    console.log("All plans have been updated successfully.");
  } catch (error) {
    console.error("Error seeding plans:", error);
  }
};

export default seed;
