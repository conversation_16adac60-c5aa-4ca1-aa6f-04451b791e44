// eslint-disable-next-line import/no-extraneous-dependencies
import { discountTypes } from "easyflow-enums";
import prisma from "../prisma.client.js";

const coupons = [
  {
    name: "10$ off",
    code: "coupon-amount-10",
    discount_type: discountTypes.AMOUNT,
    amount: parseFloat(10),
    discount_limit_duration: 12,
    start_date: new Date(),
    end_date: new Date("2026-12-31"),
    max_limit: 100,
    plans: ["pro-yearly", "pro-monthly"],
    status: true,
    redeem_count: 0,
  },
  {
    name: "10% off",
    code: "coupon-percent-10",
    discount_type: discountTypes.PERCENT,
    amount: parseFloat(10),
    discount_limit_duration: 12,
    start_date: new Date(),
    end_date: new Date("2026-12-31"),
    max_limit: 100,
    plans: ["pro-yearly", "pro-monthly"],
    status: true,
    redeem_count: 0,
  },
  {
    name: "100% off",
    code: "coupon-percent-100",
    discount_type: discountTypes.PERCENT,
    amount: parseFloat(100),
    discount_limit_duration: 12,
    start_date: new Date(),
    end_date: new Date("2026-12-31"),
    max_limit: 100,
    plans: ["pro-yearly", "pro-monthly"],
    status: true,
    redeem_count: 0,
  },
];

const seed = async () => {
  try {
    coupons.map(async (coup) => {
      await prisma.coupon.create({
        // @ts-ignore
        data: {
          ...coup,
        },
      });
    });

    // Replace console.log with a logging utility if available
    console.log("All coupon have been seeded successfully.");
  } catch (error) {
    console.error("Error seeding coupon:", error);
  }
};

export default seed;
