/* eslint-disable import/no-extraneous-dependencies */
const styleConfig = require('eslint-config-airbnb/rules/react');
const a11yConfig = require('eslint-config-airbnb/rules/react-a11y');

module.exports = {
  env: {
    browser: true,
  },
  rules: {
    'import/extensions': ['error', 'ignorePackages', { js: 'never', mjs: 'never', jsx: 'never' }],

    ...copyRuleWith(
      'no-underscore-dangle',
      'allow',
      (allowedNames) => [
        ...allowedNames,
        '__vite_plugin_react_preamble_installed__',
        '__SHOPIFY_DEV_HOST',
      ],
      styleConfig,
    ),

    // The `Link` component from Polaris uses the `url` property instead of `href`
    ...copyRuleWith(
      'jsx-a11y/anchor-is-valid',
      'specialLink',
      (linkProperties) => [...linkProperties, 'url'],
      a11yConfig,
    ),
  },
};

/**
 * @template TOption
 *
 * @param {string} ruleName
 * @param {string} optionName
 * @param {(oldValue: TOption) => TOption} changeOption
 * @param {any} config
 */
function copyRuleWith(ruleName, optionName, changeOption, config) {
  const [state, options] = config.rules[ruleName];

  const newOptions = { ...options, [optionName]: changeOption(options[optionName]) };

  return { [ruleName]: [state, newOptions] };
}
