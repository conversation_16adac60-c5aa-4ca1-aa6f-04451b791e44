import { Crisp } from "crisp-sdk-web";
import { useCallback, useEffect, useMemo, useState } from "react";

import lodash from "lodash";
// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
// eslint-disable-next-line import/no-relative-packages
import { defaultFreePlanMaxOptionSets as freePlanMaxOptionSets } from "../defaults";
import EditOptionPage from "./pages/EditOptionPage";
import EditOptionSetPage from "./pages/EditOptionSetPage";
import HomePage from "./pages/HomePage";
import OnboardingPage from "./pages/OnboardingPage";
import PricingPage from "./pages/PricingPage";
import SettingsPage from "./pages/SettingsPage";
import { useAppQuery } from "./utilities/hooks";


const { get } = lodash;

export default function App() {
  const shopify = useAppBridge();
  const [isInitialized, setInitialized] = useState(false);

  const [isOnboardingInProgress, setOnboardingInProgress] = useState(false);
  const [isEnableExtensionDismissed, setEnableExtensionDismissed] = useState(false);

  const [optionSets, setOptionSets] = useState(/** @type {IProductOptionSet[]} */ ([]));
  const [options, setOptions] = useState(/** @type {IProductOption[]} */ ([]));

  /** @type {TypedStateOptional<Partial<IProductOptionSet>>} */
  const [editedOptionSet, setEditedOptionSet] = useState();

  /** @type {TypedStateOptional<Partial<IProductOption>>} */
  const [editedOption, setEditedOption] = useState();

  /** @type {TypedStateOptional<URLSearchParams>} */
  const [optionCursorParameters, setOptionCursorParameters] = useState();

  useEffect(() => {
    Crisp.configure("752f604b-932d-4822-a645-b41f4fdd6ee3", { autoload: false });
  }, []);

  const checkUpdates = useCallback(async (/** @type {IShopDetails} */ newShopDetails) => {
    if (localStorage.getItem("shopDetails") !== JSON.stringify(newShopDetails)) {
      const response = await fetch("/api/store/update", { method: "POST" });

      if (response.ok) {
        localStorage.setItem("shopDetails", JSON.stringify(newShopDetails));
      }
    }
  }, []);

  const isPricingPage = window.location.pathname.endsWith("/pricing");
  const isCustomizationPage = window.location.pathname.endsWith("/customization");
  const isSettingsPage = window.location.pathname.endsWith("/settings");

  /** @type {UseQueryResult<OptionItems>} */
  const { isLoading: isOptionsLoading, data: optionsData } = useAppQuery(
    !optionCursorParameters ? "/api/options/get" : `/api/options/get?${optionCursorParameters}`,
    { reactQueryOptions: { enabled: !isPricingPage && !isCustomizationPage && !isSettingsPage } }
  );

  useEffect(() => {
    if (!optionsData) {
      return;
    }

    setInitialized(true);

    const { optionSets: newOptionSets = [], options: newOptions = [] } = optionsData;

    setOptionSets((oldOptionSets) => [...oldOptionSets, ...newOptionSets]);
    setOptions((oldOptions) => [...oldOptions, ...newOptions]);

    if (optionsData.optionSetsCursor || optionsData.optionsCursor) {
      const cursorParameters = new URLSearchParams();

      if (optionsData.optionSetsCursor) {
        cursorParameters.append("optionSetsCursor", optionsData.optionSetsCursor);
      }

      if (optionsData.optionsCursor) {
        cursorParameters.append("optionsCursor", optionsData.optionsCursor);
      }

      setOptionCursorParameters(cursorParameters);
    }
  }, [optionsData]);

  const chargeId = new URLSearchParams(window.location.search).get("charge_id");
  const planSlugQueryString = new URLSearchParams(window.location.search).get("plan");
  useEffect(() => {
    if (chargeId && planSlugQueryString) {
      shopify.toast.show("Subscription successful");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initialShopDetails = useMemo(
    () => JSON.parse(document.getElementById("ef__shop-details")?.textContent || "{}"),
    []
  );

  useEffect(() => {
    checkUpdates(initialShopDetails);
  }, [checkUpdates, initialShopDetails]);

  /** @type {UseQueryResult<IShopDetails>} */
  const { data: shopDetails, refetch: refetchShopDetails } = useAppQuery(
    chargeId ? `/api/shop/details?chargeId=${chargeId}` : "/api/shop/details",
    {
      reactQueryOptions: {
        enabled: !isCustomizationPage && !isSettingsPage,
        initialData: initialShopDetails,
        staleTime: 1000,
      },
    }
  );

  useEffect(() => {
    if (shopDetails) {
      checkUpdates(shopDetails);
    }
  }, [shopDetails, checkUpdates]);

  /** @type {UseQueryResult<CrispSessionResponse>} */
  const { data: crispSessionData } = useAppQuery("/api/store/crisp-session");

  useEffect(() => {
    if (crispSessionData) {
      Crisp.setTokenId(crispSessionData.crispSessionId);
      if (crispSessionData.user) {
        Crisp.user.setEmail(get(crispSessionData, "user.email", ""));
      }
      if (crispSessionData.session) {
        Crisp.session.setData({
          ...crispSessionData.session,
        });
      }
      Crisp.load();
    }
  }, [crispSessionData]);

  /**
   * @param {IProductOption} option
   */
  const handleOptionEdit = (option) => {
    setOptions((oldOptions) => {
      const optionIndex = oldOptions.findIndex((oldOption) => oldOption.id === option.id);

      const newOptions = [...oldOptions];
      newOptions.splice(optionIndex !== -1 ? optionIndex : 0, optionIndex !== -1 ? 1 : 0, option);

      return newOptions;
    });
  };

  /**
   * @param {number | undefined} optionId
   */
  const handleOptionDelete = (optionId) => {
    if (optionId) {
      setOptions((oldOptions) => oldOptions.filter((option) => option.id !== optionId));
    }

    setEditedOption(undefined);
  };

  /**
   * @param {IProductOptionSet} optionSet
   */
  const handleOptionSetEdit = (optionSet) => {
    setOptionSets((oldOptionSets) => {
      const optionSetIndex = oldOptionSets.findIndex((oldOptionSet) => oldOptionSet.id === optionSet.id);

      const newOptionSets = [...oldOptionSets];
      newOptionSets.splice(optionSetIndex !== -1 ? optionSetIndex : 0, optionSetIndex !== -1 ? 1 : 0, optionSet);

      return newOptionSets;
    });
  };

  /**
   * @param {number | undefined} optionSetId
   */
  const handleOptionSetDelete = (optionSetId) => {
    if (optionSetId) {
      setOptionSets((oldOptionSets) => oldOptionSets.filter((optionSet) => optionSet.id !== optionSetId));
    }

    setEditedOptionSet(undefined);
  };

  const isShowOnboarding =
    shopDetails &&
    (!shopDetails?.isSubscriptionActive || chargeId) &&
    !shopDetails.isExtensionEnabled &&
    isInitialized &&
    optionSets.length === 0 &&
    options.length === 0;

  useEffect(() => {
    if (isShowOnboarding) {
      setOnboardingInProgress(true);
    }
  }, [isShowOnboarding]);

  if (isSettingsPage || isCustomizationPage) {
    return <SettingsPage isCustomizationPage={isCustomizationPage} />;
  }

  if (isPricingPage) {
    return <PricingPage shopDetails={shopDetails} />;
  }

  if (shopDetails && isOnboardingInProgress) {
    return (
      <OnboardingPage
        shopDetails={shopDetails}
        options={options}
        onOptionSetEdit={handleOptionSetEdit}
        onOptionEdit={handleOptionEdit}
        onOptionDelete={handleOptionDelete}
        onFinished={() => {
          setOnboardingInProgress(false);
          setEnableExtensionDismissed(true);
          refetchShopDetails();
        }}
      />
    );
  }

  const isLimitReached = shopDetails?.isFeatureLimited ? optionSets.length >= freePlanMaxOptionSets : false;

  if (isInitialized && shopDetails) {
    if (editedOption) {
      return (
        <EditOptionPage
          option={editedOption}
          onClose={() => setEditedOption(undefined)}
          onEdit={handleOptionEdit}
          onDelete={handleOptionDelete}
          shopDetails={shopDetails}
        />
      );
    }

    if (editedOptionSet) {
      return (
        <EditOptionSetPage
          isDuplicateDisabled={isLimitReached}
          optionSet={editedOptionSet}
          options={options}
          onClose={() => setEditedOptionSet(undefined)}
          onEdit={handleOptionSetEdit}
          onDelete={handleOptionSetDelete}
          onOptionEdit={handleOptionEdit}
          onOptionDelete={handleOptionDelete}
          shopDetails={shopDetails}
        />
      );
    }
  }

  return (
    <HomePage
      isInitialized={isInitialized}
      isOptionsLoading={isOptionsLoading}
      optionSets={optionSets}
      setOptionSets={setOptionSets}
      options={options}
      setOptions={setOptions}
      onOptionSetEdit={setEditedOptionSet}
      onOptionEdit={setEditedOption}
      shopDetails={shopDetails}
      isLimitReached={isLimitReached}
      isEnableExtensionDismissed={isEnableExtensionDismissed}
      onEnableExtensionDismiss={() => setEnableExtensionDismissed(true)}
    />
  );
}
