{"private": true, "name": "product-options-frontend", "version": "1.0.0", "license": "UNLICENSED", "type": "module", "scripts": {"build": "vite build", "dev": "vite", "coverage": "vitest run --coverage"}, "engines": {"node": "^20.0.0"}, "stylelint": {"extends": "@shopify/stylelint-polaris"}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@hello-pangea/dnd": "^16.6.0", "@sentry/browser": "^8.9.2", "@shopify/app-bridge": "^3.7.10", "@shopify/app-bridge-react": "^4.1.3", "@shopify/polaris": "^13.5.0", "@shopify/polaris-icons": "^9.1.1", "@tanstack/react-query": "^5.40.1", "@uiw/react-codemirror": "^4.23.11", "crisp-sdk-web": "^1.0.25", "easyflow-enums": "file:../packages/easyflow-enums", "lodash": "^4.17.21", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.1.5", "storeware-tanstack-query": "github:Storeware-Apps/storeware-tanstack-query", "stylelint-config-recommended": "^14.0.1", "web-vitals": "^4.2.4"}, "devDependencies": {"@types/lodash": "^4.17.5", "@types/react-refresh": "^0.14.6", "@vitejs/plugin-react": "4.3.1", "react-refresh": "^0.14.2", "stylelint": "^15.11.0", "vite": "^5.2.13"}}