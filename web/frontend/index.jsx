// import "@shopify/polaris/build/esm/styles.css";

import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

// eslint-disable-next-line import/no-extraneous-dependencies
import { onCLS, onINP, onLCP } from "web-vitals";
import Base from "./Base";
// import "./style/custom.css";

const container = document.getElementById("app");
if (!container) {
  throw new Error("React root element is not found.");
}

onLCP((metric) => console.log("LCP: ", { ...metric }), { reportAllChanges: true });
onCLS((metric) => console.log("CLS: ", { ...metric }), { reportAllChanges: true });
onINP((metric) => console.log("INP: ", { ...metric }), { reportAllChanges: true });

createRoot(container).render(
  <StrictMode>
    <Base />
  </StrictMode>
);
