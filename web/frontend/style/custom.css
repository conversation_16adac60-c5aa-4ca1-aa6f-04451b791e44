/* polaris skeleton */
.skeleton-wrapper {
  /* font-size: 0; */
  line-height: 0;
}
.skeleton-box {
  display: inline-block;
  position: relative;
  overflow: hidden;
  background: #dde0e4;
  border-radius: 5px;
}
.skeleton-box:after {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform: translateX(-100%);
  background: #dde0e4;
  content: "";
  background-image: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  animation: shimmer 2s infinite;
}
/* polaris skeleton end */
.homepage-container,
.settingspage-container,
.pricing-page-wrapper,
.option-page-wrapper,
.option-set-page-wrapper,
.onboard-page-container {
  padding-bottom: 16px;
}
/* subscription plan page */
.subscription-wrapper .pro_plans .visionary_plan,
.subscription-wrapper .pro_plans .Polaris-Grid-Cell,
.subscription-wrapper .pro_plans .Polaris-ShadowBevel {
  display: flex;
}
.subscription-wrapper .pro_plans .Polaris-BlockStack {
  display: flex;
  flex: 1;
}
.subscription-wrapper .pro_plans .Polaris-Box {
  display: flex;
}
.subscription-wrapper .pro_plans .plan_card_feature {
  flex: 1;
  /* flex-direction: column; */
}
.subscription-wrapper .pro_plans .plan_card_feature .Polaris-Box {
  flex-direction: column;
}
/* subscription plan page end */

/* modal css override */
.app_bridge_modal .Polaris-TextField__Input {
  font-size: 13px !important;
}
.app_bridge_modal .Polaris-TextField__Input::placeholder {
  font-size: 13px !important;
}

/* modal css override end */

/* Content is hidden initially */
.homepage-content {
  opacity: 0;
  visibility: hidden;
  transition:
    opacity 0.5s ease-in-out,
    visibility 0.5s ease-in-out;
}

/* When content is loaded, fade it in */
.homepage-content.visible {
  opacity: 1;
  visibility: visible;
}

/* custom checkbox style */
.custom-checkbox {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #303030;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease-in-out;
  position: relative;
}
.custom-checkbox::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 10px;
  border: solid #ffffff;
  border-width: 0 2px 2px 0;
  transition: ease-in-out.35s;
  transform: translate(-50%, -50%) rotate(45deg);
}
.custom-checkbox:checked {
  background-color: #303030;
}
.custom-checkbox:checked::after {
  color: #ffffff;
}
/* custom checkbox style  end */

/* responsive css start */

@media (max-width: 490px) {
  .settingspage-container,
  .homepage-container,
  .pricing-page-wrapper,
  .option-page-wrapper,
  .option-set-page-wrapper,
  .onboard-page-container {
    padding: 16px 16px;
  }

  /* .Polaris-ShadowBevel {
    border-radius: var(--p-border-radius-300);
  }
  .Polaris-Box {
    border-radius: var(--p-border-radius-400);
  }
  .Polaris-Banner--withinPage {
    border-radius: var(--p-border-radius-400);
  } */
}

@media (max-width: 30.6225em) {
  .Polaris-IndexFilters.Polaris-IndexFilters__IndexFiltersSticky {
    position: unset;
  }
}
@media (max-width: 1000px) {
  .pro-plan-wrapper {
    justify-content: center;
  }
}
@media (max-width: 1142px) {
  .option-drag-div .Polaris-Icon {
    margin: 0;
  }
}
/* responsive css end */
