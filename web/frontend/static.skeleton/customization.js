/* eslint-disable import/prefer-default-export */
export const customizationPageSkeleton = `<div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--isSingleRow Polaris-Page-Header--noBreadcrumbs Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__TitleWrapper Polaris-Page-Header__TitleWrapperExpand"><div class="Polaris-Header-Title__TitleWrapper"><h1 class="Polaris-Header-Title"><span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">Customization</span></h1></div></div></div></div></div><div class=""><ui-save-bar id="easyflow-save-bar"><button type="button" variant="primary">Save</button><button type="button">Discard</button></ui-save-bar><div class="settingspage-container"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-InlineGrid" style="--pc-inline-grid-grid-template-columns-sm: repeat(1, minmax(0, 1fr)); --pc-inline-grid-grid-template-columns-md: repeat(2, minmax(0, 1fr)); --pc-inline-grid-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 300px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 50px;"></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 300px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px;"></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout"><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Font styles</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Customize the style of fonts on the storefront.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Bleed" style="--pc-bleed-margin-block-start-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px;"></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Button styles</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Customize the style of buttons on the storefront.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Bleed" style="--pc-bleed-margin-block-start-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px;"></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 250px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 250px;"></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Swatch styles</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Customize the style of the image and color swatches on the storefront.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Bleed" style="--pc-bleed-margin-block-start-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px;"></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-600); --pc-bleed-margin-inline-end-xs: var(--p-space-600);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Input styles</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Customize the style of text box, multi-line text box, number field and date picker on the storefront.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Bleed" style="--pc-bleed-margin-block-start-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px;"></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Dropdown</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><p class="Polaris-Text--root Polaris-Text--bodyMd">Customize the style of dropdowns on the storefront.</p></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-Bleed" style="--pc-bleed-margin-block-start-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 80px;"></div></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 45px; width: 45px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 70px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 25px; width: 140px;"></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Layout__AnnotatedSection"><div class="Polaris-Layout__AnnotationWrapper"><div class="Polaris-Layout__Annotation"><div class="Polaris-TextContainer Polaris-TextContainer--spacingTight"><h2 class="Polaris-Text--root Polaris-Text--headingMd">Customize option styles with CSS</h2><div class="Polaris-Box" style="--pc-box-color: var(--p-color-text-secondary);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><p class="Polaris-Text--root">You can write your own CSS code using the classes below to customize the style of the options.</p><p class="Polaris-Text--root">Don't want to code? <a target="_blank" class="Polaris-Link" href="mailto:<EMAIL>?subject=EasyFlow Custom Styles" rel="noopener noreferrer" data-polaris-unstyled="true">Contact us</a> and we can make the styling for you free of charge.</p><p class="Polaris-Text--root Polaris-Text--semibold">CSS classes:</p><ul class="Polaris-List Polaris-List--spacingLoose"><li class="Polaris-List__Item">ef__product-option-root</li><li class="Polaris-List__Item">ef__option-title</li><li class="Polaris-List__Item">ef__option-title-[option type]</li><li class="Polaris-List__Item">ef__option-selected-values</li><li class="Polaris-List__Item">ef__option-selected-values-price</li><li class="Polaris-List__Item">ef__option-title-required</li><li class="Polaris-List__Item">ef__option-description</li><li class="Polaris-List__Item">ef__option-description-[option type]</li><li class="Polaris-List__Item">ef__option-values</li><li class="Polaris-List__Item">ef__option-value</li><li class="Polaris-List__Item">ef__option-value-[option type]</li><li class="Polaris-List__Item">ef__option-value-price</li><li class="Polaris-List__Item">ef__option-value-description</li><li class="Polaris-List__Item">ef__option-value-description-[option type]</li><li class="Polaris-List__Item">ef__options-addon-total</li><li class="Polaris-List__Item">ef__options-addon-total-amount</li><li class="Polaris-List__Item">ef__character-counter</li></ul><p class="Polaris-Text--root Polaris-Text--bodySm">where [option type] is one of the following: checkbox, dropdown, image-swatch, radio-button, button, text-box, multi-line-text-box, number-field, date-picker, color-swatch</p></div></div></div></div><div class="Polaris-Layout__AnnotationContent"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><h2 class="Polaris-Text--root Polaris-Text--headingSm">CSS Code</h2><div class="Polaris-Box" style="--pc-box-border-color: var(--p-color-border-inverse); --pc-box-border-style: solid; --pc-box-border-width: var(--p-border-width-0165); --pc-box-padding-block-start-xs: var(--p-space-200); --pc-box-padding-block-end-xs: var(--p-space-200); --pc-box-padding-inline-start-xs: var(--p-space-200); --pc-box-padding-inline-end-xs: var(--p-space-200); --pc-box-border-radius: var(--p-border-radius-200); --pc-box-min-height: 450px;"></div></div></div></div></div></div></div></div></div></div></div>`;
