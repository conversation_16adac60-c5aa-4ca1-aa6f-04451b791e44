// eslint-disable-next-line import/prefer-default-export
export const homepageSkeletonDiv = `<div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--noBreadcrumbs Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__TitleWrapper"><div class="Polaris-Header-Title__TitleWrapper"><h1 class="Polaris-Header-Title"><span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">EasyFlow Product Options</span></h1></div></div><div class="Polaris-Page-Header__RightAlign"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantSecondary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter" type="button"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Create option</span></button><div class="Polaris-Page-Header__PrimaryActionWrapper"><div class="Polaris-Box Polaris-Box--printHidden"><button class="Polaris-Button Polaris-Button--pressable Polaris-Button--variantPrimary Polaris-Button--sizeMedium Polaris-Button--textAlignCenter" type="button"><span class="Polaris-Text--root Polaris-Text--bodySm Polaris-Text--medium">Create option set</span></button></div></div></div></div></div></div><div class=""><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 40px;"></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div><div class="Polaris-Bleed" style="--pc-bleed-margin-inline-start-xs: var(--p-space-400); --pc-bleed-margin-inline-end-xs: var(--p-space-400);"><hr class="Polaris-Divider" style="border-block-start: var(--p-border-width-025) solid var(--p-color-border-secondary);"></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: space-between; --pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 40px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 60px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 50px;"></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px;"></div></div><div class="Polaris-InlineGrid" style="--pc-inline-grid-grid-template-columns-xs: repeat(3, minmax(0, 1fr)); --pc-inline-grid-gap-xs: var(--p-space-200);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 150px; --pc-box-min-width: 200px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 180px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 150px; --pc-box-min-width: 200px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 180px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 90px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 90px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary);"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 180px;"><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 180px; --pc-box-min-width: 200px;">&nbsp;</div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 200px;"></div></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 500px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 400px;"></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-200); --pc-inline-stack-flex-direction-xs: row;"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 50px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 40px;"></div></div></div></div></div></div></div></div></div></div><div class="Polaris-Box"><div class="Polaris-BlockStack" style="--pc-block-stack-align: space-between; --pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 20px; width: 100px;"></div></div><div class="Polaris-InlineGrid" style="--pc-inline-grid-grid-template-columns-xs: repeat(3, minmax(0, 1fr)); --pc-inline-grid-gap-xs: var(--p-space-200);"><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-min-height: 100px; --pc-box-min-width: 180px;"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-400);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-fill-secondary); --pc-box-min-height: 100px;"></div><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 250px;"></div></div><div class="skeleton-wrapper"><div class="skeleton-box" style="height: 15px; width: 150px;"></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div>`;
