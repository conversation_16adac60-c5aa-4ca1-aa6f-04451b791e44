// eslint-disable-next-line import/prefer-default-export
export const onboardPageSkeleton = `
<div class="onboard-page-container"><div class="Polaris-Page"><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-start-md: var(--p-space-600); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-block-end-md: var(--p-space-600); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-start-sm: var(--p-space-0); --pc-box-padding-inline-end-xs: var(--p-space-400); --pc-box-padding-inline-end-sm: var(--p-space-0); position: relative;"><div class="Polaris-Page-Header--isSingleRow Polaris-Page-Header--noBreadcrumbs Polaris-Page-Header--mediumTitle"><div class="Polaris-Page-Header__Row"><div class="Polaris-Page-Header__TitleWrapper Polaris-Page-Header__TitleWrapperExpand"><div class="Polaris-Header-Title__TitleWrapper"><h1 class="Polaris-Header-Title"><span class="Polaris-Text--root Polaris-Text--headingLg Polaris-Text--bold">Product Options</span></h1></div></div></div></div></div><div class=""><div class="Polaris-ShadowBevel" style="--pc-shadow-bevel-z-index: 32; --pc-shadow-bevel-content-xs: &quot;&quot;; --pc-shadow-bevel-box-shadow-xs: var(--p-shadow-100); --pc-shadow-bevel-border-radius-xs: var(--p-border-radius-300);"><div class="Polaris-Box" style="--pc-box-background: var(--p-color-bg-surface); --pc-box-min-height: 100%; --pc-box-overflow-x: clip; --pc-box-overflow-y: clip; --pc-box-padding-block-start-xs: var(--p-space-400); --pc-box-padding-block-end-xs: var(--p-space-400); --pc-box-padding-inline-start-xs: var(--p-space-400); --pc-box-padding-inline-end-xs: var(--p-space-400);"><div class="Polaris-BlockStack" style="--pc-block-stack-order: column; --pc-block-stack-gap-xs: var(--p-space-200);"><h2 class="Polaris-Text--root Polaris-Text--headingSm">Get started with EasyFlow</h2><div class="Polaris-InlineStack" style="--pc-inline-stack-wrap: wrap; --pc-inline-stack-gap-xs: var(--p-space-400); --pc-inline-stack-flex-direction-xs: row;"><p class="Polaris-Text--root Polaris-Text--bodyLg">1/3 Steps: Learn the Basics</p><div style="width: 298px; margin-top: 5px;"><div class="Polaris-ProgressBar Polaris-ProgressBar--sizeMedium Polaris-ProgressBar--toneSuccess"><progress class="Polaris-ProgressBar__Progress" value="33.33333333333333" max="100"></progress><div class="Polaris-ProgressBar__Indicator Polaris-ProgressBar__IndicatorAppearDone" style="--pc-progress-bar-duration: 500ms; --pc-progress-bar-percent: 0.33333333333333326;"><span class="Polaris-ProgressBar__Label">33.33333333333333%</span></div></div></div></div><div class="Polaris-InlineStack" style="--pc-inline-stack-align: end; --pc-inline-stack-wrap: wrap; --pc-inline-stack-flex-direction-xs: row;"><div class="Polaris-ButtonGroup"><div class="Polaris-ButtonGroup__Item"><div class="skeleton-wrapper"><div class="skeleton-box" style="width: 50px; height: 25px;"></div></div></div><div class="Polaris-ButtonGroup__Item"><div class="skeleton-wrapper"><div class="skeleton-box" style="width: 50px; height: 25px;"></div></div></div></div></div></div></div></div><div class="Polaris-Box" style="--pc-box-padding-block-start-xs: var(--p-space-400);"><div class="skeleton-wrapper"><div class="skeleton-box" style="width: 950px; height: 530px;"></div></div></div></div></div></div>
`;
