/* eslint-disable react-hooks/exhaustive-deps */
import { useAppBridge } from "@shopify/app-bridge-react";
import { Page } from "@shopify/polaris";
import { useEffect } from "react";
import HomePageSkeleton from "../components/common/skeleton/HomePageSkeleton";
import SkeletonLoader from "../components/common/skeleton/Skeleton";

const ExitIframe = () => {
  const shopify = useAppBridge();

  useEffect(() => {
    shopify.loading(true);

    return () => {
      shopify.loading(false);
    };
  }, []);

  // const navigate = useNavigate();
  // const { search } = useLocation();

  // // @ts-ignore
  // const app = createApp(shopify.config);

  // useEffect(() => {
  //   if (!!app && !!search) {
  //     const params = new URLSearchParams(search);
  //     const redirectUri = params.get("redirectUri");
  //     // @ts-ignore
  //     const url = new URL(decodeURIComponent(redirectUri));

  //     // eslint-disable-next-line no-restricted-globals
  //     if (url.hostname === location.hostname) {
  //       // @ts-ignore
  //       navigate("/");
  //     }
  //   }
  // }, [app, search]);

  // console.log("exitIframe");

  return (
    <Page
      title="EasyFlow Product Options"
      primaryAction={<SkeletonLoader style={{ height: 25, width: 100 }} />}
      secondaryActions={<SkeletonLoader style={{ height: 25, width: 100 }} />}
    >
      <div className="homepage-container">
        <div className="homepage-static-loader">
          <HomePageSkeleton />
        </div>
      </div>
    </Page>
  );
};

export default ExitIframe;
