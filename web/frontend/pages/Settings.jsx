import { useEffect, useState } from "react";

import { Page } from "@shopify/polaris";

// eslint-disable-next-line import/no-relative-packages
import { useAppMutation } from "storeware-tanstack-query";
// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
// eslint-disable-next-line import/no-relative-packages
import { defaultStyleOptions as defaultCustomizations } from "../../defaults";
import SaveBarWrapper, { saveBarId } from "../components/common/SaveBarWrapper";
import SettingsContent from "../components/SettingsContent";
import { handelUpdateShopStyles } from "../services/settings.service";
import { useAppQuery, useTrackEvent } from "../utilities/hooks";

const SettingsPage = () => {
  const shopify = useAppBridge();

  /** @type {TypedState<StyleOptions>} */
  const [customizations, setCustomizations] = useState(defaultCustomizations);
  const [storedCustomizations, setStoredCustomizations] = useState(customizations);

  const [isSaveLoading, setSaveLoading] = useState(false);
  const [hasError, setHasError] = useState(false);

  const trackEvent = useTrackEvent();

  /** @type {UseQueryResult<StyleOptions>} */
  const { isLoading, data: stylesData } = useAppQuery("/api/styles/get");

  const { mutateAsync: updateShopStyles } = useAppMutation({
    mutationKey: ["updateShopStyles"],
    mutationFn: async (/** @type {StyleOptions} */ styles) => handelUpdateShopStyles(styles),
    onSuccess: (data, styles) => {
      setStoredCustomizations(styles);
    },
    onError: (error) => {
      console.error("Shop details update error", error);
    },
    options: {
      retry: 1,
    },
  });

  useEffect(() => {
    if (!stylesData) {
      return;
    }

    setCustomizations((oldCustomizations) => {
      const newStyles = { ...oldCustomizations, ...stylesData };

      setStoredCustomizations(newStyles);
      return newStyles;
    });

    trackEvent(`view settings page`);
  }, [stylesData, trackEvent]);

  const handleSave = async () => {
    setSaveLoading(true);

    if (
      !customizations.dropdownPlaceholder ||
      !customizations.fileUploadButtonText ||
      !customizations.dragAndDropText ||
      !customizations.uploadLoadingText ||
      !customizations.fileTypeErrorText ||
      !customizations.fileSizeErrorText ||
      !customizations.fileUploadErrorText ||
      !customizations.requiredOptionErrorText ||
      !customizations.minimumSelectionErrorText ||
      !customizations.maximumSelectionErrorText ||
      !customizations.mimumCharacterErrorText
    ) {
      setSaveLoading(false);
      setHasError(true);
      return;
    }
    await updateShopStyles(customizations);
    setSaveLoading(false);
    shopify.saveBar.hide(saveBarId);
  };

  useEffect(() => {
    if (storedCustomizations !== customizations) {
      shopify.saveBar.show(saveBarId);
    }
  }, [storedCustomizations, customizations, shopify]);

  return (
    <Page title="Settings">
      <SaveBarWrapper
        saveActionCallback={() => {
          handleSave();
        }}
        discardActionCallback={() => {
          setCustomizations(storedCustomizations);
          shopify.saveBar.hide(saveBarId);
        }}
        loading={isSaveLoading}
      />
      <div className="settingspage-container">
        <SettingsContent
          isLoading={isLoading}
          customizations={customizations}
          onCustomizationsChange={setCustomizations}
          isFieldRequiredError={hasError}
        />
      </div>
    </Page>
  );
};

export default SettingsPage;
