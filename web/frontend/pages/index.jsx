import { lazy, Suspense, useEffect, useState } from "react";

// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
import { <PERSON><PERSON>, <PERSON> } from "@shopify/polaris";
// eslint-disable-next-line import/no-relative-packages
import { defaultFreePlanMaxOptionSets as freePlanMaxOptionSets } from "../../defaults";

import HomePageSkeleton from "../components/common/skeleton/HomePageSkeleton";
import OnboardPageSkeleton from "../components/common/skeleton/OnboardPageSkeleton";
import OptionPageSkeleton from "../components/common/skeleton/OptionPageSkeleton";
import OptionSetPageSkeleton from "../components/common/skeleton/OptionSetPageSkeleton";
import { useStore } from "../components/providers/StoreProvider";
import { useAppQuery } from "../utilities/hooks";

const EditOptionSetPage = lazy(() => import("../components/pageComponents/EditOptionSetPage"));
const EditOptionPage = lazy(() => import("../components/pageComponents/EditOptionPage"));
const OnboardingPage = lazy(() => import("../components/pageComponents/OnboardingPage"));
const HomePage = lazy(() => import("../components/pageComponents/HomePage"));

const MainPage = () => {
  const shopify = useAppBridge();
  const { shopDetails, refetchShopDetails } = useStore();

  const [isInitialized, setInitialized] = useState(false);

  const [isOnboardingInProgress, setOnboardingInProgress] = useState(false);
  const [isEnableExtensionDismissed, setEnableExtensionDismissed] = useState(false);

  const [optionSets, setOptionSets] = useState(/** @type {IProductOptionSet[]} */ ([]));
  const [options, setOptions] = useState(/** @type {IProductOption[]} */ ([]));

  /** @type {TypedStateOptional<Partial<IProductOptionSet>>} */
  const [editedOptionSet, setEditedOptionSet] = useState();

  /** @type {TypedStateOptional<Partial<IProductOption>>} */
  const [editedOption, setEditedOption] = useState();

  /** @type {TypedStateOptional<URLSearchParams>} */
  const [optionCursorParameters, setOptionCursorParameters] = useState();

  /** @type {UseQueryResult<OptionItems>} */
  const { isLoading: isOptionsLoading, data: optionsData } = useAppQuery(
    !optionCursorParameters ? "/api/options/get" : `/api/options/get?${optionCursorParameters}`,
    { reactQueryOptions: { enabled: true } }
  );

  useEffect(() => {
    if (!optionsData) {
      return;
    }

    setInitialized(true);

    const { optionSets: newOptionSets = [], options: newOptions = [] } = optionsData;

    setOptionSets((oldOptionSets) => [...oldOptionSets, ...newOptionSets]);
    setOptions((oldOptions) => [...oldOptions, ...newOptions]);

    if (optionsData.optionSetsCursor || optionsData.optionsCursor) {
      const cursorParameters = new URLSearchParams();

      if (optionsData.optionSetsCursor) {
        cursorParameters.append("optionSetsCursor", optionsData.optionSetsCursor);
      }

      if (optionsData.optionsCursor) {
        cursorParameters.append("optionsCursor", optionsData.optionsCursor);
      }

      setOptionCursorParameters(cursorParameters);
    }
  }, [optionsData]);

  const chargeId = new URLSearchParams(window.location.search).get("charge_id");
  const planSlugQueryString = new URLSearchParams(window.location.search).get("plan");
  useEffect(() => {
    if (chargeId && planSlugQueryString) {
      shopify.toast.show("Subscription successful");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**
   * @param {IProductOption} option
   */
  const handleOptionEdit = (option) => {
    setOptions((oldOptions) => {
      const optionIndex = oldOptions.findIndex((oldOption) => oldOption.id === option.id);

      const newOptions = [...oldOptions];
      newOptions.splice(optionIndex !== -1 ? optionIndex : 0, optionIndex !== -1 ? 1 : 0, option);

      return newOptions;
    });
  };

  /**
   * @param {number | undefined} optionId
   */
  const handleOptionDelete = (optionId) => {
    if (optionId) {
      setOptions((oldOptions) => oldOptions.filter((option) => option.id !== optionId));
    }

    setEditedOption(undefined);
  };

  /**
   * @param {IProductOptionSet} optionSet
   */
  const handleOptionSetEdit = (optionSet) => {
    setOptionSets((oldOptionSets) => {
      const optionSetIndex = oldOptionSets.findIndex((oldOptionSet) => oldOptionSet.id === optionSet.id);
      const newOptionSets = [...oldOptionSets];
      newOptionSets.splice(optionSetIndex !== -1 ? optionSetIndex : 0, optionSetIndex !== -1 ? 1 : 0, optionSet);
      return newOptionSets;
    });
  };

  /**
   * @param {number | undefined} optionSetId
   */
  const handleOptionSetDelete = (optionSetId) => {
    if (optionSetId) {
      setOptionSets((oldOptionSets) => oldOptionSets.filter((optionSet) => optionSet.id !== optionSetId));
    }

    setEditedOptionSet(undefined);
  };

  const isShowOnboarding =
    shopDetails &&
    (!shopDetails?.isSubscriptionActive || chargeId) &&
    !shopDetails.isExtensionEnabled &&
    isInitialized &&
    optionSets.length === 0 &&
    options.length === 0;

  useEffect(() => {
    if (isShowOnboarding) {
      setOnboardingInProgress(true);
    }
  }, [isShowOnboarding]);

  if (shopDetails && isOnboardingInProgress) {
    return (
      <Suspense fallback={<OnboardPageSkeleton />}>
        <OnboardingPage
          shopDetails={shopDetails}
          options={options}
          onOptionSetEdit={handleOptionSetEdit}
          onOptionEdit={handleOptionEdit}
          onOptionDelete={handleOptionDelete}
          onFinished={() => {
            setOnboardingInProgress(false);
            setEnableExtensionDismissed(true);
            refetchShopDetails();
          }}
        />
      </Suspense>
    );
  }

  const isLimitReached = shopDetails?.isFeatureLimited ? optionSets.length >= freePlanMaxOptionSets : false;

  if (isInitialized && shopDetails) {
    if (editedOption) {
      return (
        <Suspense fallback={<OptionPageSkeleton />}>
          <EditOptionPage
            option={editedOption}
            onClose={() => setEditedOption(undefined)}
            onEdit={handleOptionEdit}
            onDelete={handleOptionDelete}
            shopDetails={shopDetails}
          />
        </Suspense>
      );
    }

    if (editedOptionSet) {
      return (
        <Suspense fallback={<OptionSetPageSkeleton />}>
          <EditOptionSetPage
            isDuplicateDisabled={isLimitReached}
            optionSet={editedOptionSet}
            options={options}
            onClose={() => setEditedOptionSet(undefined)}
            onEdit={handleOptionSetEdit}
            onDelete={handleOptionSetDelete}
            onOptionEdit={handleOptionEdit}
            onOptionDelete={handleOptionDelete}
            shopDetails={shopDetails}
          />
        </Suspense>
      );
    }
  }

  return (
    <Suspense
      fallback={
        <div className="homepage-container">
          <Page
            title="EasyFlow Product Options"
            secondaryActions={
              <Button
                onClick={() => {}}
                variant="secondary"
              >
                Create option
              </Button>
            }
            primaryAction={
              <Button
                onClick={() => {}}
                variant="primary"
              >
                Create option set
              </Button>
            }
          >
            <HomePageSkeleton />
          </Page>
        </div>
      }
    >
      <HomePage
        isInitialized={isInitialized}
        isOptionsLoading={isOptionsLoading}
        optionSets={optionSets}
        setOptionSets={setOptionSets}
        options={options}
        setOptions={setOptions}
        onOptionSetEdit={setEditedOptionSet}
        onOptionEdit={setEditedOption}
        isLimitReached={isLimitReached}
        isEnableExtensionDismissed={isEnableExtensionDismissed}
        onEnableExtensionDismiss={() => setEnableExtensionDismissed(true)}
      />
    </Suspense>
  );
};

export default MainPage;
