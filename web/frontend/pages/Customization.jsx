import { useEffect, useState } from "react";

import { Page } from "@shopify/polaris";

// eslint-disable-next-line import/no-relative-packages
import { useAppMutation } from "storeware-tanstack-query";
// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
import { defaultStyleOptions as defaultCustomizations } from "../../defaults";
import SaveBarWrapper, { saveBarId } from "../components/common/SaveBarWrapper";
import CustomizationContent from "../components/CustomizationContent";
import { handelUpdateShopStyles } from "../services/settings.service";
import { useAppQuery, useTrackEvent } from "../utilities/hooks";

const CustomizationPage = () => {
  const shopify = useAppBridge();

  /** @type {TypedState<StyleOptions>} */
  const [customizations, setCustomizations] = useState(defaultCustomizations);
  const [storedCustomizations, setStoredCustomizations] = useState(customizations);

  const [isSaveLoading, setSaveLoading] = useState(false);

  const trackEvent = useTrackEvent();

  /** @type {UseQueryResult<StyleOptions>} */
  const { isLoading, data: stylesData } = useAppQuery("/api/styles/get");

  useEffect(() => {
    if (storedCustomizations !== customizations) {
      shopify.saveBar.show(saveBarId);
    }
  }, [storedCustomizations, customizations, shopify]);

  const { mutateAsync: updateShopStyles } = useAppMutation({
    mutationKey: ["updateShopStyles"],
    mutationFn: async (/** @type {StyleOptions} */ styles) => handelUpdateShopStyles(styles),
    onSuccess: (data, styles) => {
      setStoredCustomizations(styles);
    },
    onError: (error) => {
      console.error("Shop details update error", error);
    },
    options: {
      retry: 1,
    },
  });

  useEffect(() => {
    if (!stylesData) {
      return;
    }
    setCustomizations((oldCustomizations) => {
      const newStyles = { ...oldCustomizations, ...stylesData };
      setStoredCustomizations(newStyles);
      return newStyles;
    });

    trackEvent(`view customization page`);
  }, [stylesData, trackEvent]);

  const handleSave = async () => {
    setSaveLoading(true);
    await updateShopStyles(customizations);
    setSaveLoading(false);
    shopify.saveBar.hide(saveBarId);
  };

  return (
    <Page title="Customization">
      <SaveBarWrapper
        saveActionCallback={() => {
          handleSave();
        }}
        discardActionCallback={() => {
          setCustomizations(storedCustomizations);
          shopify.saveBar.hide(saveBarId);
        }}
        loading={isSaveLoading}
      />
      <div className="settingspage-container">
        <CustomizationContent
          isLoading={isLoading}
          customizations={customizations}
          onCustomizationsChange={setCustomizations}
        />
      </div>
    </Page>
  );
};

export default CustomizationPage;
