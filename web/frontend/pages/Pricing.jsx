import { Banner, BlockStack, Box, Page, Text } from "@shopify/polaris";
// @ts-ignore
import { bool, string } from "prop-types";
import { useEffect, useMemo, useState } from "react";
import SubscriptionLayoutSkeleton from "../components/common/skeleton/SubscriptionLayoutSkeleton";
import { useStore } from "../components/providers/StoreProvider";
import PlanPricingCard from "../components/subscriptions/PlanPricingCards";
import { useAppQuery } from "../utilities/hooks";

/**
 * @param {{
 *    pageTitle?: string
 *    subTitle?: string,
 *    isOnboarding?: boolean
 * }} props
 */

function SubscriptionPage({
  isOnboarding = undefined,
  pageTitle = "Choose the perfect plan for your store",
  subTitle = "Explore our flexible pricing options and choose the one that fits your needs",
}) {
  const { shopDetails } = useStore();
  const initialMoneyBackBannerDismissed = useMemo(
    () =>
      localStorage.getItem(`is${isOnboarding ? "OnboardingPage" : "PricingPage"}MoneyBackBannerDismissed`) === "true",
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );
  const initialOldPlanBannerDismissed = useMemo(
    () => localStorage.getItem(`is${isOnboarding ? "OnboardingPage" : "PricingPage"}OldPlanBannerDismissed`) === "true",
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const [plans, setPlans] = useState([]);
  const [isMoneyBackBannerDismissed, setIsMoneyBackBannerDismissed] = useState(initialMoneyBackBannerDismissed);
  const [isOldPlanBannerDismissed, setIsOldPlanBannerDismissed] = useState(initialOldPlanBannerDismissed);

  const { isLoading, data, isFetching } = useAppQuery("/api/subscription", {
    fetchOptions: {
      method: "GET",
    },
    debounceMilliseconds: 250,
  });

  useEffect(() => {
    if (data?.plan_data) {
      setPlans(data.plan_data);
    }
  }, [data]);

  return (
    <div className="pricing-page-wrapper">
      <Page
        title={!isOnboarding ? pageTitle : undefined}
        subtitle={!isOnboarding ? subTitle : undefined}
        backAction={
          !isOnboarding
            ? {
                content: "Back",
                onAction: () => window.history.back(),
              }
            : undefined
        }
        narrowWidth
      >
        <Box>
          {(!shopDetails && isLoading) || isFetching || plans.length <= 0 ? (
            <SubscriptionLayoutSkeleton isLayoutSkeleton={false} />
          ) : (
            <BlockStack gap="400">
              {!isMoneyBackBannerDismissed && !shopDetails?.isSubscriptionActive && (
                <Banner
                  title="30-day money back guarantee. No questions asked."
                  tone="info"
                  onDismiss={() => {
                    setIsMoneyBackBannerDismissed(true);
                    localStorage.setItem(
                      `is${isOnboarding ? "OnboardingPage" : "PricingPage"}MoneyBackBannerDismissed`,
                      "true"
                    );
                  }}
                >
                  We&apos;ll refund you all app charges within the first 30-days if the app doesn’t meet your needs.
                </Banner>
              )}

              {data?.is_old_subscription && !isOldPlanBannerDismissed && (
                <Banner
                  tone="info"
                  onDismiss={() => {
                    setIsOldPlanBannerDismissed(true);
                    localStorage.setItem(
                      `is${isOnboarding ? "OnboardingPage" : "PricingPage"}OldPlanBannerDismissed`,
                      "true"
                    );
                  }}
                >
                  You are on the{" "}
                  <Text
                    variant="bodyMd"
                    as="span"
                    fontWeight="semibold"
                  >
                    Recurring billing plan
                  </Text>
                </Banner>
              )}

              <PlanPricingCard
                allPlans={plans}
                isOnboarding={isOnboarding || undefined}
              />
            </BlockStack>
          )}
        </Box>
      </Page>
    </div>
  );
}

SubscriptionPage.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  pageTitle: string,
  subTitle: string,
  isOnboarding: bool,
};

export default SubscriptionPage;
