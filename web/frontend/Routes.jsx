// @ts-nocheck
import { <PERSON>, <PERSON> } from "@shopify/polaris";
import { useState } from "react";
import { Route, useRouteError } from "react-router-dom";
import NotFound from "./pages/NotFound";

/**
 * File-based routing.
 * @desc File-based routing that uses React Router under the hood.
 * To create a new route create a new .jsx file in `/pages` with a default export.
 *
 * Some examples:
 * * `/pages/index.jsx` matches `/`
 * * `/pages/blog/[id].jsx` matches `/blog/123`
 * * `/pages/[...catchAll].jsx` matches any URL not explicitly matched
 *
 * @param {object} pages value of import.meta.globEager(). See https://vitejs.dev/guide/features.html#glob-import
 *
 * @return {Routes} `<Routes/>` from React Router, with a `<Route/>` for each file in `pages`
 */

// eslint-disable-next-line react/prop-types
const Routes = ({ pages }) => {
  const routes = useRoutes(pages);
  const routeComponents = routes.map(({ path, component: Component }) => (
    <Route
      key={`${path}-${Math.random()}`}
      hasErrorBoundary
      errorElement={<ErrorBoundary />}
      path={path}
      element={<Component />}
    />
  ));

  return (
    <>
      {routeComponents}
      <Route
        path="*"
        element={<NotFound />}
      />
    </>
  );
};

function useRoutes(pages) {
  const routes = Object.keys(pages)
    .map((key) => {
      let path = key
        .replace("./pages", "")
        .replace(/\.(t|j)sx?$/, "")
        /**
         * Replace /index with /
         */
        .replace(/\/index$/i, "/")
        /**
         * Only lowercase the first letter. This allows the developer to use camelCase
         * dynamic paths while ensuring their standard routes are normalized to lowercase.
         */
        .replace(/\b[A-Z]/, (firstLetter) => firstLetter.toLowerCase())
        /**
         * Convert /[handle].jsx and /[...handle].jsx to /:handle.jsx for react-router-dom
         */
        .replace(/\[(?:[.]{3})?(\w+?)\]/g, (_match, param) => `:${param}`);

      if (path.endsWith("/") && path !== "/") {
        path = path.substring(0, path.length - 1);
      }
      const Component = pages[key]?.default;
      return {
        path,
        component: Component || null,
      };
    })
    .filter((route) => route.component);

  return routes;
}

export default Routes;

const ErrorBoundary = () => {
  const error = useRouteError();
  const [loader, setLoader] = useState(false);

  const hardRefresh = () => {
    setLoader(true);
    window.location.reload();
  };
  return (
    <Page>
      <Banner
        tone="critical"
        title={`${error?.name || "Error"}: ${error?.message || "An unknown error occurred"}`}
        action={{ content: "Reload page", loading: loader, onAction: hardRefresh }}
      >
        <p>{error?.stack || "No stack trace available"}</p>
      </Banner>
    </Page>
  );
};
