import { debounce } from "lodash";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { useAppQuery as useQuery } from "storeware-tanstack-query";

/**
 * @template TData
 *
 * @param {string} url
 * @param {{
 *   fetchOptions?: RequestInit,
 *   reactQueryOptions?: Partial<import('@tanstack/react-query').UseQueryOptions<TData>>
 *   debounceMilliseconds?: number,
 * }} [options]
 *
 * @return {import('@tanstack/react-query').UseQueryResult<TData>}
 */
export function useAppQuery(url, options) {
  const [debouncedURL, setDebouncedURL] = useState(url);

  const debouncedSetURL = useMemo(
    () => debounce((newUrl) => setDebouncedURL(newUrl), options?.debounceMilliseconds),
    [options?.debounceMilliseconds]
  );

  useEffect(() => {
    debouncedSetURL(url);
  }, [url, debouncedSetURL]);

  return useQuery({
    queryKey: [debouncedURL],
    // @ts-ignore
    queryFn: async () => {
      // old
      // const response = await fetch(url, options?.fetchOptions);
      // return response.json();

      // rumi
      const { method = "GET", body, headers = {}, ...restFetchOptions } = options?.fetchOptions || {};

      // Add Content-Type if there is a body
      const finalHeaders = {
        "Content-Type": body ? "application/json" : undefined,
        ...headers,
      };

      // Prepare the fetch options
      const finalFetchOptions = {
        method,
        body: method === "POST" ? JSON.stringify(body) : undefined,
        headers: finalHeaders,
        ...restFetchOptions,
      };

      // @ts-ignore
      const response = await fetch(url, finalFetchOptions);
      return response.json();
    },
    refetchOnWindowFocus: false,
    ...options?.reactQueryOptions,
  });
}

/**
 * A hook that returns a function for tracking events.
 * @desc The returned function will send a POST request to /api/track-event
 *
 * @returns trackEvent function
 */
export function useTrackEvent() {
  return useCallback((/** @type {string} */ eventName, /** @type {Record<string, any>} */ properties = {}) => {
    fetch("/api/track-event", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ eventName, properties }),
    });
  }, []);
}

/**
 * @template T
 * @param {T} value
 * @param {T} initialValue
 */
export function usePrevious(value, initialValue) {
  const ref = useRef(initialValue);

  useEffect(() => {
    ref.current = value;
  });

  return ref.current;
}

/**
 * @param {any[]} dependencies
 * @param {string[]} dependencyNames
 */
function useHookDebugger(dependencies, dependencyNames = []) {
  const previousDependencies = usePrevious(dependencies, []);

  const changedDependencies = dependencies.reduce((accum, dependency, index) => {
    if (dependency !== previousDependencies[index]) {
      const keyName = dependencyNames[index] || index;

      return {
        ...accum,
        [keyName]: { before: previousDependencies[index], after: dependency },
      };
    }

    return accum;
  }, {});

  if (Object.keys(changedDependencies).length) {
    // eslint-disable-next-line no-console
    console.log("[hook-debugger] ", changedDependencies);
  }

  return changedDependencies;
}

/**
 * @param {() => void} effectHook
 * @param {any[]} dependencies
 * @param {string[]} dependencyNames
 */
export function useEffectDebugger(effectHook, dependencies, dependencyNames = []) {
  useHookDebugger(dependencies, dependencyNames);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(effectHook, dependencies);
}

/**
 * @param {(...args: any[]) => any} callbackHook
 * @param {any[]} dependencies
 * @param {string[]} dependencyNames
 */
export function useCallbackDebugger(callbackHook, dependencies, dependencyNames = []) {
  useHookDebugger(dependencies, dependencyNames);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(callbackHook, dependencies);
}

/**
 * @param {(...args: any[]) => any} memoHook
 * @param {any[]} dependencies
 * @param {string[]} dependencyNames
 */
export function useMemoDebugger(memoHook, dependencies, dependencyNames = []) {
  useHookDebugger(dependencies, dependencyNames);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useMemo(memoHook, dependencies);
}
