/**
 * @param {string} path
 * @param {string} [target]
 */
export default function navigateToAppPath(path, target) {
  // `window.open(path)` doesn't add the url parameters (`shop`, `host`, etc.), so we need to
  // manually build the url
  const appName = process.env.NODE_ENV === "production" ? "product-options-35" : "product-options-test";

  window.open(`shopify://admin/apps/${appName}${path}`, target);
}
