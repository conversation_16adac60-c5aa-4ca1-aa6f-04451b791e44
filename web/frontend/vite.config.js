/* eslint-disable import/no-extraneous-dependencies, no-console */
import react from "@vitejs/plugin-react";
import fs from "fs/promises";
import { dirname } from "path";
import { fileURLToPath } from "url";
import { defineConfig, loadEnv } from "vite";

if (process.env.npm_lifecycle_event === "build" && !process.env.SHOPIFY_API_KEY && !process.env.CI) {
  console.warn(
    "\nBuilding the frontend app without an API key." +
      " The frontend build will not run without an API key." +
      " Set the SHOPIFY_API_KEY environment variable when running the build command.\n"
  );
}

const host = process.env.HOST ? process.env.HOST.replace(/https?:\/\//, "") : "localhost";
const frontendPort = process.env.FRONTEND_PORT ? parseInt(process.env.FRONTEND_PORT, 10) : 61829;

const hmrConfig =
  host === "localhost"
    ? { protocol: "ws", host: "localhost", port: 64999, clientPort: 64999 }
    : { protocol: "wss", host, port: frontendPort, clientPort: 443 };

const proxyOptions = {
  target: `http://127.0.0.1:${process.env.BACKEND_PORT}`,
  changeOrigin: false,
  secure: true,
  ws: false,
};

/**
 * Example return value: `^/(?:(?:pages)|(?:products))?(\\?.*)?$`
 * @returns {Promise<string>}
 *
 */
const topLevelPageRoutesRegex = async () => {
  const routeFileNames = await fs.readdir(`${import.meta.dirname}/pages`);

  const individualRoutesGroupRegexs = routeFileNames.map((fileName) => {
    const routePath = fileName.split(".")[0];
    return `(?:${routePath})`;
  });

  return `^/(?:${individualRoutesGroupRegexs.join("|")})?(?!/dev_embed.js)[/a-z\\d-]*(\\?.*)?$`;
};

export default defineConfig(async ({ command, mode }) => {
  const env = loadEnv(mode, `${process.cwd()}/../../`, "VITE_");
  const pageRoutesRegex = await topLevelPageRoutesRegex();

  // Ensure correct serialization while preserving correct data types
  let processEnv = Object.fromEntries(
    Object.entries(env).map(([key, value]) => {
      if (value === "true" || value === "false") {
        return [key, value];
      }
      if (!isNaN(value) && value.trim() !== "") {
        return [key, Number(value)];
      }
      return [key, value];
    })
  );

  processEnv = {
    ...processEnv,
    SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY,
  };

  return {
    root: dirname(fileURLToPath(import.meta.url)),
    plugins: [react()],
    define: {
      "process.env": { ...processEnv },
    },
    resolve: {
      preserveSymlinks: true,
    },
    server: {
      host: "localhost",
      port: frontendPort,
      hmr: hmrConfig,
      proxy: {
        "^/(\\?.*)?$": proxyOptions,
        "^/api(/|(\\?.*)?$)": proxyOptions,
        "^/appProxy(/|(\\?.*)?$)": proxyOptions,
        // '^/(customization|settings|pricing|exitiframe|subscription)': proxyOptions,
        [pageRoutesRegex]: proxyOptions,
      },
    },
    build: {
      rollupOptions: {
        output: {
          entryFileNames: "[name]-[hash].js",
          chunkFileNames: "chunks/[name]-[hash].js",
          assetFileNames: "assets/[name]-[hash][extname]",
          sanitizeFileName: true,
          manualChunks: (id) => {
            if (id.match(/node_modules\/react-dom/gim)) return "base-app";
            if (id.match(/node_modules\/(?:(?:react-router-dom)|(?:react-router))\//gim)) return "base-app";
            if (id.match(/node_modules\/react\//gim)) return "base-app";
            if (id.match(/node_modules\/@shopify/gim) && !id.match(/@shopify\/polaris/gim)) return "base-app";

            // if (id.match(/node_modules\/storeware-tanstack-query/gim)) return "base-app";

            // if (id.match(/@shopify\/polaris/gim)) return "polaris";
            // if (id.match(/@shopify\/polaris-icons/gim)) return "polaris";

            // if (id.match(/frontend\/components\/providers\/EasyFlowProvider\.jsx/gim)) return "easy-flow-provider";

            // skeletons
            // if (id.match(/frontend\/components\/common\/skeleton\/HomePageSkeleton\.jsx/gim)) return "base-app";

            // if (id.match(/frontend\/components\/common\/skeleton\/OnboardPageSkeleton\.jsx/gim)) return "base-app";
            // if (id.match(/frontend\/components\/common\/skeleton\/OptionPageSkeleton\.jsx/gim)) return "base-app";
            // if (id.match(/frontend\/components\/common\/skeleton\/OptionSetPageSkeleton\.jsx/gim)) return "base-app";

            // if (id.match(/frontend\/components\/Partners\.jsx/gim)) return "partners";

            // if (id.match(/frontend\/utilities/gim) || id.match(/frontend\/configs/gim)) return "app-helpers";
            // if (id.match(/frontend\/components\/providers/gim)) return "app-providers";

            // if (id.match(/node_modules\/@hello-pangea/gim)) return "third-party-js";
          },
        },
      },
    },
  };
});
