declare module "eslint-config-airbnb";

declare interface Window {
  $RefreshReg$?: () => void;
  $RefreshSig$?: () => (type: string) => string;
  __vite_plugin_react_preamble_installed__?: true;
  __SHOPIFY_DEV_HOST?: string;
}

type TypedState<T> = [T, import("react").Dispatch<import("react").SetStateAction<T>>];
type TypedStateOptional<T> = ReturnType<typeof import("react").useState<T>>;

type UseQueryResult<TData> = import("@tanstack/react-query").UseQueryResult<TData>;

type SettingsInput = Omit<ISettings, "fulfillmentDateOffset" | "dueDateOffset" | "paymentMethodOverrides"> & {
  fulfillmentDateOffset: string;
  dueDateOffset: string;
  paymentMethodOverrides: Partial<PaymentMethodOverride>[];
};

type ProductOptionSetInput = Partial<
  Omit<IProductOptionSet, "productIds" | "rules"> & {
    rules?: Partial<
      Omit<IProductOptionRule, "conditions" | "actions"> & {
        conditions: Partial<IProductOptionRuleCondition>[];
        actions: Partial<IProductOptionRuleAction>[];
      }
    >[];
    productIds: string[];
  }
>;

type ProductOptionInput = Omit<
  Partial<
    Omit<IProductOption, "values" | "productIds"> & {
      values: Partial<
        Omit<IProductOption["values"][number], "price"> & {
          elementId: string;
          price?: string;
          imageFile?: File;
          htmlContent?: string;
        }
      >[];
      productIds: string[];
    }
  >,
  "type"
> & { type: IProductOption["type"] };
