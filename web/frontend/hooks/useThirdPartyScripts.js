import * as Sentry from "@sentry/browser";
import { Crisp } from "crisp-sdk-web";
import { get } from "lodash";
import configs from "../configs";

const useThirdPartyScripts = () => ({
  loadClarityScript() {
    const clarityScript = document.querySelector('script[src="https://www.clarity.ms/tag/q1g9iaxw8j"]');
    // Load Clarity script if enabled
    if (!clarityScript && configs.loadClarityScript) {
      (function (c, l, a, r, i, t, y) {
        c[a] =
          c[a] ||
          function () {
            (c[a].q = c[a].q || []).push(arguments);
          };
        t = l.createElement(r);
        t.async = 1;
        t.src = "https://www.clarity.ms/tag/" + i;
        y = l.getElementsByTagName(r)[0];
        y.parentNode.insertBefore(t, y);
      })(window, document, "clarity", "script", "q1g9iaxw8j");
    }
  },
  // Function to remove the Clarity script
  removeClarityScript() {
    const script = document.querySelector('script[src="https://www.clarity.ms/tag/q1g9iaxw8j"]');
    if (script) {
      script.remove();
      console.log("Clarity script removed successfully.");
    } else {
      console.log("Clarity script not found.");
    }
  },
  loadSentryScript() {
    if (!configs.loadSentryScript) {
      return;
    }
    const shopId = new URLSearchParams(window.location.search).get("shop");
    // Initialize Sentry replays
    Sentry.init({
      dsn: configs.sentryDNS,
      replaysSessionSampleRate: 1,
      replaysOnErrorSampleRate: 1,
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({ maskAllText: false, blockAllMedia: true }),
      ],
      ...(shopId ? { initialScope: { user: { id: shopId } } } : {}),
    });
  },
  async loadCrispScript() {
    try {
      // Configure Crisp
      const websiteId = "57c11e5d-c42e-4758-a784-9a30be2fac1a";
      Crisp.configure(websiteId, { autoload: false });

      // Fetch Crisp session data
      const response = await fetch("/api/store/crisp-session");
      if (response.ok) {
        const crispSessionData = await response.json();

        if (crispSessionData) {
          Crisp.setTokenId(crispSessionData.crispSessionId);
          if (crispSessionData.user) {
            Crisp.user.setEmail(get(crispSessionData, "user.email", ""));
          }
          if (crispSessionData.session) {
            Crisp.session.setData({
              ...crispSessionData.session,
            });
          }
          Crisp.load();
          console.log("Crisp script loaded successfully.");
        }
      }
    } catch (error) {
      console.error("Failed to load Crisp script:", error);
    }
  },
});

export default useThirdPartyScripts;
