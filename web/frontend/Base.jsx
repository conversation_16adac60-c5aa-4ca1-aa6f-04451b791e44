// @ts-nocheck
import { AppProvider } from "@shopify/polaris";
import translations from "@shopify/polaris/locales/en.json";
// eslint-disable-next-line import/no-extraneous-dependencies
import { createBrowserRouter, createRoutesFromElements, Outlet, Route, RouterProvider } from "react-router-dom";
import { QueryProvider } from "storeware-tanstack-query";
import AppNavigationProvider from "./components/providers/AppNavigationProvider";
import EasyFlowProvider from "./components/providers/EasyFlowProvider";
import ErrorProvider from "./components/providers/ErrorProvider";
import StoreProvider from "./components/providers/StoreProvider";
import Routes from "./Routes";

const Base = () => {
  const pages = import.meta.glob("./pages/**/!(*.test.[jt]sx)*.([jt]sx)", { eager: true });
  const router = createBrowserRouter(
    createRoutesFromElements(
      <Route
        path="/"
        element={
          <QueryProvider>
            <AppNavigationProvider />
            <EasyFlowProvider />
            <StoreProvider>
              <ErrorProvider>
                <Outlet />
              </ErrorProvider>
            </StoreProvider>
          </QueryProvider>
        }
      >
        {Routes({ pages })}
      </Route>
    )
  );

  return (
    <AppProvider i18n={translations}>
      <RouterProvider router={router} />
    </AppProvider>
  );
};

export default Base;
