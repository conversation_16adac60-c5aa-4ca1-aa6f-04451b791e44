// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";
import { useState } from "react";

// @ts-ignore
const MultiLineOptionPreview = ({ option = {} }) => {
  const [valueCount, setValueCount] = useState(0);
  const price = parseFloat(option?.values?.[0]?.price) > 0 ? parseFloat(option?.values?.[0]?.price).toFixed(2) : null;
  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          input,
          textarea {
            outline: 0;
          }
            
          /* multi text */
          .multi-line-text {
            width: 100%;
            min-height: 70px;
            padding: 12px;
            border: 1px solid #8A8A8A;
            border-radius: 8px;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }

          .required-field::after {
            content: '*';
            color: #EA3323;
            margin-left: 4px;
          }

          .ef__character-counter {
            position: absolute;
            bottom: ${option?.isRequired ? "3rem" : "1.5rem"};
            right: 1.5rem;
            opacity: 0.4;
            font-size: 12px;
          }
        `}
      </style>
      <Box>
        <label className={`label ${option?.isRequired ? "required-field" : ""}`}>
          {option?.title || "Multi line text box"}
          {price && <span className="ef__option-value-price"> (+&nbsp;{price})</span>}
        </label>
        {option?.values?.length > 0 &&
          option?.values.map((val) => (
            <>
              {option?.description && (
                <div className="caption ef__option-description ef__option-description-text-box">
                  {option?.description ?? ""}
                </div>
              )}
              <textarea
                className="multi-line-text"
                key={val.elementId}
                maxLength={option?.maximumValue ?? undefined}
                minLength={option?.minimumValue ?? undefined}
                placeholder={option?.placeholderText ?? ""}
                onChange={(e) => setValueCount(e.target.value.length)}
              />
              {option?.maximumValue > 0 && (
                <span className="ef__character-counter">
                  {valueCount}/{option?.maximumValue ?? ""}
                </span>
              )}
              {option.isRequired && (
                <div className="error-message">{option?.title || "Multi line text box"} is required</div>
              )}
            </>
          ))}
      </Box>
    </Box>
  );
};

export default MultiLineOptionPreview;
