// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";

// @ts-ignore
const HTMLOptionPreview = ({ option = {} }) => {
  // Get the raw HTML content without any wrapping tags
  const htmlContent = option?.values?.[0]?.htmlContent || "<div>HTML content will appear here</div>";

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          .required-field {
            position: relative;
          }

          .required-field::after {
            content: '*';
            color: #EA3323;
            margin-left: 4px;
          }

          .html-content {
            margin-top: 8px;
            background-color: transparent;
            color: #303030;
            border-radius: 4px;
            // font-family: monospace;
            // white-space: pre-wrap;
            overflow-x: auto;
          }
          iframe {
            border: 0;
            max-width: 100%;
          }

          /* Apply max-width: 100% to all possible HTML elements within the HTML content */
          .html-content * {
            max-width: 100%;
            line-height: 1em;
          }
          .html-content p {
            line-height: 1.4em;
          }

          /* Ensure specific media elements maintain aspect ratio */
          .html-content img,
          .html-content video,
          .html-content canvas,
          .html-content svg {
            max-width: 100%;
            height: auto;
          }

          /* Ensure tables don't overflow */
          .html-content table {
            max-width: 100%;
            overflow-x: auto;
            display: block;
          }

        `}
      </style>
      <Box>
        <label className={`label ${option?.isRequired ? "required-field" : ""}`}>{option?.title || "HTML"}</label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-html">{option?.description ?? ""}</div>
        )}
        {/*
          Using dangerouslySetInnerHTML is necessary here as we're displaying HTML content
          that the user has entered. In a production environment, this should be sanitized.
        */}
        <div
          className="html-content"
          dangerouslySetInnerHTML={{ __html: htmlContent }}
        />
      </Box>
    </Box>
  );
};

export default HTMLOptionPreview;
