// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";

const DatePickerOptionPreview = ({ option = {} }) => {
  const price = parseFloat(option?.values?.[0]?.price) > 0 ? parseFloat(option?.values?.[0]?.price).toFixed(2) : null;
  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          /* date picker */
          .date-picker {
            width: 100%;
            padding: 12px;
            border: 1px solid #8A8A8A;
            border-radius: 8px;
            font-size: 14px;
          }

          input,
          textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {option?.title || "Date picker"}
          {price && <span className="ef__option-value-price"> (+&nbsp;{price})</span>}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        {option?.values?.length > 0 &&
          option?.values.map((val) => (
            // eslint-disable-next-line react/button-has-type
            <input
              type="date"
              className="date-picker"
              key={val.elementId}
              placeholder="dd/mm/yyyy"
            />
          ))}
        {option.isRequired && <div className="error-message">{option?.title || "Date picker"} is required</div>}
      </Box>
    </Box>
  );
};

export default DatePickerOptionPreview;
