// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";
import { useState } from "react";

// @ts-ignore
const FileUploadOptionPreview = ({ option = {} }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const price = parseFloat(option?.values?.[0]?.price) > 0 ? parseFloat(option?.values?.[0]?.price).toFixed(2) : null;

  const handleOpenFileDialog = () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.multiple = false;
    fileInput.accept = option?.allowedFileTypes;
    fileInput.onchange = (e) => {
      setSelectedFiles([...e.target.files]);
    };
    fileInput.click();
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          /* File Upload */
          .file-upload {
            border: 1px solid #8A8A8A;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            width: 100%;
          }

          .file-upload input {
            font-size: 12px;
            line-height: 19px;
            font-weight: 500;
            color: #303030;
            background: #F0F0F2;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
          }

          .file-upload button {
            font-size: 12px;
            line-height: 19px;
            font-weight: 500;
            color: #303030;
            background: #F0F0F2;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
          }

          .file-upload p {
            font-size: 13px;
            line-height: 20px;
            color: #616161;
            margin: 0;
            padding-top: 8px;
          }
          input, textarea {
            outline: 0;
          }
          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }
          .ef__upload-content {
            width: 100%;
            display: flex;
            align-items: center;
            z-index: 2;
          }
          .ef__upload-content > img {
            width: 3rem;
            cursor: pointer;
          }
          .ef__upload-content > svg {
            margin-right: 2rem;
            cursor: pointer;
            z-index: 2;
          }
          .ef__upload-content .remove-file-button {
            background: none;
            padding: 0;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {option?.title || "File upload"}
          {price && <span className="ef__option-value-price"> (+&nbsp;{price})</span>}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        {option?.values?.length > 0 &&
          option?.values.map((val) => (
            // eslint-disable-next-line react/button-has-type
            <div
              className="file-upload"
              key={val.elementId}
            >
              {selectedFiles.length > 0 ? (
                <div>
                  {selectedFiles.map((file) => (
                    <div className="ef__upload-content">
                      <img
                        src={URL.createObjectURL(file)}
                        alt="img"
                      />
                      <span>{file.name}</span>

                      <button
                        type="button"
                        className="remove-file-button"
                        onClick={() => {
                          const newSelectedFiles = selectedFiles.filter((f) => f !== file);
                          setSelectedFiles(newSelectedFiles);
                        }}
                        aria-label={`Remove ${file.name}`}
                      >
                        <svg
                          fill="#000000"
                          width="1.5rem"
                          height="1.5rem"
                          viewBox="0 0 256 256"
                          id="Flat"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M202.82861,197.17188a3.99991,3.99991,0,1,1-5.65722,5.65624L128,133.65723,58.82861,202.82812a3.99991,3.99991,0,0,1-5.65722-5.65624L122.343,128,53.17139,58.82812a3.99991,3.99991,0,0,1,5.65722-5.65624L128,122.34277l69.17139-69.17089a3.99991,3.99991,0,0,1,5.65722,5.65624L133.657,128Z" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  <input
                    type="file"
                    name="file"
                    id={val.elementId}
                    className="ef__product-option-input field__input"
                    autoComplete="file"
                    accept={val.accept}
                    style={{ display: "none" }}
                  />
                  <button
                    type="button"
                    onClick={() => handleOpenFileDialog()}
                  >
                    Choose file
                  </button>
                  <p>or drop files to upload</p>
                </>
              )}
            </div>
          ))}
        {option.isRequired && <div className="error-message">{option?.title || "File upload"} is required</div>}
      </Box>
    </Box>
  );
};

export default FileUploadOptionPreview;
