/* eslint-disable jsx-a11y/label-has-associated-control */
// @ts-nocheck
/* eslint-disable react/prop-types */
import { BlockStack, Box } from "@shopify/polaris";
import { useEffect, useState } from "react";

const CheckboxOptionPreview = ({ option = {} }) => {
  const [checkedStates, setCheckedStates] = useState({});
  const [selectedCount, setSelectedCount] = useState(0);
  const [selectedValues, setSelectedValues] = useState([]);

  useEffect(() => {
    // Initialize checked states based on option.values
    const initialCheckedStates = {};
    option?.values?.forEach((val) => {
      initialCheckedStates[val.elementId] = val.checked || val.isDefault || false;
    });
    setCheckedStates(initialCheckedStates);
    setSelectedCount(Object.values(initialCheckedStates).filter(Boolean).length);

    setSelectedValues(option?.values?.filter((val) => val.checked || val.isDefault).map((val) => val.title) || []);
  }, [option?.values]);

  const handleCheckboxChange = (e, val) => {
    const { elementId, title } = val;
    const newCheckedStates = { ...checkedStates };
    const currentState = newCheckedStates[elementId];

    // If maximumSelectionCount is set and we're trying to check a new item
    if (option?.maximumSelectionCount && !currentState && selectedCount >= option.maximumSelectionCount) {
      return; // Don't allow checking if we've reached the maximum
    }

    newCheckedStates[elementId] = !currentState;
    setCheckedStates(newCheckedStates);
    setSelectedCount(Object.values(newCheckedStates).filter(Boolean).length);

    if (e.target.value.trim() !== "") {
      const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
      const finalValue = `${title} ${price ? `(+${price})` : ""}`;

      if (!selectedValues.includes(finalValue)) {
        setSelectedValues([...selectedValues, finalValue]);
      } else {
        setSelectedValues(selectedValues.filter((ti) => ti !== finalValue));
      }
    }
  };

  const getPrice = (price) => (parseFloat(price) > 0 ? parseFloat(price).toFixed(2) : null);

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          /* checkbox */
          .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }

          .checkbox-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
          }

          .checkbox-option input[type="checkbox"] {
            width: 16px;
            height: 16px;
          }

          .checkbox-option span {
            font-size: 13px;
            line-height: 20px;
            color: #616161;
          }

          input,
          textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }
        `}
      </style>
      <BlockStack gap="100">
        <label className="label">
          {option?.title || "Checkbox"}
          {option.isShowSelectedValues && selectedValues.length > 0 && (
            <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
          )}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        <div className="checkbox-group">
          {option?.values?.length > 0 &&
            option?.values.map((val, index) => (
              <>
                <label
                  className="checkbox-option"
                  key={val.elementId}
                >
                  <input
                    type="checkbox"
                    checked={checkedStates[val.elementId] || false}
                    disabled={
                      option?.maximumSelectionCount &&
                      option?.maximumSelectionCount > 0 &&
                      !checkedStates[val.elementId] &&
                      selectedCount >= option.maximumSelectionCount
                    }
                    onChange={(e) => handleCheckboxChange(e, val)}
                  />
                  <span>{val?.title || `Add-on ${index + 1}`}</span>
                  {getPrice(val?.price) && (
                    <span className="ef__option-value-price"> (+&nbsp;{getPrice(val?.price)})</span>
                  )}
                  {val?.description && (
                    <em className="ef__option-value-description ef__option-value-description-radio-button">
                      {val?.description}
                    </em>
                  )}
                </label>
                {val?.image && (
                  <img
                    alt=""
                    src={val?.image?.url}
                    width="48"
                    height="48"
                    style={{ margin: "0 2rem" }}
                    loading="lazy"
                  />
                )}
              </>
            ))}
          {option.isRequired && <div className="error-message">{option?.title || "Checkbox"} is required</div>}
        </div>
      </BlockStack>
    </Box>
  );
};

export default CheckboxOptionPreview;
