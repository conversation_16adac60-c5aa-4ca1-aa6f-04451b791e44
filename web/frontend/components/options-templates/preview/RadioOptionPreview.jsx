// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";
import { useEffect, useState } from "react";

// @ts-ignore
const RadioOptionPreview = ({ option = {} }) => {
  const [selectedValues, setSelectedValues] = useState([]);

  useEffect(() => {
    setSelectedValues(
      option?.values
        ?.filter((val) => val.isDefault || false)
        .map((val) => {
          const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
          return `${val.title} ${price ? `(+${price})` : ""}`;
        }) || []
    );
  }, [option?.values]);

  const handleRadioChange = (e, val) => {
    if (val.title.trim() !== "") {
      const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
      const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;

      if (!selectedValues.includes(finalValue)) {
        setSelectedValues([finalValue]);
      } else {
        setSelectedValues(selectedValues.filter((title) => title !== finalValue));
      }
    }
  };
  const getPrice = (price) => (parseFloat(price) > 0 ? parseFloat(price).toFixed(2) : null);
  const getIsChecked = (val) => {
    const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
    const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;
    return selectedValues.includes(finalValue);
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          /* radio group */
          .radio-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }

          .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
          }

          .radio-option input[type="radio"] {
            width: 16px;
            height: 16px;
            margin: 0;
          }

          .radio-option span {
            font-size: 13px;
            line-height: 20px;
            color: #616161;
          }

          input,
          textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }

          .radio-option.disabled {
            border-color: rgba(118, 118, 118, 0.3);
            cursor: not-allowed;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {option?.title || "Radio button"}
          {option.isShowSelectedValues && selectedValues.length > 0 && (
            <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
          )}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        <div className="radio-group">
          {option?.values?.length > 0 &&
            option?.values.map((val, index) => (
              <label
                className="radio-option"
                key={val.elementId}
              >
                <input
                  type="radio"
                  name="radio-group"
                  checked={getIsChecked(val)}
                  onChange={(e) => handleRadioChange(e, val)}
                />
                <span>{val?.title || `Label ${index + 1}`}</span>
                {getPrice(val?.price) && (
                  <span className="ef__option-value-price"> (+&nbsp;{getPrice(val?.price)})</span>
                )}
                <em className="ef__option-value-description ef__option-value-description-radio-button">
                  {val?.description}
                </em>
              </label>
            ))}
        </div>
        {option.isRequired && <div className="error-message">{option?.title || "Radio button"} is required</div>}
      </Box>
    </Box>
  );
};

export default RadioOptionPreview;
