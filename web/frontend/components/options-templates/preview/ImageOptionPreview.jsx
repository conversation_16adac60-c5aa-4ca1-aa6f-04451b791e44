// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import { useEffect, useState } from "react";
// @ts-ignore
const ImageOptionPreview = ({ option = {} }) => {
  const [selectedValues, setSelectedValues] = useState([]);
  const [checkedStates, setCheckedStates] = useState({});
  const [selectedCount, setSelectedCount] = useState(0);

  useEffect(() => {
    const initialCheckedStates = {};
    option?.values?.forEach((val) => {
      initialCheckedStates[val.elementId] = val.checked || val.isDefault || false;
    });
    setCheckedStates(initialCheckedStates);
    setSelectedCount(Object.values(initialCheckedStates).filter(Boolean).length);

    setSelectedValues(
      option?.values
        ?.filter((val) => val.checked || val.isDefault || false)
        .map((val) => {
          const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
          return `${val.title} ${price ? `(+${price})` : ""}`;
        }) || []
    );
  }, [option?.values]);

  const handleClick = (value) => {
    const { elementId } = value;
    const newCheckedStates = { ...checkedStates };
    const currentState = newCheckedStates[elementId];

    // If maximumSelectionCount is set and we're trying to check a new item
    if (option?.maximumSelectionCount && !currentState && selectedCount >= option.maximumSelectionCount) {
      return;
    }

    newCheckedStates[elementId] = !currentState;
    setCheckedStates(newCheckedStates);
    setSelectedCount(Object.values(newCheckedStates).filter(Boolean).length);

    if (value.title.trim() !== "") {
      const val = value.title.trim();
      const price = parseFloat(value?.price) > 0 ? parseFloat(value?.price).toFixed(2) : null;
      const finalValue = `${val} ${price ? `(+${price})` : ""}`;
      if (!selectedValues.includes(finalValue)) {
        const newSelectedValues = option?.isMultiSelect ? [...selectedValues, finalValue] : [finalValue];
        setSelectedValues(newSelectedValues);
      } else {
        setSelectedValues(selectedValues.filter((title) => title !== val));
      }
    }
  };

  const renderImage = (val) => {
    if (val?.imageFile) {
      return (
        <>
          <img
            src={URL.createObjectURL(val.imageFile)}
            alt="img"
          />
          {val?.title && (
            <span className="ef__hover-text">
              {val?.title}
              {val?.price && <span className="ef__option-value-price"> &nbsp;(+&nbsp;{val?.price})</span>}
            </span>
          )}
        </>
      );
    }
    if (val.image) {
      return (
        <>
          <img
            src={val?.image?.url}
            alt="img"
          />
          {val?.title && (
            <span className="ef__hover-text">
              {val?.title}
              {val?.price && <span className="ef__option-value-price"> &nbsp;(+&nbsp;{val?.price})</span>}
            </span>
          )}
        </>
      );
    }
    return (
      <>
        <svg
          width="48"
          height="48"
          viewBox="0 0 35 35"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M2.91667 0C2.14312 0 1.40125 0.307291 0.854272 0.854272C0.307291 1.40125 0 2.14312 0 2.91667V32.0833C0 32.8569 0.307291 33.5987 0.854272 34.1457C1.40125 34.6927 2.14312 35 2.91667 35H32.0833C32.8569 35 33.5987 34.6927 34.1457 34.1457C34.6927 33.5987 35 32.8569 35 32.0833V2.91667C35 2.14312 34.6927 1.40125 34.1457 0.854272C33.5987 0.307291 32.8569 0 32.0833 0H2.91667ZM12.6389 6.80556C14.7778 6.80556 16.5278 8.55556 16.5278 10.6944C16.5278 12.8333 14.7778 14.5833 12.6389 14.5833C10.5 14.5833 8.75 12.8333 8.75 10.6944C8.75 8.55556 10.5 6.80556 12.6389 6.80556ZM30.1369 31.1111H4.85528C4.05806 31.1111 3.61083 30.2167 4.0775 29.575L10.9861 21.6981C11.3556 21.2897 11.9972 21.2897 12.3861 21.6786L15.5556 25.2778L21.5056 15.9814C21.5969 15.847 21.7205 15.7376 21.865 15.6633C22.0095 15.5889 22.1703 15.552 22.3328 15.5559C22.4953 15.5598 22.6542 15.6043 22.795 15.6854C22.9358 15.7665 23.054 15.8817 23.1389 16.0203L30.9925 29.6528C31.3425 30.3139 30.8758 31.1111 30.1369 31.1111Z"
            fill="#B6BCC1"
          />
        </svg>
        {val?.title && (
          <span className="ef__hover-text">
            {val?.title}
            {val?.price && <span className="ef__option-value-price"> &nbsp;(+&nbsp;{val?.price})</span>}
          </span>
        )}
      </>
    );
  };

  const getIsSelected = (val) => {
    const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
    const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;
    return selectedValues.includes(finalValue);
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          /* image swatch */
          .image-swatches {
            padding: 0 10px 10px 0px;
          }
          .image-swatches>.Polaris-InlineStack {
            row-gap: 22px;
          }

          .image-swatch {
            width: 48px;
            height: 48px;
            border-radius: 4px;
            transition: transform 0.2s ease-in-out;
            font-size: 0.8em;
          }

          .image-swatch:hover {
            transform: scale(1.2);
            z-index: 1;
          }

          .image-swatch img {
            max-width: 48px;
            height: 100%;
            object-fit: cover;
          }

          .image-swatch svg {
            max-width: 48px;
            height: 100%;
          }

          input,
          textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 12px;
            
          }

          .image-swatch.disabled {
            border-color: rgba(118, 118, 118, 0.3);
            cursor: not-allowed;
          }
          .ef__hover-text {
            visibility: hidden;
            white-space: nowrap;
            position: absolute;
            bottom: -16px;
            left: 2px;
            z-index: 999;
          }
          .image-swatch:hover .ef__hover-text {
            visibility: visible;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {
            // @ts-ignore
            option?.title || "Image swatch"
          }
          {option.isShowSelectedValues && selectedValues.length > 0 && (
            <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
          )}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        <BlockStack gap="200">
          <div className="image-swatches">
            <InlineStack gap="200">
              {
                // @ts-ignore
                option?.values?.length > 0 &&
                  // @ts-ignore
                  option?.values.map((val) => (
                    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                    <div
                      className={`image-swatch ${
                        option?.maximumSelectionCount &&
                        option?.maximumSelectionCount > 0 &&
                        !checkedStates[val.elementId] &&
                        selectedCount >= (option.maximumSelectionCount ?? 1)
                          ? ""
                          : "disabled"
                      }`}
                      key={val.elementId}
                      style={{
                        cursor: "pointer",
                        boxShadow: getIsSelected(val) ? "0 0 0 2px #000" : "none",
                      }}
                      onClick={() => handleClick(val)}
                    >
                      {renderImage(val)}
                    </div>
                  ))
              }
            </InlineStack>
          </div>
          {option.isRequired && <div className="error-message">{option?.title || "Image swatch"} is required</div>}
        </BlockStack>
      </Box>
    </Box>
  );
};

export default ImageOptionPreview;
