// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import { useEffect, useState } from "react";

// @ts-ignore
const ColorSwatchPreview = ({ option = {} }) => {
  const [selectedValues, setSelectedValues] = useState([]);
  const [checkedStates, setCheckedStates] = useState({});
  const [selectedCount, setSelectedCount] = useState(0);

  useEffect(() => {
    const initialCheckedStates = {};
    option?.values?.forEach((val) => {
      initialCheckedStates[val.elementId] = val.checked || val.isDefault || false;
    });
    setCheckedStates(initialCheckedStates);
    setSelectedCount(Object.values(initialCheckedStates).filter(Boolean).length);

    setSelectedValues(
      option?.values
        ?.filter((val) => val.checked || val.isDefault || false)
        .map((val) => {
          const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
          return `${val.title} ${price ? `(+${price})` : ""}`;
        }) || []
    );
  }, [option?.values]);

  const handleClick = (value) => {
    const { elementId } = value;
    const newCheckedStates = { ...checkedStates };
    const currentState = newCheckedStates[elementId];

    // If maximumSelectionCount is set and we're trying to check a new item
    if (option?.maximumSelectionCount && !currentState && selectedCount >= option.maximumSelectionCount) {
      return;
    }

    newCheckedStates[elementId] = !currentState;
    setCheckedStates(newCheckedStates);
    setSelectedCount(Object.values(newCheckedStates).filter(Boolean).length);

    if (value.title.trim() !== "") {
      const val = value.title.trim();
      const price = parseFloat(value?.price) > 0 ? parseFloat(value?.price).toFixed(2) : null;
      const finalValue = `${val} ${price ? `(+${price})` : ""}`;

      if (!selectedValues.includes(finalValue)) {
        const newSelectedValues = option?.isMultiSelect ? [...selectedValues, finalValue] : [finalValue];
        setSelectedValues(newSelectedValues);
      } else {
        setSelectedValues(selectedValues.filter((title) => title !== val));
      }
    }
  };

  const getPrice = (price) => (parseFloat(price) > 0 ? parseFloat(price).toFixed(2) : null);

  const getIsSelected = (val) => {
    const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
    const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;
    return selectedValues.includes(finalValue);
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          .color-swatches {
            display: flex;
            gap: 8px;
            padding: 0 25px 10px 0px;
          }
          .color-swatches> .Polaris-InlineStack {
            row-gap: 22px;
          }

          .color-swatch {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            cursor: pointer;
          }

          .ef__hover-text {
            white-space: nowrap;
            position: absolute;
            bottom: -20px;
            display: none;
          }
          .color-swatch-container {
            position: relative;
          }
          .color-swatch-container:hover .color-swatch {
            box-shadow: rgb(0, 0, 0) 0px 0px 0px 2px !important;
            border-radius: 8px;
          }
          .color-swatch-container:hover .ef__hover-text {
            transition: opacity 0.2s ease-in-out;
            display: block;
          }

          input,
          textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }

          .color-swatch.disabled {
            border-color: rgba(118, 118, 118, 0.3);
            cursor: not-allowed;
          }
          
        `}
      </style>
      <BlockStack gap="200">
        <Box gap="200">
          <label className="label">
            {option?.title || "Color swatch"}
            {option.isShowSelectedValues && selectedValues.length > 0 && (
              <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
            )}
            {option?.isRequired && <span className="required-field"> *</span>}
          </label>
          {option?.description && (
            <div className="caption ef__option-description ef__option-description-text-box">
              {option?.description ?? ""}
            </div>
          )}
          <div className="color-swatches">
            <InlineStack gap="200">
              {option?.values?.length > 0 &&
                option?.values.map((val) => (
                  <div className="color-swatch-container">
                    {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
                    <div
                      className={`color-swatch ${
                        option?.maximumSelectionCount &&
                        option?.maximumSelectionCount > 0 &&
                        !checkedStates[val.elementId] &&
                        selectedCount >= option.maximumSelectionCount
                          ? ""
                          : "disabled"
                      }`}
                      style={{
                        cursor: "pointer",
                        boxShadow: getIsSelected(val) ? "0 0 0 2px #000" : "none",
                        background: val?.color || "#808080",
                      }}
                      onClick={() => handleClick(val)}
                    />
                    <span className="ef__hover-text">
                      {val?.title}
                      {getPrice(val?.price) && (
                        <span className="ef__option-value-price"> (+&nbsp;{getPrice(val?.price)})</span>
                      )}
                    </span>
                  </div>
                ))}
            </InlineStack>
          </div>
        </Box>

        {option.isRequired && <div className="error-message">{option?.title || "Color swatch"} is required</div>}
      </BlockStack>
    </Box>
  );
};

export default ColorSwatchPreview;
