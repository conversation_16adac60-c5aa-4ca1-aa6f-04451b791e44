// @ts-nocheck
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";
import { useState } from "react";

// @ts-ignore
const TextboxOptionPreview = ({ option = {} }) => {
  const [valueCount, setValueCount] = useState(0);
  const price = parseFloat(option?.values?.[0]?.price) > 0 ? parseFloat(option?.values?.[0]?.price).toFixed(2) : null;
  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          .date-picker {
            width: 100%;
            padding: 12px;
            border: 1px solid #8A8A8A;
            border-radius: 8px;
            font-size: 14px;
          }

          /* text box */
          .required-field {
            position: relative;
          }

          .required-field::after {
            content: '*';
            color: #EA3323;
            margin-left: 4px;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }

          .ef__character-counter {
            position: absolute;
            bottom: ${option?.isRequired ? "2.5rem" : "1.5rem"};
            right: 1.5rem;
            opacity: 0.4;
            font-size: 12px;
          }
        `}
      </style>
      <Box>
        <label
          htmlFor="textbox-input"
          className={`label ${option?.isRequired ? "required-field" : ""}`}
        >
          {option?.title || "Text box"}
          {price && <span className="ef__option-value-price"> (+&nbsp;{price})</span>}
        </label>
        {option?.values?.length > 0 &&
          option?.values.map((val) => (
            <>
              {option?.description && (
                <div className="caption ef__option-description ef__option-description-text-box">
                  {option?.description ?? ""}
                </div>
              )}
              <input
                type="text"
                key={val.elementId}
                onChange={(e) => setValueCount(e.target.value.length)}
                className="date-picker"
                placeholder={option?.placeholderText ?? ""}
                minLength={option?.minimumValue ?? undefined}
                maxLength={option?.maximumValue ?? undefined}
              />
              {option?.maximumValue > 0 && (
                <span className="ef__character-counter">
                  {valueCount}/{option?.maximumValue ?? ""}
                </span>
              )}
              {option.isRequired && <div className="error-message">{option?.title || "Text box"} is required</div>}
            </>
          ))}
      </Box>
    </Box>
  );
};

export default TextboxOptionPreview;
