// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box } from "@shopify/polaris";
import { useEffect, useState } from "react";

// @ts-ignore
const DropdownOptionPreview = ({ option = {} }) => {
  const [selectedValues, setSelectedValues] = useState([]);
  const price = parseFloat(option?.values?.[0]?.price) > 0 ? parseFloat(option?.values?.[0]?.price).toFixed(2) : null;
  useEffect(() => {
    setSelectedValues(option?.values?.filter((val) => val.checked || val.isDefault).map((val) => val.title) || []);
  }, [option?.values]);

  const handleChange = (e) => {
    if (e.target.value.trim() !== "") {
      const val = e.target.value.trim();
      if (val === `Choose ${option?.title || "Dropdown"}`) {
        setSelectedValues([]);
      } else if (!selectedValues.includes(val)) {
        setSelectedValues([val]);
      } else {
        setSelectedValues(selectedValues.filter((title) => title !== val));
      }
    }
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }

          .dropdown {
            width: 100%;
            padding: 12px;
            border: 1px solid #8A8A8A;
            border-radius: 8px;
            cursor: pointer;
            background: transparent;
          }

          input, textarea {
            outline: 0;
          }

          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {option?.title || "Dropdown"}
          {option.isShowSelectedValues && selectedValues.length > 0 && (
            <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
          )}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        <select
          className="dropdown"
          onChange={(e) => handleChange(e)}
        >
          <option key="choose-option">{`Choose ${option?.title || "Dropdown"}`}</option>
          {option?.values?.length > 0 &&
            option?.values.map((val, index) => (
              <option
                selected={selectedValues.includes(val.title)}
                key={val.elementId}
              >
                {val?.title || `Dropdown ${index + 1}`}{" "}
                {price && <span className="ef__option-value-price"> (+&nbsp;{price})</span>}
              </option>
            ))}
        </select>
        {option.isRequired && <div className="error-message">{option?.title || "Dropdown"} is required</div>}
      </Box>
    </Box>
  );
};

export default DropdownOptionPreview;
