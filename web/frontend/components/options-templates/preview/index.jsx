/* eslint-disable react/jsx-props-no-spreading */
// @ts-nocheck
/* eslint-disable react/prop-types */
import { BlockStack, Card, Text } from "@shopify/polaris";
// eslint-disable-next-line import/no-cycle
import ButtonOptionPreview from "./ButtonOptionPreview";
import CheckboxOptionPreview from "./CheckboxOptionPreview";
import ColorSwatchPreview from "./ColorSwatchPreview";
import DatePickerOptionPreview from "./DatePickerOptionPreview";
import DropdownOptionPreview from "./DropdownOptionPreview";
import FileUploadOptionPreview from "./FileUploadOptionPreview";
import HTMLOptionPreview from "./HTMLOptionPreview";
import ImageOptionPreview from "./ImageOptionPreview";
import MultiLineOptionPreview from "./MultiLineOptionPreview";
import NumberFieldOptionPreview from "./NumberFieldOptionPreview";
import PreviewOptionSkeleton from "./PreviewOptionSkeleton";
import RadioOptionPreview from "./RadioOptionPreview";
import TextboxOptionPreview from "./TextboxOptionPreview";

export const productOptionsPreviewMap = {
  Checkbox: (props) => <CheckboxOptionPreview {...props} />,
  Dropdown: (props) => <DropdownOptionPreview {...props} />,
  "Image swatch": (props) => <ImageOptionPreview {...props} />,
  "Radio button": (props) => <RadioOptionPreview {...props} />,
  Button: (props) => <ButtonOptionPreview {...props} />,
  "Text box": (props) => <TextboxOptionPreview {...props} />,
  "Multi-line text box": (props) => <MultiLineOptionPreview {...props} />,
  "Number field": (props) => <NumberFieldOptionPreview {...props} />,
  "Date picker": (props) => <DatePickerOptionPreview {...props} />,
  "Color swatch": (props) => <ColorSwatchPreview {...props} />,
  "File upload": (props) => <FileUploadOptionPreview {...props} />,
  HTML: (props) => <HTMLOptionPreview {...props} />,
};

/**
 *
 * @param {{option: Partial<IProductOption>}} props
 * @returns
 */
const ProductOptionPreviewCard = ({ option }) => {
  const PreviewComponent = productOptionsPreviewMap[option.type] || (() => null);

  return (
    <Card>
      <BlockStack gap="400">
        <Text
          as="h2"
          variant="headingMd"
        >
          Preview
        </Text>
        <PreviewOptionSkeleton />
        <PreviewComponent option={option} />
      </BlockStack>
    </Card>
  );
};

export default ProductOptionPreviewCard;
