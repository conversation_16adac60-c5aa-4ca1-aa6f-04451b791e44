// @ts-nocheck
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable react/prop-types */
import { Box, InlineStack } from "@shopify/polaris";
import { useEffect, useState } from "react";

// @ts-ignore
const ButtonOptionPreview = ({ option = {} }) => {
  const [selectedValues, setSelectedValues] = useState([]);

  useEffect(() => {
    setSelectedValues(
      option?.values
        ?.filter((val) => val.checked || val.isDefault || false)
        .map((val) => {
          const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
          return `${val.title} ${price ? `(+${price})` : ""}`;
        }) || []
    );
  }, [option?.values]);

  const handleButtonClick = (val) => {
    const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
    const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;

    if (val.title.trim() !== "") {
      if (!selectedValues.includes(finalValue)) {
        setSelectedValues([finalValue]);
      } else {
        setSelectedValues(selectedValues.filter((title) => title !== finalValue));
      }
    }
  };
  const getPrice = (price) => (parseFloat(price) > 0 ? parseFloat(price).toFixed(2) : null);
  const getIsActive = (val) => {
    const price = parseFloat(val?.price) > 0 ? parseFloat(val?.price).toFixed(2) : null;
    const finalValue = `${val.title} ${price ? `(+${price})` : ""}`;

    return selectedValues.includes(finalValue);
  };

  return (
    <Box>
      <style>
        {`
          .label {
            display: block;
            margin-bottom: 4px;
            font-size: 13px;
            line-height: 20px;
            color: #303030;
            font-weight: bold;
          }
          input,
          textarea {
            outline: 0;
          }
          /* Button */
          .color-buttons {
            display: flex;
            gap: 12px;
          }
          .color-button {
            padding: 8px 20px;
            border-radius: 12px;
            border: 1px solid #3F3F3F;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            // min-height: 40px;
          }
          .primary {
            background: #3F3F3F;
            color: #FFFFFF;
          }
          .secondary {
            background: transparent;
            color: #3F3F3F;
          }
          .error-message {
            color: #EA3323;
            font-size: 13px;
            line-height: 20px;
            margin-top: 4px;
          }
          .color-button.disabled {
            border-color: rgba(118, 118, 118, 0.3);
            cursor: not-allowed;
          }
        `}
      </style>
      <Box>
        <label className="label">
          {option?.title || "Buttons"}
          {option.isShowSelectedValues && selectedValues.length > 0 && (
            <span className="ef__option-selected-values"> - {selectedValues.join(", ")}</span>
          )}
          {option?.isRequired && <span className="required-field"> *</span>}
        </label>
        {option?.description && (
          <div className="caption ef__option-description ef__option-description-text-box">
            {option?.description ?? ""}
          </div>
        )}
        <div className="color-buttons">
          <InlineStack gap="200">
            {option?.values?.length > 0 &&
              option?.values.map((val, index) => (
                <InlineStack
                  gap="100"
                  align="center"
                  blockAlign="center"
                >
                  {/* eslint-disable-next-line react/button-has-type */}
                  <button
                    key={val.elementId}
                    className={`color-button ${getIsActive(val) ? "primary" : "secondary"}`}
                    onClick={() => handleButtonClick(val)}
                  >
                    {val?.title || `button ${index + 1}`}
                    {getPrice(val?.price) && (
                      <span className="ef__option-value-price"> (+&nbsp;{getPrice(val?.price)})</span>
                    )}
                  </button>
                  <em className="ef__option-value-description ef__option-value-description-radio-button">
                    {val?.description}
                  </em>
                </InlineStack>
              ))}
          </InlineStack>
        </div>
        {option.isRequired && <div className="error-message">{option?.title || "Buttons"} is required</div>}
      </Box>
    </Box>
  );
};

export default ButtonOptionPreview;
