/* eslint-disable react/prop-types */

import { Mo<PERSON>, TitleBar } from "@shopify/app-bridge-react";
import { BlockStack, Box, Card, Grid, InlineStack, Text } from "@shopify/polaris";
import { productOptionTypes } from "../pageComponents/EditOptionPage";
import { useStore } from "../providers/StoreProvider";
import ButtonOptionIcon from "./icons/ButtonOptionIcon";
import CheckboxOptionIcon from "./icons/CheckboxOptionIcon";
import ColorSwatchOptionIcon from "./icons/ColorSwatchOptionIcon";
import DateOptionIcon from "./icons/DateOptionIcon";
import DropdownOptionIcon from "./icons/DropdownOptionIcon";
import FileUploadOptionIcon from "./icons/FileUploadOptionIcon";
import HtmlOptionIcon from "./icons/HtmlOptionIcon";
import ImageSwatchOptionIcon from "./icons/ImageSwatchOptionIcon";
import MultiLineOptionIcon from "./icons/MultiLineOptionIcon";
import NumberOptionIcon from "./icons/NumberOptionIcon";
import RadioOptionIcon from "./icons/RadioOptionIcon";
import TextboxOptionIcon from "./icons/TextboxOptionIcon";

export const productOptionNewIcons = {
  Checkbox: <CheckboxOptionIcon />,
  Dropdown: <DropdownOptionIcon />,
  "Image swatch": <ImageSwatchOptionIcon />,
  "Color swatch": <ColorSwatchOptionIcon />,
  "Radio button": <RadioOptionIcon />,
  Button: <ButtonOptionIcon />,
  "Text box": <TextboxOptionIcon />,
  "Multi-line text box": <MultiLineOptionIcon />,
  "Number field": <NumberOptionIcon />,
  "Date picker": <DateOptionIcon />,
  "File upload": <FileUploadOptionIcon />,
  HTML: <HtmlOptionIcon />,
};

/**
 * @param {{
 *   open: boolean,
 *   onHideCallback?: () => void,
 *   onCreate: (type: string) => void,
 * }} props
 */
const OptionsTemplateModal = ({ open, onHideCallback, onCreate }) => {
  const { shopDetails } = useStore();

  return (
    <Modal
      open={open}
      onHide={onHideCallback}
    >
      <TitleBar title="Select an option" />
      <Box padding="400">
        <Grid columns={{ xs: 1, sm: 4, md: 4, lg: 4, xl: 4 }}>
          {productOptionTypes.map((type) => (
            <button
              type="button"
              key={`${type}-${Math.random()}`}
              onClick={() => onCreate(type)}
              style={{
                all: "unset",
                cursor: "pointer",
                display: "block",
                width: "100%",
              }}
            >
              <Card padding="300">
                <BlockStack gap="200">
                  <Box
                    background="bg-surface-secondary"
                    borderRadius="full"
                    width="fit-content"
                    padding="300"
                  >
                    <InlineStack>{productOptionNewIcons[type]}</InlineStack>
                  </Box>

                  <Text
                    as="p"
                    variant="bodyMd"
                  >
                    {shopDetails?.isFeatureLimited && type === "File upload" ? `${type} (Upgrade plan)` : type}
                  </Text>
                </BlockStack>
              </Card>
            </button>
          ))}
        </Grid>
      </Box>
    </Modal>
  );
};

export default OptionsTemplateModal;
