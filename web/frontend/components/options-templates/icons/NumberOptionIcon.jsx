const NumberOptionIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_4945_30214)">
      <path
        d="M8.3029 6.7998H5.73438V8.08407H7.01864V11.9368H5.73438V13.2211H9.58716V11.9368H8.3029V6.7998Z"
        fill="#8A8A8A"
      />
      <path
        d="M10.6797 8.08407H12.6061V9.36833H10.6797V13.2211H13.8903V11.9368H11.9639V10.6526H13.8903V6.7998H10.6797V8.08407Z"
        fill="#8A8A8A"
      />
      <path
        d="M17.5313 5.9705C17.7901 5.9705 18 5.76063 18 5.50175V2.46875C18 2.20987 17.7901 2 17.5313 2H14.4983C14.2394 2 14.0295 2.20987 14.0295 2.46875V3.5165H5.9705V2.46875C5.9705 2.20987 5.76063 2 5.50175 2H2.46875C2.20987 2 2 2.20987 2 2.46875V5.50175C2 5.76063 2.20987 5.9705 2.46875 5.9705H3.5165V14.0295H2.46875C2.20987 14.0295 2 14.2394 2 14.4983V17.5313C2 17.7901 2.20987 18 2.46875 18H5.50175C5.76063 18 5.9705 17.7901 5.9705 17.5313V16.4835H14.0295V17.5313C14.0295 17.7901 14.2394 18 14.4983 18H17.5313C17.7901 18 18 17.7901 18 17.5313V14.4983C18 14.2394 17.7901 14.0295 17.5313 14.0295H16.4835V5.9705H17.5313ZM14.967 2.9375H17.0625V5.033H14.967L14.967 2.9375ZM2.9375 2.9375H5.033V5.033H2.9375V2.9375ZM5.033 17.0625H2.9375V14.967H5.033V17.0625ZM17.0625 17.0625H14.967V14.967H17.0625V17.0625ZM15.546 14.0295H14.4983C14.2394 14.0295 14.0295 14.2394 14.0295 14.4983V15.546H5.9705V14.4983C5.9705 14.2394 5.76063 14.0295 5.50175 14.0295H4.454V5.9705H5.50175C5.76063 5.9705 5.9705 5.76063 5.9705 5.50175V4.454H14.0295V5.50175C14.0295 5.76063 14.2394 5.9705 14.4983 5.9705H15.546V14.0295Z"
        fill="#8A8A8A"
      />
    </g>
    <defs>
      <clipPath id="clip0_4945_30214">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="translate(2 2)"
        />
      </clipPath>
    </defs>
  </svg>
);

export default NumberOptionIcon;
