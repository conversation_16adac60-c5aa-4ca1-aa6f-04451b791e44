import { node, string as stringProp } from 'prop-types';

import { Text } from '@shopify/polaris';

/**
 * @param {{
 *   description: string,
 *   children?: React.ReactNode,
 * }} props
 */
export default function InputDescription({ description, children }) {
  return (
    <div style={{ marginTop: '0.25rem' }}>
      <Text tone="subdued" as="p">
        {description}
      </Text>

      <div style={{ marginTop: '0.2rem' }}>{children}</div>
    </div>
  );
}

InputDescription.propTypes = {
  description: stringProp.isRequired,
  children: node,
};
