import { Crisp } from "crisp-sdk-web";
import { array, bool, func, object } from "prop-types";
import { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState } from "react";

import {
  Banner,
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Card,
  EmptyState,
  InlineGrid,
  InlineStack,
  Layout,
  Modal,
  Page,
  Text,
  TextField,
  Tooltip,
} from "@shopify/polaris";
import { DeleteIcon, DuplicateIcon } from "@shopify/polaris-icons";

/* eslint-disable import/no-relative-packages */
import { useAppBridge } from "@shopify/app-bridge-react";
import { defaultActionType, defaultConditionRelation, defaultProductSelectionMethod } from "../../../defaults";
import AddToProduct from "../AddToProduct";
import SaveBarWrapper, { saveBarId } from "../common/SaveBarWrapper";
import ScheduleCall from "../common/ScheduleCall";
import CreateOptionButton from "../CreateOptionButton";
import HelpTooltip from "../HelpTooltip";
import OptionSelectModal from "../OptionSelectModal";
import ProductOptionList from "../ProductOptionList";
import PromptModal from "../PromptModal";
import { useAppErrors } from "../providers/ErrorProvider";
import RuleEditor from "../RuleEditor";
import EditOptionPage from "./EditOptionPage";

/**
 * @param {ProductOptionSetInput | undefined} optionSetInput
 * @param {IProductOption[]} options
 * @param {boolean | undefined} isOnboarding
 * @param {boolean} [isSkip]
 *
 * @returns {{ optionSet: IProductOptionSet } | { errors: string[] }}
 */
function optionSetFromInput(optionSetInput, options, isOnboarding, isSkip) {
  const errors = [
    ...(!optionSetInput?.title ? ["title"] : []),
    ...(!optionSetInput?.optionIds?.length ? ["options"] : []),
    ...(isOnboarding &&
    !isSkip &&
    !optionSetInput?.productSelectionValues?.length &&
    optionSetInput?.productSelectionMethod === "collection"
      ? ["products"]
      : []),
    ...(isOnboarding &&
    !isSkip &&
    !optionSetInput?.productIds?.length &&
    optionSetInput?.productSelectionMethod === "manual"
      ? ["products"]
      : []),
  ];

  return !errors.length && optionSetInput?.title && optionSetInput?.optionIds?.length
    ? {
        optionSet: {
          ...optionSetInput,
          id: optionSetInput.id || 0,
          title: optionSetInput.title,
          optionIds: optionSetInput.optionIds,
          productIds: optionSetInput.productIds || [],
          productSelectionValues: optionSetInput.productSelectionValues?.flatMap((value) =>
            value ? value.trim() : []
          ),
          rules: optionSetInput.rules?.flatMap((rule) =>
            rule.conditions && rule.name
              ? {
                  ...rule,
                  name: rule.name,
                  conditions:
                    rule.conditions.flatMap((condition) =>
                      condition.optionId && condition.values?.length
                        ? {
                            ...condition,
                            optionId: condition.optionId,
                            relation: condition.relation || defaultConditionRelation,
                            values: condition.values,
                          }
                        : []
                    ) || [],
                  actions:
                    rule.actions?.flatMap((action) => {
                      const actionOption = options.find((option) => option.id === action.optionId);
                      const isValueLess = !actionOption?.values.some((value) => value.title);

                      return action.optionId && (action.values?.length || isValueLess)
                        ? {
                            ...action,
                            type: action.type || defaultActionType,
                            optionId: action.optionId,
                            values: action.values || [],
                          }
                        : [];
                    }) || [],
                }
              : []
          ),
        },
      }
    : { errors };
}

/**
 * @param {Partial<IProductOptionSet> | undefined} optionSet
 *
 * @returns {ProductOptionSetInput}
 */
function optionSetToInput(optionSet) {
  const existingRules =
    optionSet?.rules?.map((rule) => ({
      ...rule,
      name: rule.name,
      conditions: rule.conditions.length ? rule.conditions : [{}],
      actions: rule.actions.length ? rule.actions : [{}],
    })) || [];

  return {
    ...optionSet,
    productIds: optionSet?.productIds?.map((id) => id.toString()),
    productSelectionMethod: optionSet?.productSelectionMethod || defaultProductSelectionMethod,
    rules: existingRules.length ? existingRules : [],
  };
}

const EditOptionSetPage = forwardRef(
  (
    /**
     * @type {{
     *   optionSet: Partial<IProductOption>,
     *   options: IProductOption[],
     *   onClose?: () => void | undefined,
     *   onEdit: (optionSet: IProductOptionSet) => void,
     *   onDelete?: (optionSetId: number | undefined) => void,
     *   onOptionEditingChanged?: (isEditing: boolean) => void,
     *   onOptionEdit: (option: IProductOption) => void,
     *   onOptionDelete: (optionId: number | undefined) => void,
     *   isOnboarding?: boolean,
     *   isDuplicateDisabled?: boolean,
     *   shopDetails: IShopDetails,
     * }} props
     */
    {
      optionSet: inputOptionSet,
      options,
      onClose,
      onEdit,
      onDelete,
      onOptionEditingChanged,
      onOptionEdit,
      onOptionDelete,
      isOnboarding,
      isDuplicateDisabled,
      shopDetails,
    },
    ref
  ) => {
    const { appErrors, setAppErrorByKey, resetAppErrorByKey } = useAppErrors();
    const shopify = useAppBridge();

    const initialOptionSet = useMemo(() => optionSetToInput(inputOptionSet), [inputOptionSet]);

    const [optionSet, setOptionSet] = useState(/** @type {ProductOptionSetInput} */ (initialOptionSet));
    const [savedOptionSet, setSavedOptionSet] = useState(optionSet);
    const [isSaveLoading, setSaveLoading] = useState(false);
    const [isDeleteLoading, setDeleteLoading] = useState(false);
    const [errors, setErrors] = useState(/** @type {string[]} */ ([]));

    /** @type {TypedStateOptional<string>} */
    const [tutorialVideoUrl, setTutorialVideoUrl] = useState();

    /** @type {TypedStateOptional<'delete' | 'discard'>} */
    const [openModalType, setOpenModalType] = useState();

    /** @type {TypedStateOptional<Partial<IProductOption>>} */
    const [editedOption, setEditedOption] = useState();

    const [isOptionSelectModalOpen, setOptionSelectModalOpen] = useState(false);

    useImperativeHandle(ref, () => ({ saveOptionSet }));

    const handleBannerErrors = useCallback(
      (changedOptionSet) => {
        if (isOnboarding) {
          let newErrors = [];

          changedOptionSet.errors.forEach((error) => {
            if (!newErrors.some((e) => e.key === error)) {
              // Avoid duplicates
              if (error === "products") {
                newErrors.push({
                  key: "products",
                  message: "Add at least 1 product to continue",
                });
              }
              if (error === "options") {
                newErrors.push({
                  key: "options",
                  message: "Add at least 1 option to save the option set",
                });
              }
            }
          });

          setAppErrorByKey("optionSet", newErrors); // Update errors
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [isOnboarding, appErrors, setAppErrorByKey]
    );

    /**
     * @param {boolean} [isSkip]
     */
    const saveOptionSet = async (isSkip) => {
      setErrors([]);
      setSaveLoading(true);

      const changedOptionSet = optionSetFromInput(optionSet, options, isOnboarding, isSkip);

      if ("errors" in changedOptionSet) {
        handleBannerErrors(isSkip ? { errors: [] } : changedOptionSet);
        setErrors((oldErrors) => [...oldErrors, ...changedOptionSet.errors]);
        setSaveLoading(false);
        return false;
      }

      const response = await fetch("/api/options/save", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ optionSets: [changedOptionSet.optionSet] }),
      });

      if (response.ok) {
        /** @type {OptionItems} */
        const updatedOptionSets = await response.json();
        const updatedOptionSet = updatedOptionSets.optionSets?.[0];

        if (updatedOptionSet) {
          const updatedOptionSetInput = optionSetToInput(updatedOptionSet);

          setOptionSet(updatedOptionSetInput);
          setSavedOptionSet(updatedOptionSetInput);

          onEdit(updatedOptionSet);
          shopify.toast.show("Option set saved");
        }
      }

      setSaveLoading(false);
      if (!isOnboarding) {
        shopify.saveBar.hide(saveBarId);
      }
      return true;
    };

    const deleteOptionSet = async () => {
      setDeleteLoading(true);

      const response = await fetch("/api/options/delete", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ optionSetIds: [optionSet.id] }),
      });

      if (response.ok) {
        Crisp.session.pushEvent("delete_option_set");
        onDelete?.(optionSet.id);

        return;
      }

      setDeleteLoading(false);
    };

    useEffect(() => {
      onOptionEditingChanged?.(!!editedOption);
    }, [editedOption, onOptionEditingChanged]);

    useEffect(() => {
      if (editedOption && isOnboarding) {
        Crisp.session.pushEvent("add_option_onboarding");
      }
    }, [editedOption, isOnboarding]);

    useEffect(() => {
      const handler = setTimeout(() => {
        if (optionSet !== savedOptionSet && !isOnboarding) {
          shopify.saveBar.show(saveBarId);
        } else {
          shopify.saveBar.hide(saveBarId);
        }
      }, 200);

      return () => clearTimeout(handler);
    }, [optionSet, savedOptionSet, isOnboarding, shopify]);

    if (editedOption) {
      return (
        <EditOptionPage
          option={editedOption}
          onClose={() => setEditedOption(undefined)}
          onEdit={(option) => {
            onOptionEdit(option);

            if (!optionSet.optionIds?.includes(option.id)) {
              setOptionSet((oldOptionSet) => ({
                ...oldOptionSet,
                optionIds: [...(oldOptionSet.optionIds || []), option.id],
              }));
            }

            setEditedOption(undefined);
            const newErrors = errors.filter((error) => error !== "options");
            setErrors(newErrors);
            handleBannerErrors({ errors: newErrors });
          }}
          onDelete={(optionId) => {
            if (optionId && optionSet.optionIds?.includes(optionId)) {
              setOptionSet((oldOptionSet) => ({
                ...oldOptionSet,
                optionIds: oldOptionSet.optionIds?.filter((oldOptionId) => oldOptionId !== optionId),
              }));
            }

            onOptionDelete(optionId);
            setEditedOption(undefined);
          }}
          shopDetails={shopDetails}
        />
      );
    }

    const pageContent = (
      <>
        {!isOnboarding && errors.length > 0 && errors.some((err) => err === "options" || err === "products") && (
          <Box paddingBlockEnd="400">
            <Banner
              title="Action required"
              tone="critical"
              onDismiss={() => setErrors([])}
            >
              <Box paddingInlineStart="200">
                {errors.includes("options") && <li>Add at least 1 option to save the option set</li>}
                {errors.includes("products") && <li>Add at least 1 product to continue</li>}
              </Box>
            </Banner>
          </Box>
        )}

        <Layout>
          <Layout.Section>
            <BlockStack gap="400">
              <Card>
                <InlineGrid
                  columns="auto 0.35fr"
                  gap="200"
                >
                  <TextField
                    label="Option set title"
                    value={optionSet?.title}
                    error={errors.includes("title") ? "Option set title is required" : undefined}
                    onChange={(newTitle) => setOptionSet((oldOptionSet) => ({ ...oldOptionSet, title: newTitle }))}
                    helpText="Not visible to customers."
                    autoComplete="option-set-title"
                  />

                  <TextField
                    type="number"
                    label="Rank"
                    min={1}
                    value={(optionSet?.rank || 1).toString()}
                    onChange={(newRank) => setOptionSet((oldOptionSet) => ({ ...oldOptionSet, rank: Number(newRank) }))}
                    autoComplete="option-set-rank"
                  />
                </InlineGrid>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <InlineStack
                    blockAlign="center"
                    gap="400"
                  >
                    <HelpTooltip
                      title="Options"
                      content="Every option set consists of one or more options. One option is a specific thing the customer can customize (e.g., Color). There are several option types to choose from (e.g., checkbox, text box)."
                    />

                    <Button
                      onClick={() =>
                        setTutorialVideoUrl("https://www.youtube.com/embed/-3KMHftdEr0?si=DbcRdHiUSMdUPslk&autoplay=1")
                      }
                      variant="plain"
                    >
                      Watch tutorial
                    </Button>
                  </InlineStack>

                  {!!optionSet.optionIds?.length && (
                    <Box paddingBlock="200">
                      <Text
                        as="span"
                        tone="subdued"
                      >
                        Drag options to set the order
                      </Text>
                    </Box>
                  )}

                  {optionSet.optionIds?.length ? (
                    <ProductOptionList
                      options={(optionSet.optionIds || []).flatMap(
                        (optionId) => options.find((option) => option.id === optionId) || []
                      )}
                      isPlain
                      isReorderable
                      onReorder={(fromIndex, toIndex) => {
                        setOptionSet((oldOptionSet) => {
                          const newOption = (oldOptionSet?.optionIds || [])?.[fromIndex];
                          if (!newOption) {
                            return oldOptionSet;
                          }

                          const newOptions = [...(oldOptionSet?.optionIds || [])];

                          newOptions.splice(fromIndex, 1);
                          newOptions.splice(toIndex, 0, newOption);

                          return { ...oldOptionSet, optionIds: newOptions };
                        });
                      }}
                      onOptionEdit={setEditedOption}
                      itemActions={[
                        {
                          icon: DuplicateIcon,
                          action: (id) => {
                            const optionId = id.startsWith("option-") ? Number(id.substring(7)) : undefined;

                            if (optionId) {
                              const duplicatedOption = options.find((option) => option.id === optionId);

                              if (duplicatedOption) {
                                setEditedOption({
                                  ...duplicatedOption,
                                  id: 0,
                                  nickname: `Copy of ${duplicatedOption.nickname || duplicatedOption.title}`,
                                  values: duplicatedOption.values.map((value) => {
                                    const { addonProduct, ...newValue } = value;
                                    return value.addonType === "new" ? newValue : value;
                                  }),
                                });
                              }
                            }
                          },
                        },
                        {
                          icon: DeleteIcon,
                          action: (id) => {
                            // TODO: factor out parsing the option ID from the item ID
                            const optionId = id.startsWith("option-") ? Number(id.substring(7)) : undefined;

                            // TODO: factor out all option set option IDs array change to a utility function
                            if (optionId) {
                              setOptionSet((oldOptionSet) => ({
                                ...oldOptionSet,
                                optionIds: oldOptionSet.optionIds?.filter((oldOptionId) => oldOptionId !== optionId),
                              }));
                            }
                          },
                        },
                      ]}
                    />
                  ) : (
                    <Card padding="0">
                      <EmptyState
                        heading="Add options to this option set"
                        image=""
                      >
                        <p>
                          Every option set consists of one or more options. One option is a specific thing the customer
                          can customize (e.g., Color). There are several option types to choose from (e.g., checkbox,
                          text box).
                        </p>

                        <div style={{ marginTop: "1rem", marginBottom: "-3.5rem" }}>
                          <InlineStack
                            align="center"
                            blockAlign="center"
                            gap="200"
                          >
                            {options.length ? (
                              <Button onClick={() => setOptionSelectModalOpen(true)}>Add existing option</Button>
                            ) : undefined}

                            <CreateOptionButton
                              label="Create new option"
                              onCreate={(type) => {
                                setEditedOption({ type });
                                resetAppErrorByKey("optionSet");
                              }}
                              primary
                            />
                          </InlineStack>
                        </div>
                      </EmptyState>
                    </Card>
                  )}

                  {optionSet.optionIds?.length ? (
                    <Box paddingBlock="200">
                      <InlineStack align="end">
                        <ButtonGroup>
                          {options.length && (
                            <Button onClick={() => setOptionSelectModalOpen(true)}>Add existing option</Button>
                          )}

                          <CreateOptionButton
                            label="Create new option"
                            onCreate={(type) => {
                              setEditedOption({ type });
                              resetAppErrorByKey("optionSet");
                            }}
                            primary
                          />
                        </ButtonGroup>
                      </InlineStack>
                    </Box>
                  ) : undefined}
                </BlockStack>
              </Card>

              <RuleEditor
                optionSet={optionSet}
                onOptionSetChange={setOptionSet}
                options={options}
                onTutorialOpen={setTutorialVideoUrl}
                shopDetails={shopDetails}
              />

              <ScheduleCall
                title="Let our experts help create your option set"
                description={
                  <>
                    Want to save your time? Get in touch with our Shopify experts for a quick guide to creating option
                    sets in your store, with{" "}
                    <Text
                      as="span"
                      variant="bodySm"
                      fontWeight="semibold"
                    >
                      ZERO charges
                    </Text>
                    .
                  </>
                }
                pageType="onboarding_options_set"
              />
            </BlockStack>
          </Layout.Section>

          <Layout.Section variant="oneThird">
            <AddToProduct
              optionSet={optionSet}
              onOptionSetChange={(newOptionSet) => {
                setOptionSet(newOptionSet);
                const changedOptionSet = optionSetFromInput(newOptionSet, options, isOnboarding);
                if ("errors" in changedOptionSet && errors.includes("products")) {
                  handleBannerErrors(changedOptionSet);
                  setErrors(changedOptionSet.errors);
                }
                if (isOnboarding) {
                  if ("errors" in changedOptionSet) {
                    if (appErrors?.optionSet.some((e) => e.key === "products")) {
                      handleBannerErrors(changedOptionSet);
                    }
                  } else {
                    handleBannerErrors({ errors: [] });
                  }
                }
              }}
            />
          </Layout.Section>
        </Layout>
        <div style={{ height: "1rem" }} />

        <Modal
          title="Tutorial"
          titleHidden
          open={!!tutorialVideoUrl}
          src={tutorialVideoUrl}
          onClose={() => setTutorialVideoUrl(undefined)}
        />

        <OptionSelectModal
          isOpen={isOptionSelectModalOpen}
          onClose={(selectedOptionIds) => {
            if (selectedOptionIds?.length) {
              setOptionSet((oldOptionSet) => ({
                ...oldOptionSet,
                optionIds: [...new Set([...(oldOptionSet.optionIds || []), ...selectedOptionIds])],
              }));
            }

            setOptionSelectModalOpen(false);
          }}
          options={options}
        />

        <PromptModal
          title={openModalType === "delete" ? "Delete option set" : "Discard changes"}
          prompt={
            openModalType === "delete"
              ? "Are you sure you want to delete this option set? This cannot be undone."
              : "Are you sure you want to discard the changes?"
          }
          actionLabel={openModalType === "delete" ? "Delete" : "Discard"}
          isDestructive
          isOpen={!!openModalType}
          isLoading={isDeleteLoading}
          onClose={async (isConfirmed) => {
            if (isConfirmed) {
              if (openModalType === "delete") {
                await deleteOptionSet();
              } else {
                onClose?.();
              }
            }

            setOpenModalType(undefined);
          }}
        />
      </>
    );

    return (
      <div className="option-set-page-wrapper">
        {!isOnboarding ? (
          <Page
            title={optionSet.title || "Untitled option set"}
            backAction={{
              content: "Option sets",
              onAction: () => {
                if (optionSet !== savedOptionSet) {
                  shopify.saveBar.leaveConfirmation();
                  // setOpenModalType("discard");
                } else {
                  onClose?.();
                }
              },
            }}
            secondaryActions={
              typeof optionSet.id !== "undefined" ? (
                <Tooltip
                  content={isDuplicateDisabled && "Upgrade plan to create unlimited option sets"}
                  hoverDelay={isDuplicateDisabled ? 0 : 100000}
                >
                  <Button
                    disabled={isDuplicateDisabled || optionSet !== savedOptionSet}
                    onClick={() => {
                      const { id, productIds, productSelectionValues, ...newOptionSet } = optionSet;

                      newOptionSet.title = `Copy of ${newOptionSet.title}`;
                      setOptionSet(newOptionSet);
                    }}
                  >
                    Duplicate
                  </Button>
                </Tooltip>
              ) : undefined
            }
            primaryAction={
              typeof optionSet.id !== "undefined"
                ? {
                    content: "Delete",
                    destructive: true,
                    onAction: () => setOpenModalType("delete"),
                  }
                : undefined
            }
          >
            <SaveBarWrapper
              saveActionCallback={() => saveOptionSet()}
              discardActionCallback={() => {
                if (isOnboarding) {
                  setAppErrorByKey("optionSet", []);
                }
                setErrors([]);
                setOptionSet(savedOptionSet);
                shopify.saveBar.hide(saveBarId);
              }}
              loading={isSaveLoading}
            />
            {pageContent}
          </Page>
        ) : (
          pageContent
        )}
      </div>
    );
  }
);

export default EditOptionSetPage;

EditOptionSetPage.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  optionSet: object.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: array.isRequired,
  onClose: func,
  onEdit: func.isRequired,
  onDelete: func,
  onOptionEditingChanged: func,
  onOptionEdit: func.isRequired,
  onOptionDelete: func.isRequired,
  isOnboarding: bool,
  isDuplicateDisabled: bool,
  /* eslint-disable react/forbid-prop-types */
  // @ts-ignore
  shopDetails: object.isRequired,
};
