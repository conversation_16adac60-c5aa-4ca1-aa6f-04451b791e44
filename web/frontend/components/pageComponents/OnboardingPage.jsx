import { Crisp } from "crisp-sdk-web";
import { arrayOf, func, object } from "prop-types";
import { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from "react";

import {
  Banner,
  BlockStack,
  Box,
  Button,
  ButtonGroup,
  Card,
  InlineStack,
  Modal,
  Page,
  ProgressBar,
  Text,
} from "@shopify/polaris";

// import PricingPage from "../../pages/Pricing";
import { useTrackEvent } from "../../utilities/hooks";
import OptionSetPageSkeleton from "../common/skeleton/OptionSetPageSkeleton";
import SubscriptionLayoutSkeleton from "../common/skeleton/SubscriptionLayoutSkeleton";
import EnableExtension from "../EnableExtension";
import { useAppErrors } from "../providers/ErrorProvider";

// import EditOptionSetPage from "./EditOptionSetPage";
const EditOptionSetPage = lazy(() => import("./EditOptionSetPage"));
const PricingPage = lazy(() => import("../../pages/Pricing"));

/**
 * @param {{
 *   shopDetails: IShopDetails,
 *   options: IProductOption[],
 *   onOptionSetEdit: (optionSet: IProductOptionSet) => void,
 *   onOptionEdit: (option: IProductOption) => void,
 *   onOptionDelete: (optionId: number | undefined) => void,
 *   onFinished: () => void,
 * }} props
 */
export default function OnboardingPage({
  shopDetails,
  options,
  onOptionSetEdit,
  onOptionEdit,
  onOptionDelete,
  onFinished,
}) {
  const { appErrors, setAppErrorByKey, resetAppErrorByKey, hasAppErrors, allAppErrors } = useAppErrors();

  const [currentStep, setCurrentStep] = useState(1);
  const [isEditingOption, setEditingOption] = useState(false);
  const [isNextDisabled, setNextDisabled] = useState(true);

  /** @type {TypedStateOptional<string>} */
  const [onboardingLoadingButton, setOnboardingLoadingButton] = useState();
  const [isExtensionEnableClicked, setExtensionEnableClicked] = useState(false);

  /** @type {TypedStateOptional<string>} */
  const [productHandle, setProductHandle] = useState();

  /** @type {TypedStateOptional<string>} */
  const [tutorialVideoUrl, setTutorialVideoUrl] = useState();

  const trackEvent = useTrackEvent();

  /**
   * @type {import('react').MutableRefObject<{
   *   saveOptionSet: (isSkip: boolean | undefined) => Promise<boolean>
   * } | undefined>}
   */
  const editOptionSetPageRef = useRef();

  const isIncludeChoosePlanStep = !shopDetails?.isPartnerDevelopment && !shopDetails?.isGrandfathered;

  const steps = useMemo(
    () => [...(isIncludeChoosePlanStep ? ["choose_plan"] : []), "tutorial", "create_option_set", "add_app_block"],
    [isIncludeChoosePlanStep]
  );

  const stepTitles = useMemo(
    () => [
      ...(isIncludeChoosePlanStep ? ["Choose a plan"] : []),
      "Learn the Basics",
      "Create Option Set",
      "Enable App Embed",
    ],
    [isIncludeChoosePlanStep]
  );

  const getStep = useCallback((/** @type {string} */ stepKey) => steps.indexOf(stepKey) + 1, [steps]);

  const handleSkipOrNext = useCallback(
    async (/** @type {string} */ action) => {
      trackEvent(`click ${action} onboarding option set create`);

      if (currentStep === getStep("create_option_set")) {
        setOnboardingLoadingButton(action);

        try {
          const success = await editOptionSetPageRef?.current?.saveOptionSet(action === "skip");

          if (success || action !== "next") {
            setCurrentStep(currentStep + 1);
          }
        } finally {
          setOnboardingLoadingButton(undefined);
        }
      } else {
        setCurrentStep(currentStep + 1);
      }
    },
    [currentStep, getStep, setOnboardingLoadingButton, trackEvent, editOptionSetPageRef]
  );

  const handleOptionSetEdit = useCallback(
    async (/** @type {IProductOptionSet} */ optionSet) => {
      onOptionSetEdit(optionSet);

      let query = "";

      if (optionSet.productSelectionMethod === "manual" && optionSet.productIds?.length) {
        query = `?productId=${optionSet.productIds[0].replace("gid://shopify/Product/", "")}`;
      }

      if (optionSet.productSelectionMethod === "collection" && optionSet.productSelectionValues?.length) {
        query = `?collectionId=${optionSet.productSelectionValues[0]}`;
      }

      if (query) {
        const response = await fetch(`/api/products/get-handle${query}`, {
          method: "GET",
          headers: { "Content-Type": "application/json" },
        });

        if (response.ok) {
          /** @type {ProductsGetHandleResponse} */
          const { handle } = await response.json();

          setProductHandle(handle);
        }
      }
    },
    [onOptionSetEdit]
  );

  useEffect(() => {
    if (currentStep === getStep("choose_plan")) {
      trackEvent("view onboarding plans");
      Crisp.session.pushEvent("pricing_onboarding");
    }

    if (currentStep === getStep("create_option_set")) {
      trackEvent("view onboarding option create");
      Crisp.session.pushEvent("option_set_onboarding");
    }

    if (currentStep === getStep("add_app_block")) {
      trackEvent("view onboarding add block");
      Crisp.session.pushEvent("app_block_onboarding");
    }
  }, [currentStep, trackEvent, getStep]);

  useEffect(() => {
    if (shopDetails?.isSubscriptionActive || new URLSearchParams(window.location.search).get("charge_id")) {
      setCurrentStep(getStep("tutorial"));
    }
  }, [shopDetails, getStep]);

  useEffect(() => {
    if (currentStep === getStep("tutorial")) {
      setTimeout(() => setNextDisabled(false), 5 * 1000);
    }
  }, [currentStep, getStep]);

  return (
    <div className="onboard-page-container">
      <Page title={isEditingOption ? undefined : "Product Options"}>
        {hasAppErrors && (
          <Box paddingBlockEnd="400">
            <Banner
              title="Action required"
              tone="critical"
              onDismiss={() => {
                resetAppErrorByKey("onboarding");
                resetAppErrorByKey("optionSet");
                resetAppErrorByKey("option");
              }}
            >
              <Box paddingInlineStart="200">
                {allAppErrors.map((error) => (
                  <li key={`err-${Math.random()}`}>{error?.message}</li>
                ))}
              </Box>
            </Banner>
          </Box>
        )}

        {!isEditingOption && (
          <Card>
            <BlockStack gap="200">
              <Text
                as="h2"
                variant="headingSm"
              >
                Get started with EasyFlow
              </Text>

              <InlineStack gap="400">
                <Text
                  variant="bodyLg"
                  as="p"
                >
                  {currentStep}/{steps.length} Steps: {stepTitles[currentStep - 1]}
                </Text>

                <div style={{ width: 298, marginTop: 5 }}>
                  <ProgressBar
                    progress={(currentStep / steps.length) * 100}
                    tone="success"
                  />
                </div>
              </InlineStack>

              <InlineStack align="end">
                {currentStep === getStep("tutorial") || currentStep === getStep("create_option_set") ? (
                  <ButtonGroup>
                    <Button
                      disabled={!!onboardingLoadingButton}
                      loading={onboardingLoadingButton === "skip"}
                      onClick={() => handleSkipOrNext("skip")}
                    >
                      Skip
                    </Button>

                    <Button
                      variant="primary"
                      disabled={!!onboardingLoadingButton || isNextDisabled}
                      loading={onboardingLoadingButton === "next"}
                      onClick={() => handleSkipOrNext("next")}
                    >
                      Next
                    </Button>
                  </ButtonGroup>
                ) : (
                  <InlineStack gap="200">
                    <Button
                      variant="plain"
                      onClick={() => window.open("https://storeware.io/easyflow/talk-with-expert", "_blank")}
                    >
                      Schedule a call
                    </Button>
                    <Button
                      onClick={() => {
                        Crisp.message.send("text", "I need help setting up EasyFlow.");
                        Crisp.chat.open();
                      }}
                    >
                      Need help?
                    </Button>
                  </InlineStack>
                )}
              </InlineStack>
            </BlockStack>
          </Card>
        )}
        {currentStep === getStep("tutorial") && (
          <Box paddingBlockStart="400">
            <iframe
              width="100%"
              height="530px"
              src="https://www.youtube.com/embed/m--Td7nmvfo?si=VsXNJwFDXlTNe4pn"
              title="YouTube video player"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              referrerPolicy="strict-origin-when-cross-origin"
              allowFullScreen
            />
          </Box>
        )}

        {currentStep === getStep("choose_plan") && (
          <Box
            paddingBlockStart="400"
            paddingBlockEnd="400"
          >
            <Card>
              <Text
                as="h2"
                variant="headingSm"
              >
                {`${currentStep}. Choose a plan`}
              </Text>
              <Box paddingBlock="200">
                <Suspense fallback={<SubscriptionLayoutSkeleton showFreePlan />}>
                  <PricingPage isOnboarding />
                </Suspense>
                <InlineStack align="end">
                  <Button
                    disabled={!!onboardingLoadingButton}
                    loading={onboardingLoadingButton === "skip"}
                    onClick={() => handleSkipOrNext("skip")}
                  >
                    Skip
                  </Button>
                </InlineStack>
              </Box>
            </Card>
          </Box>
        )}

        {currentStep === getStep("create_option_set") && (
          <Box paddingBlockStart={isEditingOption ? "0" : "400"}>
            <Suspense fallback={<OptionSetPageSkeleton isOnboarding />}>
              <EditOptionSetPage
                ref={editOptionSetPageRef}
                isOnboarding
                isDuplicateDisabled
                optionSet={{ title: "Your first Option Set" }}
                options={options}
                onEdit={handleOptionSetEdit}
                onOptionEditingChanged={(isEditing) => setEditingOption(isEditing)}
                onOptionEdit={onOptionEdit}
                onOptionDelete={onOptionDelete}
                shopDetails={shopDetails}
              />
            </Suspense>
          </Box>
        )}

        {currentStep === getStep("add_app_block") && (
          <Box paddingBlockStart="400">
            <Card>
              <BlockStack gap="200">
                <Text
                  as="h2"
                  variant="headingSm"
                >
                  {currentStep}. Add App Block
                </Text>

                <EnableExtension
                  shop={shopDetails.shop}
                  productHandle={productHandle}
                  isExtensionEnableClicked={isExtensionEnableClicked}
                  onExtensionEnableClick={() => {
                    setAppErrorByKey("onboarding", [
                      {
                        key: "app-block",
                        message: "Add app block to product page to continue",
                      },
                    ]);
                    setExtensionEnableClicked(true);
                  }}
                  onWatchTutorialClick={() =>
                    setTutorialVideoUrl("https://www.youtube.com/embed/0t1y25jQOW8?si=V7-yuY5fLylIvT9C&autoplay=1")
                  }
                />

                <InlineStack align="end">
                  <Button
                    variant="primary"
                    disabled={!!onboardingLoadingButton}
                    loading={onboardingLoadingButton === "finish"}
                    onClick={() => {
                      trackEvent("click finish set up");

                      if (!isExtensionEnableClicked) {
                        resetAppErrorByKey("onboarding");
                        setAppErrorByKey("onboarding", [
                          {
                            key: "app-block",
                            message: "Add app block to product page to continue",
                          },
                        ]);
                        return;
                      }

                      setOnboardingLoadingButton("finish");
                      resetAppErrorByKey("onboarding");
                      onFinished();
                    }}
                  >
                    Finish Setup
                  </Button>
                </InlineStack>
              </BlockStack>
            </Card>
          </Box>
        )}

        <Modal
          title="Tutorial"
          titleHidden
          src={tutorialVideoUrl}
          open={!!tutorialVideoUrl}
          onClose={() => setTutorialVideoUrl(undefined)}
        />
      </Page>
    </div>
  );
}

OnboardingPage.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  shopDetails: object.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: arrayOf(object).isRequired,
  onOptionSetEdit: func.isRequired,
  onOptionEdit: func.isRequired,
  onOptionDelete: func.isRequired,
  onFinished: func.isRequired,
};
