import { bool } from "prop-types";
import { useEffect, useState } from "react";

import { Page } from "@shopify/polaris";

// eslint-disable-next-line import/no-relative-packages
import { useAppBridge } from "@shopify/app-bridge-react";
// eslint-disable-next-line import/no-relative-packages
import { defaultStyleOptions as defaultCustomizations } from "../../../defaults";
import { useAppQuery, useTrackEvent } from "../../utilities/hooks";
import SaveBarWrapper, { saveBarId } from "../common/SaveBarWrapper";
import CustomizationContent from "../CustomizationContent";
import SettingsContent from "../SettingsContent";

/**
 * @param {{
 *   isCustomizationPage: boolean,
 * }} props
 */
export default function SettingsPage({ isCustomizationPage }) {
  const shopify = useAppBridge();

  /** @type {TypedState<StyleOptions>} */
  const [customizations, setCustomizations] = useState(defaultCustomizations);
  const [storedCustomizations, setStoredCustomizations] = useState(customizations);

  const [isSaveLoading, setSaveLoading] = useState(false);

  /** @type {TypedStateOptional<'customization' | 'settings'>} */
  const [erroredPage, setErroredPage] = useState();

  const trackEvent = useTrackEvent();

  /** @type {UseQueryResult<StyleOptions>} */
  const { isLoading, data: stylesData } = useAppQuery("/api/styles/get");

  useEffect(() => {
    if (!stylesData) {
      return;
    }

    setCustomizations((oldCustomizations) => {
      const newStyles = { ...oldCustomizations, ...stylesData };

      setStoredCustomizations(newStyles);
      return newStyles;
    });

    trackEvent(`view ${isCustomizationPage ? "customization" : "settings"} page`);
  }, [stylesData, trackEvent, isCustomizationPage]);

  const handleSave = async () => {
    setSaveLoading(true);

    if (
      !isCustomizationPage &&
      (!customizations.dropdownPlaceholder ||
        !customizations.fileUploadButtonText ||
        !customizations.dragAndDropText ||
        !customizations.uploadLoadingText ||
        !customizations.fileTypeErrorText ||
        !customizations.fileSizeErrorText ||
        !customizations.fileUploadErrorText ||
        !customizations.requiredOptionErrorText ||
        !customizations.minimumSelectionErrorText ||
        !customizations.maximumSelectionErrorText ||
        !customizations.mimumCharacterErrorText)
    ) {
      setErroredPage("settings");
      setSaveLoading(false);
      return;
    }

    setErroredPage(undefined);

    const response = await fetch("/api/styles/save", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(customizations),
    });

    if (response.ok) {
      setStoredCustomizations(customizations);
    }

    setSaveLoading(false);
    shopify.saveBar.hide(saveBarId);
  };

  useEffect(() => {
    if (storedCustomizations !== customizations) {
      shopify.saveBar.show(saveBarId);
    }
  }, [storedCustomizations, customizations, shopify]);

  return (
    <Page title={isCustomizationPage ? "Customization" : "Settings"}>
      <SaveBarWrapper
        saveActionCallback={() => {
          handleSave();
        }}
        discardActionCallback={() => {
          setCustomizations(storedCustomizations);
          shopify.saveBar.hide(saveBarId);
        }}
        loading={isSaveLoading}
      />

      <div className="settingspage-container">
        {isCustomizationPage ? (
          <CustomizationContent
            isLoading={isLoading}
            customizations={customizations}
            onCustomizationsChange={setCustomizations}
          />
        ) : (
          <SettingsContent
            isLoading={isLoading}
            customizations={customizations}
            onCustomizationsChange={setCustomizations}
            isFieldRequiredError={erroredPage === "settings"}
          />
        )}
      </div>
    </Page>
  );
}

SettingsPage.propTypes = {
  isCustomizationPage: bool.isRequired,
};
