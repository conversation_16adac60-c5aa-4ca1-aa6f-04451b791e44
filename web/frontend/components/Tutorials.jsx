import { Crisp } from 'crisp-sdk-web';
import { arrayOf, bool, element, number, shape, string } from 'prop-types';
import { useState } from 'react';

import { BlockStack, Box, InlineGrid, MediaCard, Modal, VideoThumbnail } from '@shopify/polaris';

/**
 * @param {{
 *   showIndexes?: number[],
 *   columns?: import('@shopify/polaris').InlineGridProps['columns'],
 *   showScheduleCallCard?: boolean
 *   scheduleCallCardContent?: React.ReactNode
 * }} props
 */
export default function Tutorials({
  showIndexes,
  columns = { sm: 1, lg: 3 },
  showScheduleCallCard = false,
  scheduleCallCardContent = undefined,
}) {
  /** @type {TypedStateOptional<string>} */
  const [videoUrl, setVideoUrl] = useState();

  const tutorials = [
    {
      title: 'How to show options on storefront',
      description: 'Learn how to activate EasyFlow on the storefront to show the options.',
      videoThumbnail: {
        videoLength: 78,
        thumbnailUrl: 'https://i.ytimg.com/vi/0t1y25jQOW8/maxresdefault.jpg',
        onClick: () =>
          setVideoUrl('https://www.youtube.com/embed/0t1y25jQOW8?si=V7-yuY5fLylIvT9C&autoplay=1'),
      },
      primaryAction: {
        content: 'Got a question?',
        onAction: () => {
          Crisp.message.send('text', 'I have questions about showing options on the storefront.');
          Crisp.chat.open();
        },
      },
    },
    {
      title: 'How to set up EasyFlow',
      description: 'All you need to know to create options, price add-ons and conditional logic.',
      videoThumbnail: {
        videoLength: 923,
        thumbnailUrl: 'https://i.ytimg.com/vi/MxIAFXqQs4Q/sddefault.jpg',
        onClick: () => setVideoUrl('https://www.youtube.com/embed/MxIAFXqQs4Q?si=B49hx3-ZJy-ztVrw'),
      },
      primaryAction: {
        content: 'Got a question?',
        onAction: () => {
          Crisp.message.send('text', 'I have questions about setting up EasyFlow.');
          Crisp.chat.open();
        },
      },
    },
    {
      title: 'How to create add-ons?',
      description: 'Learn how to upsell with add-ons on your product page.',
      videoThumbnail: {
        videoLength: 155,
        thumbnailUrl: 'https://img.youtube.com/vi/Nxk2M3ZfUs8/maxresdefault.jpg',
        onClick: () =>
          setVideoUrl('https://www.youtube.com/embed/Nxk2M3ZfUs8?si=-oAe-Dch-WKQYJVw&autoplay=1'),
      },
      primaryAction: {
        content: 'Got a question?',
        onAction: () => {
          Crisp.message.send('text', 'I have question about creating add-ons.');
          Crisp.chat.open();
        },
      },
    },
    {
      title: 'Need any styling changes?',
      description: 'We can change the style of your options in any way you like. Free of charge.',
      thumbnailUrl: null,
      primaryAction: {
        content: 'Styling help',
        onAction: () => {
          Crisp.message.send('text', 'I would like to change the style of the options.');
          Crisp.chat.open();
        },
      },
      secondaryAction: {
        content: 'Need other help?',
        onAction: () => {
          Crisp.message.send('text', 'I need help with EasyFlow');
          Crisp.chat.open();
        },
      },
    },
    {
      title: 'EasyFlow Helpdesk',
      description:
        'If you need help with EasyFlow visit our Helpdesk that contains detailed articles on how to set up EasyFlow or contact support.',
      thumbnailUrl: null,
      primaryAction: {
        content: 'Visit helpdesk',
        onAction: () => {
          window.open('https://easyflow-options.crisp.help/en/', '_blank');
        },
      },
      secondaryAction: {
        content: 'Contact support',
        onAction: () => {
          Crisp.message.send('text', 'I need help with EasyFlow');
          Crisp.chat.open();
        },
      },
    },
  ];

  const createMediaCard = (
    /** @type {typeof tutorials[number]} */ {
      title,
      description,
      videoThumbnail,
      primaryAction,
      secondaryAction,
    },
  ) => (
    <MediaCard
      portrait
      title={title}
      primaryAction={primaryAction}
      secondaryAction={secondaryAction}
      description={description}
    >
      {videoThumbnail && (
        <VideoThumbnail
          videoLength={videoThumbnail?.videoLength}
          thumbnailUrl={videoThumbnail?.thumbnailUrl}
          onClick={() => videoThumbnail?.onClick()}
        />
      )}
    </MediaCard>
  );

  return (
    <>
      <InlineGrid gap="200" columns={columns}>
        {tutorials
          .slice(0, -2)
          .filter((_, index) => !showIndexes || showIndexes.includes(index))
          .map(({ title, description, videoThumbnail, primaryAction, secondaryAction }) => (
            <Box paddingBlockEnd="400" key={title}>
              <MediaCard
                portrait
                title={title}
                primaryAction={primaryAction}
                secondaryAction={secondaryAction}
                description={description}
              >
                {videoThumbnail && (
                  <VideoThumbnail
                    videoLength={videoThumbnail?.videoLength}
                    thumbnailUrl={videoThumbnail?.thumbnailUrl}
                    onClick={() => videoThumbnail?.onClick()}
                  />
                )}
              </MediaCard>
            </Box>
          ))}

        <Box paddingBlockEnd="400" key={0}>
          <BlockStack>
            {showIndexes?.includes(3) && createMediaCard(tutorials[3])}
            {showIndexes?.includes(4) && createMediaCard(tutorials[4])}
            {showScheduleCallCard && scheduleCallCardContent}
          </BlockStack>
        </Box>
      </InlineGrid>

      <Modal
        title="Tutorial"
        titleHidden
        open={!!videoUrl}
        src={videoUrl}
        onClose={() => setVideoUrl(undefined)}
      />
    </>
  );
}

Tutorials.propTypes = {
  showIndexes: arrayOf(number),
  columns: shape({ sm: number, lg: number }),
  showScheduleCallCard: bool,
  scheduleCallCardContent: element || string,
};
