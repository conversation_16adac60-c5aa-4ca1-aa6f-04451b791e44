// @ts-ignore
import { func, object } from "prop-types";
import { useMemo, useState } from "react";

import { BlockStack, Box, Card, ChoiceList, Select, Text, TextField } from "@shopify/polaris";

// eslint-disable-next-line import/no-relative-packages
import { defaultAutomaticProductSelectionMethod } from "../../defaults";
import { useAppQuery } from "../utilities/hooks";
import CustomCombobox from "./CustomCombobox";
import HelpTooltip from "./HelpTooltip";
import ProductList from "./ProductList";

/**
 * @param {{
 *   optionSet: ProductOptionSetInput,
 *   onOptionSetChange: (optionSet: ProductOptionSetInput) => void,
 * }} props
 */
export default function AddToProduct({ optionSet, onOptionSetChange }) {
  const [searchTerm, setSearchTerm] = useState("");

  const url = useMemo(() => {
    const query = new URLSearchParams();

    if (searchTerm) {
      query.append("searchTerm", searchTerm);
    } else if (optionSet.productSelectionValues?.length) {
      optionSet.productSelectionValues.forEach((id) => query.append("selectedIds[]", id));
    }

    return `/api/collections/get?${query.toString()}`;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchTerm]);

  /** @type {UseQueryResult<CollectionsGetResponse>} */
  const { isLoading: isCollectionsLoading, data: collectionResults = [] } = useAppQuery(url, {
    reactQueryOptions: { enabled: optionSet.productSelectionMethod === "collection" },
    debounceMilliseconds: 250,
  });

  const collections = useMemo(
    () =>
      collectionResults.map((collection) => ({
        ...collection,
        id: collection.id.split("/").at(-1) || collection.id,
      })),
    [collectionResults]
  );

  const methodSelector =
    optionSet.productSelectionMethod === "collection" ? (
      <CustomCombobox
        label={optionSet.productSelectionMethod}
        values={collections.map(({ id, title }) => ({ id, label: title }))}
        selectedItems={optionSet.productSelectionValues || []}
        onSelectionChange={(newSelectedValues) =>
          onOptionSetChange({ ...optionSet, productSelectionValues: newSelectedValues })
        }
        onSearch={(value) => setSearchTerm(value)}
        isLoading={isCollectionsLoading}
      />
    ) : (
      <TextField
        label=""
        value={optionSet.productSelectionValues?.join(", ")}
        onChange={(newValue) => {
          const oldValue = optionSet.productSelectionValues?.join(", ") || "";

          // Delete the list separator comma when deleting the space after it
          const changedValue =
            newValue[newValue.length - 1] === "," && newValue.length < oldValue.length
              ? newValue.substring(0, newValue.length - 1)
              : newValue;

          onOptionSetChange({
            ...optionSet,
            productSelectionValues: changedValue
              ? changedValue
                  .split(",")
                  ?.flatMap((value, index, array) =>
                    value || index === array.length - 1 ? value.replace(/^\s+/, "") : []
                  )
              : [],
          });
        }}
        autoComplete="product-selection-values"
      />
    );

  return (
    <BlockStack gap="400">
      <Card>
        <HelpTooltip
          title="Add to products"
          content="To show the option set on the store front assign it to products manually or automatically."
        />

        <Box paddingBlock="200">
          <ChoiceList
            title=""
            choices={[
              {
                label: "Manually",
                value: "manual",
                helpText: "Choose products manually from the list below.",
              },
              {
                label: "Automatically",
                value: "automatic",
                helpText:
                  "The option set will be added to existing and future products that match the conditions you set.",
              },
            ]}
            selected={optionSet.productSelectionMethod === "manual" ? ["manual"] : ["automatic"]}
            onChange={(newSelectionMode) =>
              onOptionSetChange({
                ...optionSet,
                productSelectionMethod:
                  newSelectionMode[0] === "manual" ? "manual" : defaultAutomaticProductSelectionMethod,
              })
            }
          />
        </Box>
      </Card>
      {optionSet.productSelectionMethod === "manual" && (
        <Card>
          <BlockStack gap="200">
            <Text
              as="h2"
              variant="headingSm"
            >
              Search and select products
            </Text>
            <ProductList
              selectedIds={optionSet.productIds || []}
              onSelectedIdsChange={(newSelectedIds) => onOptionSetChange({ ...optionSet, productIds: newSelectedIds })}
            />
          </BlockStack>
        </Card>
      )}
      {optionSet.productSelectionMethod !== "manual" && (
        <Card>
          <BlockStack gap="200">
            <Text
              as="h2"
              variant="bodySm"
            >
              Select type
            </Text>
            <Box paddingBlock="200">
              <BlockStack gap="200">
                <Select
                  label=""
                  options={[
                    { label: "Collection", value: "collection" },
                    { label: "Product tag", value: "tag" },
                    { label: "Vendor", value: "vendor" },
                  ]}
                  value={optionSet.productSelectionMethod}
                  onChange={(newSelectionMethod) =>
                    onOptionSetChange({
                      ...optionSet,
                      productSelectionMethod: /** @type {ProductSelectionMethod} */ (newSelectionMethod),
                    })
                  }
                />

                {methodSelector}
              </BlockStack>
            </Box>
          </BlockStack>
        </Card>
      )}
    </BlockStack>
  );
}

AddToProduct.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  optionSet: object.isRequired,
  onOptionSetChange: func.isRequired,
};
