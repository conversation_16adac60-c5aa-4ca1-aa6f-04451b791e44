import { arrayOf, bool, func, object, string } from 'prop-types';
import { useCallback, useState } from 'react';

import {
  AutoSelection,
  BlockStack,
  Box,
  Combobox,
  Icon,
  InlineStack,
  Listbox,
  Spinner,
  Tag,
  Text,
} from '@shopify/polaris';
import { SearchIcon } from '@shopify/polaris-icons';

/**
 * @param {{
 *   label: string,
 *   isAllowMultiple?: boolean,
 *   values: { id: string, label: string }[],
 *   selectedItems: string[],
 *   onSelectionChange: (selectedValues: string[]) => void,
 *   onSearch?: (searchTerm: string) => void,
 *   isLoading?: boolean,
 * }} props
 */
export default function CustomCombobox({
  label: comboboxLabel,
  isAllowMultiple = true,
  values,
  selectedItems,
  onSelectionChange,
  onSearch,
  isLoading,
}) {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = useCallback(
    (/** @type {string} */ value) => {
      setInputValue(value);
      onSearch?.(value);
    },
    [onSearch],
  );

  const handleSelectionChange = useCallback(
    (/** @type {string} */ selected) => {
      const nextSelectedTags = new Set([...selectedItems.map((value) => `value-${value}`)]);

      if (nextSelectedTags.has(selected)) {
        nextSelectedTags.delete(selected);
      } else {
        nextSelectedTags.add(selected);
      }

      onSelectionChange([...nextSelectedTags].map((id) => id.substring(6)));

      handleInputChange('');
    },
    [selectedItems, onSelectionChange, handleInputChange],
  );

  const addNewOptionContent = !onSearch &&
    inputValue &&
    !values.some((value) => `value-${value.id}` === inputValue) && (
      <Listbox.Action value={`value-${inputValue}`}>{`Add "${inputValue}"`}</Listbox.Action>
    );

  let prefix = onSearch ? <Icon source={SearchIcon} /> : undefined;
  prefix = isLoading ? <Spinner accessibilityLabel="Small spinner" size="small" /> : prefix;

  return (
    <BlockStack>
      <Combobox
        allowMultiple={isAllowMultiple}
        activator={
          <Combobox.TextField
            labelHidden
            label={`${onSearch ? 'Search ' : ''}${comboboxLabel}`}
            placeholder={`${onSearch ? 'Search ' : ''}${comboboxLabel}`}
            prefix={prefix}
            value={inputValue}
            onChange={handleInputChange}
            autoComplete="off"
          />
        }
      >
        {(addNewOptionContent || values.length) && !isLoading ? (
          <Listbox autoSelection={AutoSelection.None} onSelect={handleSelectionChange}>
            {addNewOptionContent}

            {values.map(({ label, id }) => {
              const trimmedLabel = inputValue.trim().toLocaleLowerCase();
              const matchIndex = label.toLocaleLowerCase().indexOf(trimmedLabel);

              return (
                <Listbox.Option
                  key={id}
                  value={`value-${id}`}
                  selected={selectedItems.includes(id)}
                  accessibilityLabel={label}
                >
                  <Listbox.TextOption selected={selectedItems.includes(id)}>
                    {inputValue && matchIndex !== -1 ? (
                      <p>
                        {label.slice(0, matchIndex)}

                        <Text fontWeight="bold" as="span">
                          {label.slice(matchIndex, matchIndex + trimmedLabel.length)}
                        </Text>

                        {label.slice(matchIndex + trimmedLabel.length, label.length)}
                      </p>
                    ) : (
                      label
                    )}
                  </Listbox.TextOption>
                </Listbox.Option>
              );
            })}
          </Listbox>
        ) : undefined}
      </Combobox>

      {!!selectedItems.length && (
        <Box paddingBlock="100">
          <InlineStack align="start" blockAlign="center" gap="100">
            {selectedItems
              .filter((option) => values.some((value) => value.id === option))
              .map((valueId) => (
                <Tag key={valueId} onRemove={() => handleSelectionChange(`value-${valueId}`)}>
                  {values.find((value) => value.id === valueId)?.label}
                </Tag>
              ))}
          </InlineStack>
        </Box>
      )}
    </BlockStack>
  );
}

CustomCombobox.propTypes = {
  label: string.isRequired,
  isAllowMultiple: bool,
  // eslint-disable-next-line react/forbid-prop-types
  values: arrayOf(object).isRequired,
  selectedItems: arrayOf(string).isRequired,
  onSelectionChange: func.isRequired,
  onSearch: func,
  isLoading: bool,
};
