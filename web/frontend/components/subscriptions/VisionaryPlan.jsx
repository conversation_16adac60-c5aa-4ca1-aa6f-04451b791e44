import { BlockStack, Box, Card, Divider } from "@shopify/polaris";

import PlanChooseButton from "./PlanChooseButton";
import PlanFeature from "./PlanFeature";
import PlanHeader from "./PlanHeader";

/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {boolean} [is_subscribed]
 * @property {number} [trial_days]
 * @property {string} [description]
 * @property {string} [coupon]
 * @property {number} [price]
 * @property {number} [final_price]
 * @property {string} [interval_text]
 * @property {[]} [package_info]
 * @property {string} [slug]
 * @property {string} [interval]
 */

/**
 * @param {{ plan: SubscriptionPlan , handelChosePlan: (_, _) => void}} props
 */
// eslint-disable-next-line react/prop-types
const VisionaryPlan = ({ plan, handelChosePlan }) => (
  <Card padding="0">
    <BlockStack>
      <Box padding="400">
        <PlanHeader plan={plan} />
      </Box>
      <Divider />
      <PlanFeature features={plan?.package_info || []} />
      <Divider />
      <div className="plan_card_footer">
        <Box padding="400">
          <PlanChooseButton
            plan={plan}
            onClickAction={(e) => handelChosePlan(e, plan)}
          />
        </Box>
      </div>
    </BlockStack>
  </Card>
);

export default VisionaryPlan;
