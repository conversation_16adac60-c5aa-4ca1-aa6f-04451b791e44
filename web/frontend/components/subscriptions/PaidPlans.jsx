/* eslint-disable react/prop-types */
import { BlockStack, Box, Card, Divider } from "@shopify/polaris";
import lodash from "lodash";
import { useEffect, useState } from "react";
import PlanChooseButton from "./PlanChooseButton";
import PlanFeature from "./PlanFeature";
import PlanHeader from "./PlanHeader";

const { isEmpty } = lodash;

/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {boolean} [is_subscribed]
 * @property {number} [trial_days]
 * @property {string} [description]
 * @property {string} [coupon]
 * @property {number} [price]
 * @property {number} [final_price]
 * @property {string} [interval_text]
 * @property {[]} [package_info]
 * @property {string} [slug]
 * @property {string} [interval]
 */

/**
 * @param {{ allPlans: SubscriptionPlan[], handelChosePlan: (_,_) => void, plan?: SubscriptionPlan }} props
 */
// eslint-disable-next-line react/prop-types
const PaidPlans = ({ allPlans, handelChosePlan, plan }) => {
  // plan
  const [activePlan, setActivePlan] = useState(undefined);
  const [planFeatures, setPlanFeatures] = useState([]);

  useEffect(() => {
    if (allPlans && !isEmpty(allPlans)) {
      let desiredPlan;
      if (plan) {
        const [selectedPlan] = allPlans.filter((p) => p.slug === plan.slug);
        desiredPlan = selectedPlan || allPlans[0];
      } else {
        const [selectedPlan] = allPlans.filter((p) => p.is_subscribed);
        desiredPlan = selectedPlan || allPlans[0];
      }

      // @ts-ignore
      setActivePlan(desiredPlan);
      if (desiredPlan?.package_info) {
        setPlanFeatures(desiredPlan.package_info);
      }
    }
  }, [allPlans, plan]);

  // for latter use when we have visionary plan
  /**
   * Handles the change of plan type.
   *
   * @param {React.MouseEvent<HTMLButtonElement>} e - The click event.
   * @param {string} slug - The slug of the selected plan.
   */
  // const handelChangePlanType = (e, slug) => {
  //   const plan = allPlans.find((nPlan) => nPlan.slug === slug);
  //   // @ts-ignore
  //   setActivePlan(plan);
  // };

  return (
    <Card padding="0">
      <Box>
        <BlockStack align="space-between">
          <Box padding="400">
            <PlanHeader
              plan={activePlan}
              // planToggleButton={
              //   <PlanTypeToggleButton
              //     plan={activePlan}
              //     allPlans={allPlans}
              //     handelChangePlanType={handelChangePlanType}
              //   />
              // }
            />
          </Box>
          <Divider />
          <PlanFeature features={planFeatures} />
          <Divider />
          <div className="plan_card_footer">
            <Box padding="400">
              <PlanChooseButton
                plan={activePlan}
                onClickAction={(e) => handelChosePlan(e, activePlan)}
              />
            </Box>
          </div>
        </BlockStack>
      </Box>
    </Card>
  );
};

// for latter use when we have visionary plan
/**
 * @param {{ allPlans: SubscriptionPlan[], plan: SubscriptionPlan | undefined,
 * handelChangePlanType: (e: React.MouseEvent<HTMLButtonElement>, slug: string) => void }} props
 */
// const PlanTypeToggleButton = ({ plan, allPlans, handelChangePlanType }) => (
//   <ButtonGroup
//     variant="segmented"
//     fullWidth
//   >
//     {allPlans.map((aPlan) => (
//       <Button
//         pressed={aPlan.slug === plan?.slug}
//         fullWidth
//         key={aPlan.slug}
//         // @ts-ignore
//         onClick={(e) => {
//           // @ts-ignore
//           handelChangePlanType(e, aPlan.slug);
//         }}
//       >
//         {capitalize(aPlan?.interval)}
//       </Button>
//     ))}
//   </ButtonGroup>
// );

export default PaidPlans;
