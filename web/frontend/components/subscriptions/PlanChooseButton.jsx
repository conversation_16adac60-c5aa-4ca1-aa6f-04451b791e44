/* eslint-disable react/prop-types */
import { Button } from "@shopify/polaris";

/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {boolean} [is_subscribed]
 * @property {string} [slug]
 */

/**
 * @param {{ plan: SubscriptionPlan | undefined, onClickAction: () => void}} props
 */

const PlanChooseButton = ({ plan, onClickAction }) =>
  plan ? (
    <Button
      onClick={onClickAction}
      disabled={plan?.is_subscribed}
      variant="primary"
      fullWidth
    >
      {plan?.is_subscribed ? "Active plan" : "Choose"}
    </Button>
  ) : null;

export default PlanChooseButton;
