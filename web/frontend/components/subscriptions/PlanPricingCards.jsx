import { useEffect, useState } from "react";

import { useAppBridge } from "@shopify/app-bridge-react";
import { BlockStack, Box, Divider, Grid, InlineStack, Text } from "@shopify/polaris";
import { planIntervals, planTypes } from "easyflow-enums";
import lodash from "lodash";
import { checkoutModalId } from "../../utilities/helpers/global";
import CheckoutModal from "../checkout/CheckoutModal";
import FreePlan from "./FreePlan";
import PaidPlans from "./PaidPlans";
import VisionaryPlan from "./VisionaryPlan";

const { isEmpty } = lodash;

/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {string} [type]
 * @property {boolean} [is_free]
 * @property {boolean} [is_subscribed]
 * @property {number} [trial_days]
 * @property {string} [description]
 * @property {string} [coupon]
 * @property {number} [price]
 * @property {number} [final_price]
 * @property {string} [interval_text]
 * @property {[]} [package_info]
 * @property {string} [slug]
 * @property {string} [interval]
 */

/**
 * @param {{ allPlans: SubscriptionPlan[], isOnboarding?: undefined }} props
 */

// eslint-disable-next-line react/prop-types
const PlanPricingCard = ({ allPlans = [], isOnboarding = undefined }) => {
  const shopify = useAppBridge();

  const [freePlanData, setFreePlanData] = useState(undefined);
  const [visionaryPlanData, setVisionaryPlanData] = useState(undefined);
  const [allPaidPlansData, setAllPaidPlansData] = useState([]);
  const [chosenPlanData, setChosenPlanData] = useState(undefined);

  useEffect(() => {
    if (allPlans && !isEmpty(allPlans)) {
      const paidPlans = allPlans.filter((plan) => plan.type === planTypes.PRO && plan.interval !== planIntervals.USAGE);
      const freePlan = allPlans.find((plan) => plan.type === planTypes.FREE && plan.is_free);
      const visionaryPlan = allPlans.find(
        (plan) =>
          plan.type === planTypes.PRO &&
          plan.interval === planIntervals.USAGE &&
          plan.slug === `visionary-${planIntervals.USAGE}`
      );

      // @ts-ignore
      setAllPaidPlansData(paidPlans);
      // @ts-ignore
      setVisionaryPlanData(visionaryPlan);
      // @ts-ignore
      setFreePlanData(freePlan);
    }
  }, [allPlans]);

  const handelChosePlan = (e, plan) => {
    e.preventDefault();
    setChosenPlanData(plan);
    shopify.modal.show(checkoutModalId);
  };

  return (
    <div className="subscription-wrapper">
      <BlockStack gap="400">
        <div className="pro_plans">
          <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 2 }}>
            {allPaidPlansData &&
              allPaidPlansData.length > 0 &&
              allPaidPlansData.map((plan) => (
                <div
                  style={{ display: "flex" }}
                  key={`${plan?.slug}-${Math.random()}`}
                >
                  <Grid.Cell key="pro_plans">
                    <PaidPlans
                      plan={plan}
                      allPlans={allPaidPlansData}
                      handelChosePlan={handelChosePlan}
                    />
                  </Grid.Cell>
                </div>
              ))}
            {visionaryPlanData && (
              <div className="visionary_plan">
                <Grid.Cell key="visionary_plan">
                  <VisionaryPlan
                    plan={visionaryPlanData}
                    handelChosePlan={handelChosePlan}
                  />
                </Grid.Cell>
              </div>
            )}
          </Grid>
        </div>
        {freePlanData && !isOnboarding && (
          <>
            <Box>
              <InlineStack
                blockAlign="center"
                gap="400"
                wrap={false}
              >
                <Box width="40%">
                  <Divider borderColor="border" />
                </Box>
                <Text
                  as="p"
                  tone="subdued"
                  alignment="center"
                >
                  Alternatively
                </Text>
                <Box width="40%">
                  <Divider borderColor="border" />
                </Box>
              </InlineStack>
            </Box>

            <div className="free-plan">
              <FreePlan
                plan={freePlanData}
                handelChosePlan={handelChosePlan}
              />
            </div>
          </>
        )}
      </BlockStack>
      <CheckoutModal plan={chosenPlanData} />
    </div>
  );
};

export default PlanPricingCard;
