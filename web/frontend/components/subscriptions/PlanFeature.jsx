/* eslint-disable react/prop-types */
import { BlockStack, Box, Icon, InlineGrid, InlineStack, Text } from "@shopify/polaris";
import { CheckSmallIcon, XSmallIcon } from "@shopify/polaris-icons";

/**
 * @typedef {Object} features
 * @property {string} [name]
 * @property {string} [display_name]
 * @property {boolean | number} [value]
 */

/**
 * @param {{ children: React.ReactNode, fullWidth?: boolean }} props
 */
// eslint-disable-next-line react/prop-types
const FeaturesRow = ({ children, fullWidth }) => (
  <Box>
    {fullWidth ? (
      <InlineGrid
        columns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
        gap="100"
      >
        {children}
      </InlineGrid>
    ) : (
      // eslint-disable-next-line react/jsx-no-useless-fragment
      <>{children}</>
    )}
  </Box>
);

/**
 * @param {{ features: features[], fullWidth?: boolean }} props
 */
// eslint-disable-next-line react/prop-types
function PlanFeature({ features, fullWidth }) {
  return (
    <div className="plan_card_feature">
      <Box padding="400">
        <BlockStack gap="300">
          <FeaturesRow fullWidth={fullWidth}>
            {features &&
              features.length > 0 &&
              features.map((benefit, index) => (
                <InlineStack
                  gap="200"
                  wrap={false}
                  // eslint-disable-next-line react/no-array-index-key
                  key={`${benefit.name}${index}`}
                  blockAlign="start"
                >
                  <Box>
                    <Icon
                      source={benefit.value ? CheckSmallIcon : XSmallIcon}
                      tone="primary"
                    />
                  </Box>
                  <Text
                    variant="bodyMd"
                    as="p"
                  >
                    {benefit?.display_name}
                  </Text>
                </InlineStack>
              ))}
          </FeaturesRow>
        </BlockStack>
      </Box>
    </div>
  );
}

export default PlanFeature;
