/* eslint-disable react/prop-types */
import { Badge, BlockStack, Box, InlineStack, Text } from "@shopify/polaris";
import { planIntervals } from "easyflow-enums";
import lodash from "lodash";

const { isNil } = lodash;
/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {boolean} [is_subscribed]
 * @property {number} [trial_days]
 * @property {string} [description]
 * @property {string} [coupon]
 * @property {number} [price]
 * @property {number} [final_price]
 * @property {string} [interval_text]
 * @property {[]} [package_info]
 * @property {string} [slug]
 * @property {string} [interval]
 */

/**
 * @param {{ plan: SubscriptionPlan | undefined, planToggleButton?: React.ReactNode }} props
 */
const PlanHeader = ({ plan, planToggleButton }) =>
  plan && (
    <BlockStack gap="400">
      <InlineStack align="space-between">
        <Box width="60%">
          <BlockStack
            gap="100"
            inlineAlign="start"
          >
            <InlineStack
              align="start"
              gap="200"
            >
              <Text
                as="h2"
                variant="headingMd"
                fontWeight="semibold"
              >
                {plan?.name}
              </Text>
              {/* {plan?.is_subscribed && <Badge tone="success">Current plan</Badge>} */}
            </InlineStack>
            <BlockStack
              align="start"
              gap="200"
            >
              {plan.interval !== planIntervals.USAGE && plan?.trial_days && plan?.trial_days > 0 && (
                <Badge tone="info">{`${plan?.trial_days} days Free trial`}</Badge>
              )}
              <Text
                as="p"
                variant="bodyXs"
                tone="subdued"
              >
                {!isNil(plan?.description) ? `*${plan?.description}` : ""}
              </Text>
            </BlockStack>
          </BlockStack>
        </Box>
        <Box width="40%">
          <BlockStack inlineAlign="end">
            {plan?.coupon && (
              <Text
                variant="bodySm"
                as="p"
                textDecorationLine="line-through"
                tone="subdued"
              >
                ${Number(plan?.price).toFixed(2)}
              </Text>
            )}
            <InlineStack blockAlign="end">
              <Text
                as="h2"
                variant="headingLg"
                fontWeight="semibold"
              >
                $
                {plan.interval === planIntervals.USAGE
                  ? Number(plan?.price).toFixed(2)
                  : Number(plan?.final_price).toFixed(2)}
              </Text>
              <Text
                as="span"
                tone="subdued"
                variant="bodySm"
              >
                {plan?.interval_text && `/${plan?.interval_text.substring(0, 1).toUpperCase()}`}
              </Text>
            </InlineStack>
          </BlockStack>
        </Box>
      </InlineStack>
      {planToggleButton && planToggleButton}
    </BlockStack>
  );

export default PlanHeader;
