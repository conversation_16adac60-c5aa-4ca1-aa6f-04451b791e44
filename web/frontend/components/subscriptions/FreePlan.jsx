/* eslint-disable react/prop-types */
import { BlockStack, Box, Button, Card, Collapsible, Divider, InlineStack, Text } from "@shopify/polaris";
import { CircleChevronDownIcon, CircleChevronUpIcon } from "@shopify/polaris-icons";
import { useCallback, useState } from "react";
import PlanChooseButton from "./PlanChooseButton";
import PlanFeature from "./PlanFeature";

/**
 * @param {{ plan: { name?: string, is_subscribed?: boolean, trial_days?: number,
 * description?: string, coupon?: string, price?: number, final_price?: number,
 * interval_text?: string }, handlePlanToggle: () => void, isPlanOpen: boolean, handelChosePlan: (_,_) => void }} props
 */

const PlanHeader = ({ plan, isPlanOpen, handlePlanToggle, handelChosePlan }) => (
  <Box
    borderStartEndRadius="050"
    padding="400"
  >
    <InlineStack
      align="space-between"
      blockAlign="center"
    >
      <BlockStack gap="100">
        <InlineStack
          align="start"
          gap="200"
        >
          <Text
            as="h2"
            variant="headingMd"
            fontWeight="semibold"
          >
            {`${plan?.name} Plan`}
          </Text>
          <Button
            icon={isPlanOpen ? CircleChevronUpIcon : CircleChevronDownIcon}
            onClick={handlePlanToggle}
            ariaExpanded={isPlanOpen}
            ariaControls="basic-collapsible"
            variant="monochromePlain"
          />
          {/* {plan?.is_subscribed && <Badge tone="success">Current Plan</Badge>} */}
        </InlineStack>
        <Text
          as="p"
          variant="bodySm"
          tone="subdued"
        >
          *No Card required
        </Text>
      </BlockStack>
      <BlockStack align="end">
        <InlineStack
          gap="400"
          blockAlign="center"
        >
          <InlineStack>
            <Text
              as="h2"
              variant="headingLg"
              fontWeight="semibold"
            >
              ${Number(plan?.final_price).toFixed(2)}
            </Text>
            <Text
              as="span"
              tone="subdued"
              variant="bodySm"
            >
              {plan?.interval_text && `/${plan?.interval_text.substring(0, 1).toUpperCase()}`}
            </Text>
          </InlineStack>

          <Box>
            <PlanChooseButton
              plan={plan}
              onClickAction={(e) => handelChosePlan(e, plan)}
            />
          </Box>
        </InlineStack>
      </BlockStack>
    </InlineStack>
  </Box>
);

/**
 * @param {{ plan: { name?: string, slug?: string, is_subscribed?: boolean, trial_days?: number,
 * description?: string, coupon?: string, price?: number, final_price?: number,
 * interval_text?: string, package_info?: [] }, handelChosePlan: (_,_) => void }} props
 */
// eslint-disable-next-line react/prop-types
const FreePlan = ({ plan, handelChosePlan }) => {
  const [isPlanOpen, setIsPlanOpen] = useState(true);

  const handlePlanToggle = useCallback(() => setIsPlanOpen((prevIsPlanOpen) => !prevIsPlanOpen), []);

  return (
    <Card padding="0">
      <BlockStack>
        <PlanHeader
          plan={plan}
          isPlanOpen={isPlanOpen}
          handlePlanToggle={handlePlanToggle}
          handelChosePlan={handelChosePlan}
        />
        <Collapsible
          open={isPlanOpen}
          id={`collapsible-${plan.slug}`}
          transition={{ duration: "300ms", timingFunction: "ease-in-out" }}
          expandOnPrint
        >
          <Divider />
          <PlanFeature
            features={plan?.package_info || []}
            fullWidth
          />
        </Collapsible>
      </BlockStack>
    </Card>
  );
};

export default FreePlan;
