import {
  AutoSelection,
  Avatar,
  Bleed,
  BlockStack,
  Box,
  Button,
  Combobox,
  Divider,
  Icon,
  InlineGrid,
  InlineStack,
  Listbox,
  Text,
  Thumbnail,
} from "@shopify/polaris";
import { SearchIcon, XSmallIcon } from "@shopify/polaris-icons";
// @ts-ignore
import PropTypes from "prop-types";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useAppMutation } from "storeware-tanstack-query";
import { handleFetchProduct } from "../services/product.service";

/**
 * @param {{
 *   selectedIds: string[],
 *   onSelectedIdsChange: (newSelectedIds: string[]) => void,
 * }} props
 */
export default function ProductList({ selectedIds, onSelectedIdsChange }) {
  const [allFetchedProducts, setAllFetchedProducts] = useState([]);
  const [titleFilter, setTitleFilter] = useState("");
  const [inputValue, setInputValue] = useState("");
  const [pageInfo, setPageInfo] = useState({});
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  /** @type {TypedStateOptional<string>} */
  const [cursor, setCursor] = useState();

  /** @type {TypedStateOptional<string>} */
  const [paginationType, setPaginationType] = useState();

  const productFetch = useMemo(() => {
    const query = new URLSearchParams();
    const productIds = new Set();

    if (titleFilter) query.append("searchTerm", titleFilter);
    if (cursor) query.append("cursor", cursor);
    if (paginationType) query.append("paginationType", paginationType);

    // Move selectedIds to productIds set
    if (selectedIds.length) {
      selectedIds.forEach((id) => productIds.add(id));
    }
    const apiUrl = `/api/products/get?${query.toString()}`;
    return { apiUrl, productIds }; // Returning both URL and productIds set
  }, [titleFilter, cursor, paginationType, selectedIds]);

  const handleSetInitialSelectedTags = useCallback(
    (data) => {
      if (data && data.products) {
        const newSelectedProducts = data.products
          .filter((product) => selectedIds.includes(product.id))
          .map((product) => ({
            id: product.id.toString(),
            title: product.title,
            imageUrl: product.imageUrl,
          }));
        setSelectedProducts(newSelectedProducts);
      }
    },
    [selectedIds]
  );

  const { mutate: fetchProductList, isPending: isLoading } = useAppMutation({
    mutationKey: ["fetchProduct"],
    mutationFn: async () =>
      handleFetchProduct({
        url: productFetch?.apiUrl,
        data: { selectedIds: Array.from(productFetch.productIds) },
      }),
    onSuccess: (data) => {
      // eslint-disable-next-line no-unused-expressions
      if (isFirstLoad) {
        setIsFirstLoad(false);
        handleSetInitialSelectedTags(data);
      }
      return data;
    },
    onError: (error) => {
      console.error("product fetch error ", error);
    },
    options: {
      retry: 1,
    },
  });

  useEffect(() => {
    fetchProductList(undefined, {
      onSuccess: (data) => {
        if (data && data.products) {
          // @ts-ignore
          setAllFetchedProducts((prev) => [
            ...prev,
            ...(data.products.map((product) => ({ ...product, id: product.id.toString() })) || []),
          ]);
        }
        // eslint-disable-next-line no-unused-expressions
        data && data.pageInfo && setPageInfo(data.pageInfo);
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cursor, paginationType]);

  useEffect(() => {
    fetchProductList(undefined, {
      onSuccess: (data) => {
        if (data && data.products) {
          // @ts-ignore
          setAllFetchedProducts((prev) => [
            ...(data.products.map((product) => ({ ...product, id: product.id.toString() })) || []),
          ]);
        }
        // eslint-disable-next-line no-unused-expressions
        data && data.pageInfo && setPageInfo(data.pageInfo);
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [titleFilter]);

  // @ts-ignore
  const updateText = useCallback((value) => {
    setInputValue(value);
    setTitleFilter(value);
    setCursor(undefined);
    setPaginationType(undefined);
  }, []);

  const removeTag = useCallback(
    // @ts-ignore
    (tag) => {
      const options = selectedIds.filter((id) => id !== tag);
      // @ts-ignore
      // const newSelectedProducts = selectedProducts.filter((product) => product.id !== tag);
      // @ts-ignore
      // setSelectedProducts(newSelectedProducts);
      onSelectedIdsChange(options);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedIds, selectedProducts]
  );

  const tagsMarkup = selectedIds.map((option, index) => (
    <BlockStack
      gap="400"
      key={option}
    >
      {index > 0 && (
        <Bleed marginInline="400">
          <Divider />
        </Bleed>
      )}
      <InlineStack
        gap="200"
        align="space-between"
        blockAlign="center"
      >
        <Box width="85%">
          <InlineStack
            gap="200"
            align="start"
            wrap={false}
          >
            {selectedProducts.find((product) => product.id === option)?.imageUrl ? (
              <Thumbnail
                source={selectedProducts.find((product) => product.id === option)?.imageUrl || ""}
                size="extraSmall"
                alt={selectedProducts.find((product) => product.id === option)?.title || ""}
              />
            ) : (
              <Avatar
                size="xs"
                customer
                name={selectedProducts.find((product) => product.id === option)?.title}
              />
            )}

            <Text
              as="p"
              variant="bodySm"
            >
              {selectedProducts.find((product) => product.id === option)?.title || ""}
            </Text>
          </InlineStack>
        </Box>

        <Button
          variant="plain"
          accessibilityLabel="Remove product"
          icon={XSmallIcon}
          onClick={() => removeTag(option)}
        />
      </InlineStack>
    </BlockStack>
  ));

  const emptyState = (
    <BlockStack
      gap="400"
      align="center"
      inlineAlign="center"
    >
      <Icon source={SearchIcon} />
      <Text
        as="p"
        variant="bodySm"
      >
        Could not find any product
      </Text>
    </BlockStack>
  );

  const listOptionMarkUp =
    allFetchedProducts.length > 0
      ? allFetchedProducts.map(({ id, title, imageUrl }) => (
          <Listbox.Option
            key={`${id}-${title}-${Math.random()}`}
            value={id}
            divider
            accessibilityLabel={title}
            selected={selectedIds.includes(id)}
          >
            <Box
              padding="200"
              paddingInlineStart="400"
            >
              <InlineStack
                gap="200"
                align="start"
                blockAlign="center"
                wrap={false}
              >
                <Box paddingBlockStart="200">
                  <input
                    type="checkbox"
                    className="custom-checkbox"
                    checked={selectedIds.includes(id)}
                    onChange={(e) => {
                      e.preventDefault();
                    }} // Prevent checkbox from being checked)
                  />
                </Box>

                <InlineStack
                  gap="200"
                  align="start"
                  wrap={false}
                >
                  {imageUrl ? (
                    <Thumbnail
                      source={imageUrl}
                      size="extraSmall"
                      alt={title}
                    />
                  ) : (
                    <Avatar
                      customer
                      name={title}
                      size="xs"
                    />
                  )}
                  <Text
                    as="p"
                    variant="bodySm"
                  >
                    {title}
                  </Text>
                </InlineStack>
              </InlineStack>
            </Box>
          </Listbox.Option>
        ))
      : !isLoading && (
          <Box
            padding="200"
            paddingInlineStart="400"
          >
            {emptyState}
          </Box>
        );

  return (
    <BlockStack gap="400">
      <Box>
        <Combobox
          allowMultiple
          // @ts-ignore
          willLoadMoreOptions={!!pageInfo?.hasNextPage}
          onScrolledToBottom={() => {
            if (allFetchedProducts && allFetchedProducts.length > 0) {
              setCursor(allFetchedProducts[allFetchedProducts.length - 1].cursor);
              setPaginationType("after");
            }
          }}
          activator={
            <Combobox.TextField
              prefix={<Icon source={SearchIcon} />}
              onChange={updateText}
              label="Search products"
              labelHidden
              value={inputValue}
              placeholder="Search products"
              autoComplete="off"
            />
          }
        >
          <Listbox
            onSelect={(value) => {
              const newSelectedProducts = selectedProducts.some((product) => product.id === value)
                ? selectedProducts.filter((product) => product.id !== value)
                : [
                    ...selectedProducts,
                    {
                      id: value,
                      title: allFetchedProducts.find((product) => product.id === value)?.title,
                      imageUrl: allFetchedProducts.find((product) => product.id === value)?.imageUrl,
                    },
                  ];

              // @ts-ignore
              setSelectedProducts(newSelectedProducts);

              const newProductIds = selectedIds.includes(value)
                ? selectedIds.filter((productId) => productId !== value)
                : [...selectedIds, value];
              onSelectedIdsChange(newProductIds);
            }}
            autoSelection={AutoSelection.None}
          >
            {listOptionMarkUp}
            {isLoading && <Listbox.Loading accessibilityLabel="Loading" />}
          </Listbox>
        </Combobox>
      </Box>
      {selectedProducts.length > 0 && selectedIds && selectedIds.length > 0 && (
        <BlockStack gap="400">
          <Text
            as="h2"
            variant="headingSm"
          >
            Selected products
          </Text>
          <InlineGrid
            columns={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 1 }}
            gap="200"
          >
            {tagsMarkup}
          </InlineGrid>
        </BlockStack>
      )}
    </BlockStack>
  );
}

ProductList.propTypes = {
  selectedIds: PropTypes.arrayOf(PropTypes.string).isRequired,
  onSelectedIdsChange: PropTypes.func.isRequired,
};
