import { Bleed, Box, Icon, InlineStack, Text } from "@shopify/polaris";
import { InfoIcon } from "@shopify/polaris-icons";
// @ts-ignore
import PropTypes from "prop-types";
import React from "react";

/**
 * @param {{
 *   content: React.ReactNode,
 * }} props
 */
const CardFooterInfo = ({ content }) => (
  <Bleed
    marginInline="400"
    marginBlockEnd="400"
  >
    <Box
      background="bg-surface-secondary"
      padding="200"
    >
      <InlineStack
        gap="200"
        blockAlign="center"
      >
        {content}
      </InlineStack>
    </Box>
  </Bleed>
);

CardFooterInfo.propTypes = {
  content: PropTypes.node.isRequired,
};

export default CardFooterInfo;
