import { bool, func, string } from "prop-types";
import { useCallback, useEffect, useMemo, useState } from "react";

import { Box, Filters, Link, Modal, ResourceItem, ResourceList, Text, Thumbnail } from "@shopify/polaris";
import { ImageIcon } from "@shopify/polaris-icons";

import { useAppQuery } from "../utilities/hooks";

/**
 * @param {{
 *   selectedVariantId: string | undefined,
 *   onVariantAdd: (product: ProductVariant | null) => void,
 *   onVariantRemove: () => void,
 *   isSingleValueType: boolean,
 *   isDisabled: boolean,
 * }} props
 */
export default function ProductPicker({
  selectedVariantId,
  onVariantAdd,
  onVariantRemove,
  isSingleValueType,
  isDisabled,
}) {
  const [isActive, setActive] = useState(false);
  const [titleFilter, setTitleFilter] = useState("");

  /** @type {TypedStateOptional<{ type: 'before' | 'after', cursor: string | undefined }>} */
  const [pagination, setPagination] = useState();

  const [selectedItems, setSelectedItems] = useState(selectedVariantId ? [selectedVariantId] : []);

  /** @type {TypedStateOptional<ProductVariant>} */
  const [initialVariant, setInitialVariant] = useState();

  const variantsUrl = useMemo(() => {
    const query = new URLSearchParams();

    if (titleFilter) {
      query.append("searchTerm", titleFilter);
    }

    if (pagination?.cursor) {
      query.append("cursor", pagination.cursor);
    }

    if (pagination?.type) {
      query.append("paginationType", pagination.type);
    }

    return `/api/variants/get?${query.toString()}`;
  }, [titleFilter, pagination]);

  /** @type {UseQueryResult<VariantsGetResponse>} */
  const { isLoading: isVariantsLoading, data: variantsData } = useAppQuery(variantsUrl, {
    reactQueryOptions: { enabled: isActive },
    debounceMilliseconds: 250,
  });

  // Safely access pageInfo with null checks to prevent crashes on 500 errors
  const pageInfo = variantsData && variantsData.pageInfo;

  // Handle the case when variantsData is undefined (e.g., on API error)
  const variants = useMemo(() => {
    // Initialize arrays with proper type annotations
    /** @type {ProductVariant[]} */
    let initialVariantArray = [];

    /** @type {ProductVariant[]} */
    let variantsList = [];

    // Handle initial variant display logic
    if (initialVariant && !pageInfo?.hasPreviousPage && !isVariantsLoading && !titleFilter) {
      // If variantsData is undefined or doesn't have variants (API error case)
      if (!variantsData || !variantsData.variants) {
        initialVariantArray = [initialVariant];
      }
      // If variantsData exists and has variants, check if initialVariant is already in the list
      else if (!variantsData.variants.some((variant) => variant.id === initialVariant.id)) {
        initialVariantArray = [initialVariant];
      }
    }

    // Safely map variants with null checks
    if (variantsData && variantsData.variants) {
      variantsList = variantsData.variants.map((variant) => ({
        ...variant,
        id: variant.id.toString(),
      }));
    }

    return [...initialVariantArray, ...variantsList];
  }, [initialVariant, pageInfo, variantsData, isVariantsLoading, titleFilter]);

  const selectedVariant = useMemo(() => {
    const foundVariant = variants.find((variant) => variant.id === selectedItems[0]);
    return foundVariant || initialVariant;
  }, [variants, selectedItems, initialVariant]);

  const handleTitleFilterChange = useCallback((/** @type {string} */ newTitleFilter) => {
    setTitleFilter(newTitleFilter);
    setPagination(undefined);
  }, []);

  useEffect(() => {
    if (isActive) {
      setSelectedItems(selectedVariantId ? [selectedVariantId] : []);
    }
  }, [isActive, selectedVariantId]);

  useEffect(() => {
    (async () => {
      if (!selectedVariantId || initialVariant) {
        return;
      }

      const response = await fetch(`/api/variants/${encodeURIComponent(selectedVariantId)}`);

      /** @type {ProductVariant} */
      const responseData = await response.json();

      if ("error" in responseData) {
        onVariantRemove();
        return;
      }

      setInitialVariant(responseData);
    })();
  }, [initialVariant, onVariantRemove, selectedVariantId]);

  const openProductPickerButton = (
    <div style={{ width: isSingleValueType ? "14rem" : "5.8rem" }}>
      <Text
        truncate
        as="span"
      >
        {selectedVariant ? selectedVariant.displayName : "add-on product"}
      </Text>
    </div>
  );

  return (
    <Modal
      activator={
        !isDisabled ? (
          // eslint-disable-next-line jsx-a11y/anchor-is-valid
          <Link
            onClick={() => setActive(!isActive)}
            removeUnderline
          >
            {openProductPickerButton}
          </Link>
        ) : (
          openProductPickerButton
        )
      }
      open={isActive}
      onClose={() => setActive(false)}
      title="Add-on product"
      primaryAction={{
        disabled: !selectedVariant,
        content: "Add add-on product",
        onAction: () => {
          if (selectedVariant) {
            onVariantAdd(selectedVariant);
            setActive(false);
          }
        },
      }}
      secondaryActions={[
        {
          content: "Remove add-on product",
          onAction: () => {
            setSelectedItems([]);
            setInitialVariant(undefined);

            onVariantRemove();
            setActive(false);
          },
        },
      ]}
    >
      <ResourceList
        resourceName={{ singular: "products", plural: "products" }}
        items={variants}
        loading={isVariantsLoading}
        flushFilters
        selectable
        selectedItems={selectedItems}
        onSelectionChange={(clickedItems) =>
          setSelectedItems(clickedItems.length > 0 ? [clickedItems[clickedItems.length - 1]] : [])
        }
        filterControl={
          <Box padding="200">
            <Filters
              filters={[]}
              queryPlaceholder="Search products"
              queryValue={titleFilter}
              onQueryChange={handleTitleFilterChange}
              onQueryClear={() => handleTitleFilterChange("")}
              onClearAll={() => handleTitleFilterChange("")}
            />
          </Box>
        }
        renderItem={(item) => {
          const { id, imageUrl, displayName } = item;

          return (
            <ResourceItem
              id={id}
              media={
                <Thumbnail
                  source={imageUrl || ImageIcon}
                  size="small"
                  alt={displayName}
                />
              }
              accessibilityLabel={`View details for ${displayName}`}
              url="#"
              onClick={() => setSelectedItems([id])}
            >
              <Text
                variant="bodyMd"
                fontWeight="bold"
                as="h3"
              >
                {displayName}
              </Text>
            </ResourceItem>
          );
        }}
        pagination={{
          hasNext: pageInfo && !!pageInfo?.hasNextPage && !isVariantsLoading,
          hasPrevious: pageInfo && !!pageInfo?.hasPreviousPage && !isVariantsLoading,
          onPrevious: () => setPagination({ type: "before", cursor: variants[0].cursor }),
          onNext: () => setPagination({ type: "after", cursor: variants[variants.length - 1].cursor }),
        }}
      />
    </Modal>
  );
}

ProductPicker.propTypes = {
  selectedVariantId: string.isRequired,
  onVariantAdd: func.isRequired,
  onVariantRemove: func.isRequired,
  isSingleValueType: bool.isRequired,
  isDisabled: bool.isRequired,
};
