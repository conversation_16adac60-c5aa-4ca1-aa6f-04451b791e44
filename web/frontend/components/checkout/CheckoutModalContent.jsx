/* eslint-disable react/prop-types */
// @ts-nocheck
import { useAppBridge } from "@shopify/app-bridge-react";
import { Bleed, BlockStack, Box, Button, Divider, InlineStack, Link, Text, TextField } from "@shopify/polaris";
import { XSmallIcon } from "@shopify/polaris-icons";
import { planIntervals } from "easyflow-enums";
import lodash from "lodash";
import { useEffect, useState } from "react";
// eslint-disable-next-line import/no-extraneous-dependencies
import { useAppMutation } from "storeware-tanstack-query";
import { handleSubscription } from "../../services/subscription.service";
import { checkoutModalId, getApiError } from "../../utilities/helpers/global";

const { toInteger } = lodash;

/**
 * @typedef {Object} SubscriptionPlan
 * @property {string} [name]
 * @property {boolean} [is_subscribed]
 * @property {boolean} [is_free]
 * @property {number} [trial_days]
 * @property {string} [description]
 * @property {string} [duration]
 * @property {Object} [coupon]
 * @property {string} [coupon_code]
 * @property {number} [price]
 * @property {number} [final_price]
 * @property {string} [interval_text]
 * @property {[]} [package_info]
 * @property {string} [slug]
 * @property {string} [interval]
 */

/**
 * @param {{ plan: SubscriptionPlan, isModalShown: boolean, redirectAfterSubscription, redirectAfterSubscription: (_,_) => void }} props
 */
const CheckoutModalContent = ({ plan, isModalShown, redirectAfterSubscription }) => {
  const shopify = useAppBridge();

  const [finalPlan, setFinalPlan] = useState(/** @type {SubscriptionPlan | undefined} */ (undefined));

  // coupon
  const [couponLoading, setCouponLoading] = useState(false);
  const [showCouponApplyField, setShowCouponApplyField] = useState(false);
  const [tempCoupon, setTempCoupon] = useState(undefined);
  const [isHundredPercentCoupon, setHundredPercentCoupon] = useState(false);

  const [hasCouponErr, setHasCouponErr] = useState(false);
  const [couponErr, setCouponErr] = useState([]);

  useEffect(() => {
    if (!isModalShown) {
      resetCouponForm();
      setShowCouponApplyField(false);
      setFinalPlan({ ...plan, coupon: null, coupon_code: null, coupon_desc: null });
    }
  }, [isModalShown, plan]);

  useEffect(() => {
    // @ts-ignore
    setFinalPlan(plan);
  }, [plan]);

  const resetCouponForm = () => {
    setTempCoupon(undefined);
    setHasCouponErr(false);
    setCouponErr([]);
    setCouponLoading(false);
  };

  const updateValidatedCoupon = (data) => {
    const { message, plan_data: newFinalPlan } = data;
    shopify.toast.show(message);
    resetCouponForm();
    setShowCouponApplyField(false);
    if (newFinalPlan) {
      setFinalPlan(newFinalPlan);
      setHundredPercentCoupon(!!newFinalPlan?.hundred_percent_coupon_applied);
    }
  };

  const { mutate: subscribeToPlan, isPending: checkoutLoading } = useAppMutation({
    mutationKey: ["subscribeToPlan"],
    mutationFn: async () =>
      handleSubscription({
        slug: finalPlan?.slug,
        coupon: finalPlan?.coupon_code,
      }),
    onSuccess: (data) => {
      const { confirmation_url: confirmationURL, is_free_subscription: isFreeSubscription, message = null } = data;

      if (isFreeSubscription) {
        redirectAfterSubscription(`${confirmationURL}`, "_self");
        // eslint-disable-next-line no-unused-expressions
        message && shopify.toast.show(message);
      } else {
        redirectAfterSubscription(confirmationURL, "_parent");
      }
    },
    onError: (error) => {
      console.error("User Subscription error", error);
    },
    options: {
      retry: 2,
    },
  });

  const handelSubmitCoupon = async (e) => {
    e.preventDefault();
    if (tempCoupon && finalPlan) {
      try {
        setCouponLoading(true);
        const response = await fetch("/api/validate-coupon", {
          method: "POST",
          body: JSON.stringify({
            slug: finalPlan.slug,
            coupon: tempCoupon,
          }),
          headers: {
            "Content-Type": "application/json",
          },
        });

        // Check if the response is successful
        if (!response.ok) {
          const error = await getApiError(response);
          console.error("API Error:", error);
          // @ts-ignore
          setCouponErr(error);
          setHasCouponErr(true);
        }

        const data = await response.json();
        updateValidatedCoupon(data);
      } catch (err) {
        const error = await getApiError(err);
        console.error("API Error:", error);
        setCouponLoading(false);
      }
    }
  };
  const handelRemoveCoupon = async (e) => {
    e.preventDefault();
    try {
      if (finalPlan) {
        setCouponLoading(true);
        const response = await fetch(`api/checkout/${plan.slug}`);
        // Check if the response is successful
        if (!response.ok) {
          const error = await getApiError(response);
          console.error("API Error:", error);
          // @ts-ignore
          resetCouponForm();
        }

        const { message, plan_data: newFinalPlan } = await response.json();
        shopify.toast.show(message);
        setFinalPlan(newFinalPlan);
        setShowCouponApplyField(false);
        setCouponLoading(false);
      }
    } catch (err) {
      const error = await getApiError(err);
      console.error("API Error:", error);
      setCouponLoading(false);
    }
  };

  const handelSubmitCheckout = async (e, desPlan) => {
    e.preventDefault();
    const isDowngrade = desPlan.is_free;
    return subscribeToPlan();
  };

  return (
    <Box padding="400">
      <BlockStack gap="400">
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          <Text
            as="h2"
            variant="headingSm"
          >
            Plan name
          </Text>
          <Text
            as="h2"
            variant="headingSm"
          >
            {finalPlan?.name}
          </Text>
        </InlineStack>
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          <Text
            as="h2"
            variant="headingSm"
          >
            Plan price
          </Text>
          <Text
            as="h2"
            variant="headingSm"
          >
            ${finalPlan?.is_free ? toInteger(finalPlan?.price) : Number(finalPlan?.price).toFixed(2)}/
            {finalPlan?.interval_text}
          </Text>
        </InlineStack>
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          <Text
            as="h2"
            variant="headingSm"
          >
            Duration
          </Text>
          <Text
            as="h2"
            variant="headingSm"
          >
            {`${finalPlan?.duration} ${finalPlan?.interval_text}`}
            {finalPlan?.duration > 1 && "s"}
          </Text>
        </InlineStack>
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          <Text
            as="h2"
            variant="headingSm"
          >
            Sub total
          </Text>
          <Text
            as="h3"
            variant="headingSm"
          >
            ${finalPlan?.is_free ? toInteger(finalPlan?.sub_total) : Number(finalPlan?.sub_total).toFixed(2)}
          </Text>
        </InlineStack>
        {finalPlan &&
          !finalPlan?.is_free &&
          finalPlan?.slug !== `free-${planIntervals.LIFETIME}` &&
          (!finalPlan?.coupon_code ? (
            <InlineStack
              align="space-between"
              blockAlign="center"
            >
              {!showCouponApplyField ? (
                // eslint-disable-next-line jsx-a11y/anchor-is-valid
                <Link
                  monochrome
                  onClick={() => setShowCouponApplyField(true)}
                >
                  <Text
                    as="p"
                    variant="bodyMd"
                    fontWeight="semibold"
                  >
                    Do you have any coupon code?
                  </Text>
                </Link>
              ) : (
                <Box width="100%">
                  <TextField
                    label="Give your coupon code"
                    labelAction={{
                      content: "Close",
                      onAction: () => {
                        setShowCouponApplyField(false);
                        resetCouponForm();
                      },
                    }}
                    connectedRight={
                      <Button
                        loading={couponLoading}
                        onClick={(e) => handelSubmitCoupon(e)}
                      >
                        {hasCouponErr ? "Retry" : "Apply"}
                      </Button>
                    }
                    onChange={(val) => {
                      setTempCoupon(val);
                      setHasCouponErr(false);
                      setCouponErr([]);
                    }}
                    value={tempCoupon}
                    clearButton
                    onClearButtonClick={resetCouponForm}
                    error={hasCouponErr && couponErr}
                    placeholder="Coupon code"
                    autoComplete="off"
                  />
                </Box>
              )}
            </InlineStack>
          ) : (
            <InlineStack
              align="space-between"
              blockAlign="center"
            >
              <InlineStack>
                <Text
                  as="h3"
                  variant="headingSm"
                >
                  Discount({finalPlan?.coupon_code})
                </Text>
                <Button
                  variant="plain"
                  tone="critical"
                  onClick={(e) => handelRemoveCoupon(e)}
                  icon={XSmallIcon}
                />
              </InlineStack>
              <Text
                as="h3"
                variant="headingSm"
              >
                -${Number(finalPlan?.discount).toFixed(2)}
              </Text>
            </InlineStack>
          ))}
        <Bleed>
          <Divider />
        </Bleed>
        <InlineStack
          align="space-between"
          blockAlign="center"
        >
          <Text
            as="h3"
            variant="headingSm"
          >
            Total
          </Text>
          <Text
            as="h3"
            variant="headingSm"
          >
            ${finalPlan?.is_free ? toInteger(finalPlan?.final_price) : Number(finalPlan?.final_price).toFixed(2)}
          </Text>
        </InlineStack>
        <Bleed marginInline="400">
          <Divider />
        </Bleed>

        <InlineStack
          align="end"
          gap="200"
        >
          <Button onClick={() => shopify.modal.hide(checkoutModalId)}>Cancel</Button>
          <Button
            variant="primary"
            onClick={(e) => {
              handelSubmitCheckout(e, finalPlan);
            }}
            loading={checkoutLoading}
            disabled={checkoutLoading}
          >
            Checkout
          </Button>
        </InlineStack>
      </BlockStack>
    </Box>
  );
};

export default CheckoutModalContent;
