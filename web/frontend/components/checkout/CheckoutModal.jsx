/* eslint-disable react/prop-types */
import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";

import { useState } from "react";
import { checkoutModalId } from "../../utilities/helpers/global";
import CheckoutModalContent from "./CheckoutModalContent";

/**
 *
 * @param {{plan: object}} props
 */
const CheckoutModal = ({ plan }) => {
  const shopify = useAppBridge();
  const [isModalShown, setIsModalShown] = useState(false);

  const redirectAfterSubscription = (path, target = "self") => {
    shopify.modal.hide(checkoutModalId);
    window.open(path, target);
  };

  return (
    <Modal
      id={checkoutModalId}
      onHide={() => setIsModalShown(false)}
      onShow={() => setIsModalShown(true)}
    >
      <TitleBar title="Checkout" />
      {plan && (
        <div className="app_bridge_modal">
          <CheckoutModalContent
            redirectAfterSubscription={redirectAfterSubscription}
            plan={plan}
            isModalShown={isModalShown}
          />
        </div>
      )}
    </Modal>
  );
};

export default CheckoutModal;
