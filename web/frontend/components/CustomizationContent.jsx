import { Crisp } from "crisp-sdk-web";
import { bool, func, object } from "prop-types";

import {
  BlockStack,
  Box,
  Button,
  Card,
  InlineGrid,
  Layout,
  Link,
  List,
  SkeletonBodyText,
  Text,
  TextField,
} from "@shopify/polaris";

import StylingSection from "./StylingSection";

/**
 * @param {'border-width' | 'border-color'} type
 * @param {string | undefined} [defaultValue]
 */
function createPropertyInterceptors(type, defaultValue) {
  const propertyToValue = (/** @type {string | undefined} */ property) => {
    const propertyValue = property || defaultValue;

    switch (type) {
      case "border-width":
        return propertyValue?.split(" ")[3];
      case "border-color": {
        const boxShadowValues = propertyValue?.split(" ");
        const colorValue = boxShadowValues?.slice(4, boxShadowValues.length).join(" ");

        return colorValue?.startsWith("#") ? colorValue : undefined;
      }
      default:
        return undefined;
    }
  };

  const valueToProperty = (/** @type {string} */ newValue, /** @type {string | undefined} */ oldProperty) => {
    const boxShadowValues = (oldProperty || defaultValue)?.split(" ");
    if (!boxShadowValues) {
      return undefined;
    }

    switch (type) {
      case "border-width":
        boxShadowValues[3] = newValue;
        break;
      case "border-color":
        boxShadowValues.splice(4, boxShadowValues.length - 4, newValue);
        break;
      default:
        break;
    }

    return boxShadowValues.join(" ");
  };

  return { propertyToValue, valueToProperty };
}

/**
 * @param {{
 *   isLoading: boolean,
 *   customizations: StyleOptions,
 *   onCustomizationsChange: (newCustomizations: StyleOptions) => void,
 * }} props
 */
export default function CustomizationContent({ isLoading, customizations, onCustomizationsChange }) {
  return (
    <>
      <Box paddingBlockEnd="400">
        <InlineGrid
          columns={{ sm: 1, md: 2 }}
          gap="400"
        >
          <Card>
            <Text
              as="h2"
              variant="headingMd"
            >
              Need any styling changes?
            </Text>
            <Box paddingBlockStart="200">
              <BlockStack gap="200">
                <Text
                  as="p"
                  variant="bodyMd"
                >
                  We can change the style of your options in any way you like. Free of charge.
                </Text>
                <Box>
                  <Button
                    onClick={() => {
                      Crisp.message.send("text", "I need help with styling.");
                      Crisp.chat.open();
                    }}
                  >
                    Styling help
                  </Button>
                </Box>
              </BlockStack>
            </Box>
          </Card>
          <Card>
            <Text
              as="h2"
              variant="headingMd"
            >
              Need help from experts?
            </Text>
            <Box paddingBlockStart="200">
              <BlockStack gap="200">
                <Text
                  as="p"
                  variant="bodyMd"
                >
                  We can help customize the option types used in your Shopify store, it is fully{" "}
                  <Text
                    as="span"
                    variant="bodySm"
                    fontWeight="semibold"
                  >
                    FREE
                  </Text>
                  .
                </Text>
                <Box>
                  <Button
                    onClick={() => {
                      window.open("https://storeware.io/easyflow/talk-with-expert", "_blank");
                    }}
                  >
                    Schedule a call
                  </Button>
                </Box>
              </BlockStack>
            </Box>
          </Card>
        </InlineGrid>
      </Box>

      <Layout>
        <StylingSection
          title="Font styles"
          description="Customize the style of fonts on the storefront."
          isLoading={isLoading}
          values={customizations.cssCustomizations}
          onValuesChange={(newValues) => onCustomizationsChange({ ...customizations, cssCustomizations: newValues })}
          options={[
            { groupTitle: "Option title", groupSelector: ".ef__option-title" },
            { groupTitle: "Option description", groupSelector: ".ef__option-description" },
            { groupTitle: "Values", groupSelector: ".ef__option-values" },
            {
              groupTitle: "Text input",
              groupSelector: "input[type='text'], input[type='number'], input[type='date'], textarea",
            },
          ].map(({ groupTitle, groupSelector }) => ({
            groupTitle,
            groupSelector,
            group: [
              { label: "Font color", type: "color", propertyName: "color" },
              {
                label: "Font size",
                type: "size-px",
                propertyName: "font-size",
                defaultValue: groupTitle === "Option description" ? "12px" : "14px",
              },
              {
                label: "Font weight",
                type: "font-weight",
                propertyName: "font-weight",
                defaultValue: groupTitle === "Option title" ? "700" : "400",
              },
              {
                label: "Font style",
                type: "font-style",
                propertyName: "font-style",
                defaultValue: "normal",
              },
            ],
          }))}
        />

        <StylingSection
          title="Button styles"
          description="Customize the style of buttons on the storefront."
          isLoading={isLoading}
          values={customizations.cssCustomizations}
          onValuesChange={(newValues) => onCustomizationsChange({ ...customizations, cssCustomizations: newValues })}
          options={[
            {
              groupTitle: "Default",
              groupSelector: ".product-form__input--pill input[type='radio'] + label",
              group: [
                {
                  label: "Border width",
                  type: "size-px",
                  propertyName: "border",
                  propertyToValue: (property) => property && parseFloat(property).toString(),
                  valueToProperty: (newValue) => `${newValue} solid`,
                },
                { label: "Border radius", type: "size-px", propertyName: "border-radius" },
              ],
            },
            ...[
              { groupSelector: ".product-form__input--pill input[type='radio'] + label" },
              {
                groupTitle: "On hover",
                groupSelector: ".product-form__input--pill input[type='radio'] + label:hover",
              },
              {
                groupTitle: "Selected",
                groupSelector: ".product-form__input--pill input[type='radio']:checked + label",
              },
            ].map(
              ({ groupTitle, groupSelector }) =>
                /** @type {const} */ ({
                  groupTitle,
                  groupSelector,
                  group: [
                    { label: "Background color", type: "color", propertyName: "background-color" },
                    { label: "Border color", type: "color", propertyName: "border-color" },
                    { label: "Text color", type: "color", propertyName: "color" },
                  ],
                })
            ),
          ]}
        />

        <StylingSection
          title="Swatch styles"
          description="Customize the style of the image and color swatches on the storefront."
          isLoading={isLoading}
          values={customizations.cssCustomizations}
          onValuesChange={(newValues) => onCustomizationsChange({ ...customizations, cssCustomizations: newValues })}
          options={[
            {
              groupTitle: "Default",
              groupSelector: "label.ef__swatch-image, label.ef__swatch-color",
              group: [
                { label: "Width", type: "size-px", propertyName: "width", defaultValue: "48px" },
                { label: "Height", type: "size-px", propertyName: "height", defaultValue: "48px" },
                { label: "Border radius", type: "size-px", propertyName: "border-radius" },
              ],
            },
            {
              groupTitle: "Selected",
              groupSelector: ".ef__swatch-input:checked + label",
              group: [
                {
                  label: "Border color",
                  type: "color",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-color",
                    "0 0 0 2px rgba(var(--ef-color-foreground-selected), var(--ef-border-opacity-selected))"
                  ),
                },
                {
                  label: "Border width",
                  type: "size-px",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-width",
                    "0 0 0 2px rgba(var(--ef-color-foreground-selected), var(--ef-border-opacity-selected))"
                  ),
                },
              ],
            },
          ]}
        />

        <StylingSection
          title="Input styles"
          description="Customize the style of text box, multi-line text box, number field and date picker on the storefront."
          isLoading={isLoading}
          values={customizations.cssCustomizations}
          onValuesChange={(newValues) => onCustomizationsChange({ ...customizations, cssCustomizations: newValues })}
          options={[
            {
              groupSelector: ".field:not(.ef__option-value-file-upload)",
              group: [
                {
                  label: "Width",
                  type: "size-percent",
                  propertyName: "width",
                  defaultValue: "100%",
                },
              ],
            },
            {
              isInline: true,
              groupSelector: '.field input:not([type="button"]), .field textarea',
              group: [{ label: "Background color", type: "color", propertyName: "background-color" }],
            },
            {
              isInline: true,
              groupSelector:
                ".field:not(.ef__option-value-file-upload)::after, .field:not(.ef__option-value-file-upload):hover::after, .field:not(.ef__option-value-file-upload) input:focus",
              group: [
                {
                  label: "Border color",
                  type: "color",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-color",
                    "0 0 0 var(--ef-border-width) rgba(var(--ef-border-color), var(--ef-border-opacity))"
                  ),
                },
                {
                  label: "Border width",
                  type: "size-px",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-width",
                    "0 0 0 var(--ef-border-width) rgba(var(--ef-border-color), var(--ef-border-opacity))"
                  ),
                },
              ],
            },
          ]}
        />

        <StylingSection
          title="Dropdown"
          description="Customize the style of dropdowns on the storefront."
          isLoading={isLoading}
          values={customizations.cssCustomizations}
          onValuesChange={(newValues) => onCustomizationsChange({ ...customizations, cssCustomizations: newValues })}
          options={[
            {
              groupSelector: ".ef__option-values-dropdown",
              group: [
                {
                  label: "Width",
                  type: "size-percent",
                  propertyName: "width",
                  defaultValue: "100%",
                },
              ],
            },
            {
              isInline: true,
              groupSelector: ".ef__option-values-dropdown select",
              group: [{ label: "Background color", type: "color", propertyName: "background-color" }],
            },
            {
              isInline: true,
              groupSelector:
                ".ef__option-values-dropdown::after, .ef__option-values-dropdown:hover::after, select:focus",
              group: [
                {
                  label: "Border color",
                  type: "color",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-color",
                    "0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-border-opacity))"
                  ),
                },
                {
                  label: "Border width",
                  type: "size-px",
                  propertyName: "box-shadow",
                  ...createPropertyInterceptors(
                    "border-width",
                    "0 0 0 var(--inputs-border-width) rgba(var(--color-foreground), var(--inputs-border-opacity))"
                  ),
                },
              ],
            },
          ]}
        />

        <Layout.AnnotatedSection
          title="Customize option styles with CSS"
          description={
            <BlockStack gap="200">
              <Text as="p">
                You can write your own CSS code using the classes below to customize the style of the options.
              </Text>

              <Text as="p">
                Don&apos;t want to code?{" "}
                <Link
                  url="mailto:<EMAIL>?subject=EasyFlow Custom Styles"
                  target="_blank"
                >
                  Contact us
                </Link>{" "}
                and we can make the styling for you free of charge.
              </Text>

              <Text
                as="p"
                fontWeight="semibold"
              >
                CSS classes:
              </Text>

              <List type="bullet">
                <List.Item>ef__product-option-root</List.Item>

                <List.Item>ef__option-title</List.Item>
                <List.Item>ef__option-title-[option type]</List.Item>
                <List.Item>ef__option-selected-values</List.Item>
                <List.Item>ef__option-selected-values-price</List.Item>
                <List.Item>ef__option-title-required</List.Item>

                <List.Item>ef__option-description</List.Item>
                <List.Item>ef__option-description-[option type]</List.Item>

                <List.Item>ef__option-values</List.Item>

                <List.Item>ef__option-value</List.Item>
                <List.Item>ef__option-value-[option type]</List.Item>

                <List.Item>ef__option-value-price</List.Item>

                <List.Item>ef__option-value-description</List.Item>
                <List.Item>ef__option-value-description-[option type]</List.Item>

                <List.Item>ef__options-addon-total</List.Item>
                <List.Item>ef__options-addon-total-amount</List.Item>

                <List.Item>ef__character-counter</List.Item>
              </List>

              <Text
                as="p"
                variant="bodySm"
              >
                where [option type] is one of the following: checkbox, dropdown, image-swatch, radio-button, button,
                text-box, multi-line-text-box, number-field, date-picker, color-swatch
              </Text>
            </BlockStack>
          }
        >
          <Card>
            <Text
              as="h2"
              variant="headingSm"
            >
              CSS Code
            </Text>

            <Box paddingBlock="200">
              {!isLoading ? (
                <TextField
                  label=""
                  value={customizations.cssText}
                  onChange={(newCSS) => onCustomizationsChange({ ...customizations, cssText: newCSS })}
                  autoComplete="css-styles"
                  multiline={21}
                  disabled={isLoading}
                />
              ) : (
                <SkeletonBodyText lines={26} />
              )}
            </Box>
          </Card>
        </Layout.AnnotatedSection>
      </Layout>
    </>
  );
}

CustomizationContent.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  customizations: object.isRequired,
  onCustomizationsChange: func.isRequired,
  isLoading: bool.isRequired,
};
