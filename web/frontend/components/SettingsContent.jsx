import { Crisp } from "crisp-sdk-web";
import { bool, func, object } from "prop-types";

import { BlockStack, Box, Button, Card, Checkbox, InlineStack, Layout, Link, Text, TextField } from "@shopify/polaris";

/**
 * @param {{
 *   isLoading: boolean,
 *   customizations: StyleOptions,
 *   onCustomizationsChange: (newCustomizations: StyleOptions) => void,
 *   isFieldRequiredError: boolean,
 * }} props
 */
export default function SettingsContent({ isLoading, customizations, onCustomizationsChange, isFieldRequiredError }) {
  return (
    <Layout>
      <Layout.AnnotatedSection
        title="Customize how price add-ons work"
        description="Make sure to enable the EasyFlow App Embed for these settings to work properly. If the app embed is enabled the add-on product will be removed from the cart if the base product is removed."
      >
        <Card>
          <BlockStack gap="200">
            <Box paddingBlockEnd="400">
              <Button
                variant="primary"
                onClick={() => window.open(`shopify://admin/themes/current/editor?context=apps`, "_blank")}
              >
                Enable App Embed in your Theme
              </Button>
            </Box>

            <Checkbox
              label="Add-on quantity should match product quantity"
              checked={customizations.isMatchAddonQuantity}
              disabled={isLoading}
              onChange={(newChecked) => onCustomizationsChange({ ...customizations, isMatchAddonQuantity: newChecked })}
            />

            <Checkbox
              label="Disable removing add-on products from the cart"
              checked={!customizations.isEnableRemoveAddonFromCart}
              disabled={isLoading}
              onChange={(newChecked) =>
                onCustomizationsChange({
                  ...customizations,
                  isEnableRemoveAddonFromCart: !newChecked,
                })
              }
            />

            <Checkbox
              label="Disable opening add-on product page from cart"
              checked={!customizations.isEnableOpenAddonProductPage}
              disabled={isLoading}
              onChange={(newChecked) =>
                onCustomizationsChange({
                  ...customizations,
                  isEnableOpenAddonProductPage: !newChecked,
                })
              }
            />

            <Checkbox
              label="Work with “Buy now” button"
              checked={!customizations.isBuyNowIntegrationDisabled}
              disabled={isLoading}
              onChange={(newChecked) =>
                onCustomizationsChange({
                  ...customizations,
                  isBuyNowIntegrationDisabled: !newChecked,
                })
              }
            />

            <Box paddingBlockStart="400">
              <InlineStack gap="200">
                <Text
                  as="p"
                  fontWeight="bold"
                >
                  Something is not working?
                </Text>

                <Button
                  variant="plain"
                  onClick={() => {
                    Crisp.message.send("text", "Cart functionalities are not working.");
                    Crisp.chat.open();
                  }}
                >
                  Get help
                </Button>
              </InlineStack>
            </Box>
          </BlockStack>
        </Card>
      </Layout.AnnotatedSection>

      <Layout.AnnotatedSection title="Customize how add-on prices are displayed">
        <Card>
          <BlockStack gap="200">
            <Checkbox
              label="Change base product price when add-ons are selected"
              checked={!customizations.isChangeBaseProductPriceDisabled}
              disabled={isLoading}
              onChange={(checked) =>
                onCustomizationsChange({
                  ...customizations,
                  isChangeBaseProductPriceDisabled: !checked,
                })
              }
            />

            <Checkbox
              label="Show total price of selected add-ons below the options"
              checked={customizations.isShowTotalPrice}
              disabled={isLoading}
              onChange={(checked) => onCustomizationsChange({ ...customizations, isShowTotalPrice: checked })}
            />

            {customizations.isShowTotalPrice && (
              <TextField
                label="Storefront text"
                placeholder="Total add-ons: %total-addon-amount%"
                helpText="%total-addon-amount% is the total price of selected add-ons, %total-amount% is the base product price + the total price of selected add-ons."
                autoComplete="storefront-text"
                disabled={isLoading}
                value={customizations.storefrontFooterText}
                onChange={(newText) => onCustomizationsChange({ ...customizations, storefrontFooterText: newText })}
              />
            )}
          </BlockStack>
        </Card>
      </Layout.AnnotatedSection>

      <Layout.AnnotatedSection
        title="Customize system text"
        description="Enable the EasyFlow App Embed if you want the add-on product to be removed from the cart if the base product is removed."
      >
        <BlockStack gap="400">
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Files upload
              </Text>
              <TextField
                label="File upload button"
                autoComplete="file-upload-button"
                disabled={isLoading}
                value={customizations.fileUploadButtonText}
                error={isFieldRequiredError && !customizations.fileUploadButtonText}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, fileUploadButtonText: newValue })}
              />

              <TextField
                label="File upload drag files"
                autoComplete="file-upload-drag-files"
                disabled={isLoading}
                value={customizations.dragAndDropText}
                error={isFieldRequiredError && !customizations.dragAndDropText && "This field is required."}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, dragAndDropText: newValue })}
              />

              <TextField
                label="Files uploading"
                autoComplete="dropdown-placeholder"
                disabled={isLoading}
                value={customizations.uploadLoadingText}
                error={isFieldRequiredError && !customizations.uploadLoadingText && "This field is required."}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, uploadLoadingText: newValue })}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Dropdown
              </Text>
              <TextField
                label="Dropdown placeholder"
                autoComplete="dropdown-placeholder"
                disabled={isLoading}
                value={customizations.dropdownPlaceholder}
                error={isFieldRequiredError && !customizations.dropdownPlaceholder}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, dropdownPlaceholder: newValue })}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                File upload errors
              </Text>
              <TextField
                label="Not allowed file type error"
                autoComplete="not-allowed-file-type-error"
                disabled={isLoading}
                value={customizations.fileTypeErrorText}
                error={isFieldRequiredError && !customizations.fileTypeErrorText && "This field is required."}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, fileTypeErrorText: newValue })}
              />

              <TextField
                label="File size error"
                autoComplete="file-size-error"
                disabled={isLoading}
                value={customizations.fileSizeErrorText}
                error={isFieldRequiredError && !customizations.fileSizeErrorText && "This field is required."}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, fileSizeErrorText: newValue })}
              />

              <TextField
                label="File upload error"
                autoComplete="file-upload-error"
                disabled={isLoading}
                value={customizations.fileUploadErrorText}
                error={isFieldRequiredError && !customizations.fileUploadErrorText && "This field is required."}
                onChange={(newValue) => onCustomizationsChange({ ...customizations, fileUploadErrorText: newValue })}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Errors
              </Text>

              <TextField
                label="Required option error"
                autoComplete="required-option-error"
                disabled={isLoading}
                value={customizations.requiredOptionErrorText}
                error={isFieldRequiredError && !customizations.requiredOptionErrorText && "This field is required."}
                onChange={(newValue) =>
                  onCustomizationsChange({ ...customizations, requiredOptionErrorText: newValue })
                }
              />

              <TextField
                label="Minimum selection error"
                autoComplete="minimum-selection-error"
                disabled={isLoading}
                value={customizations.minimumSelectionErrorText}
                error={isFieldRequiredError && !customizations.minimumSelectionErrorText && "This field is required."}
                onChange={(newValue) =>
                  onCustomizationsChange({ ...customizations, minimumSelectionErrorText: newValue })
                }
              />

              <TextField
                label="Maximum selection error"
                autoComplete="maximum-selection-error"
                disabled={isLoading}
                value={customizations.maximumSelectionErrorText}
                error={isFieldRequiredError && !customizations.maximumSelectionErrorText && "This field is required."}
                onChange={(newValue) =>
                  onCustomizationsChange({ ...customizations, maximumSelectionErrorText: newValue })
                }
              />

              <TextField
                label="Minimum character error"
                autoComplete="minimum-character-error"
                disabled={isLoading}
                value={customizations.mimumCharacterErrorText}
                error={isFieldRequiredError && !customizations.mimumCharacterErrorText && "This field is required."}
                onChange={(newValue) =>
                  onCustomizationsChange({ ...customizations, mimumCharacterErrorText: newValue })
                }
              />
            </BlockStack>
          </Card>
        </BlockStack>
      </Layout.AnnotatedSection>

      <Layout.AnnotatedSection
        title="(Advanced) Customize app embed behavior"
        description={
          <BlockStack gap="200">
            <Text as="p">
              For some functionalities to work the app embed relies on CSS selectors that might not work in all themes.
              In that case, you can specify the CSS selectors for these functionalities to make sure they work as
              expected.
            </Text>

            <Text as="p">
              If you&apos;re unsure,{" "}
              <Link
                url="mailto:<EMAIL>?subject=EasyFlow Custom Styles"
                target="_blank"
              >
                contact us
              </Link>{" "}
              and we&apos;re happy to help you.
            </Text>
          </BlockStack>
        }
      >
        <BlockStack gap="400">
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Option placement
              </Text>
              <TextField
                label="Options placement on product page"
                helpText="CSS selector above which the options should show. Only needed if you can’t add app blocks to the product page."
                value={customizations.appEmbedPlacementSelector}
                onChange={(newSelector) =>
                  onCustomizationsChange({
                    ...customizations,
                    appEmbedPlacementSelector: newSelector,
                  })
                }
                autoComplete="app-embed-placement-selector"
                disabled={isLoading}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Price modifications
              </Text>
              <TextField
                label="Regular price item"
                helpText="CSS selector to target the price item that is not a sale price item on the product page."
                value={customizations.regularPriceItemSelector}
                onChange={(newSelector) =>
                  onCustomizationsChange({
                    ...customizations,
                    regularPriceItemSelector: newSelector,
                  })
                }
                autoComplete="regular-price-item-selector"
                disabled={isLoading}
              />

              <TextField
                label="Compare at price item"
                helpText="CSS selector to target the compare at price item on the product page."
                value={customizations.compareAtPriceItemSelector}
                onChange={(newSelector) =>
                  onCustomizationsChange({
                    ...customizations,
                    compareAtPriceItemSelector: newSelector,
                  })
                }
                autoComplete="sale-price-item-selector"
                disabled={isLoading}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Cart modifications
              </Text>
              <TextField
                label="Cart line"
                helpText="CSS selector for the cart line to hide the remove and quantity buttons for the add-ons. Write %line-number% to use the line number in the selector."
                value={customizations.cartLineSelector}
                onChange={(newSelector) => onCustomizationsChange({ ...customizations, cartLineSelector: newSelector })}
                autoComplete="cart-line-selector"
                disabled={isLoading}
              />

              <TextField
                label="Quantity and remove button in cart"
                helpText="CSS selector to target the remove and quantity input in the cart to hide it for add-ons."
                value={customizations.quantityAndRemoveButtonSelector}
                onChange={(newSelector) =>
                  onCustomizationsChange({
                    ...customizations,
                    quantityAndRemoveButtonSelector: newSelector,
                  })
                }
                autoComplete="quantity-remove-button-selector"
                disabled={isLoading}
              />
            </BlockStack>
          </Card>
          <Card>
            <BlockStack gap="200">
              <Text
                as="h5"
                variant="headingSm"
              >
                Shopify prices
              </Text>
              <Checkbox
                label="Support Shopify market prices"
                helpText="Only supports up to 20 add-on products per product page"
                checked={customizations.isGetPricesOnStorefront}
                disabled={isLoading}
                onChange={(newChecked) =>
                  onCustomizationsChange({
                    ...customizations,
                    isGetPricesOnStorefront: newChecked,
                  })
                }
              />
            </BlockStack>
          </Card>
        </BlockStack>
      </Layout.AnnotatedSection>
    </Layout>
  );
}

SettingsContent.propTypes = {
  isLoading: bool.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  customizations: object.isRequired,
  onCustomizationsChange: func.isRequired,
  isFieldRequiredError: bool.isRequired,
};
