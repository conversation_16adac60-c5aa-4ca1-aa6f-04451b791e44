import { bool, func, string } from "prop-types";
import { useEffect, useState } from "react";

import { Button } from "@shopify/polaris";

import useNavigationHook from "../hooks/useNavigationHook";
import OptionsTemplateModal from "./options-templates";
import { useStore } from "./providers/StoreProvider";

/**
 * @param {{
 *   label: string,
 *   onCreate: (option: ProductOptionType) => void,
 *   onOpen?: () => void,
 *   primary?: boolean,
 * }} props
 */
export default function CreateOptionButton({ label, onCreate, onOpen, primary }) {
  const { shopDetails } = useStore();
  const { navigateTo } = useNavigationHook();

  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (onOpen && isModalOpen) {
      onOpen();
    }
  }, [onOpen, isModalOpen]);

  return (
    <>
      <Button
        variant={primary ? "primary" : undefined}
        onClick={() => setIsModalOpen(true)}
      >
        {label}
      </Button>
      <OptionsTemplateModal
        open={isModalOpen}
        onHideCallback={() => setIsModalOpen(false)}
        onCreate={(type) => {
          setIsModalOpen(false);
          if (shopDetails?.isFeatureLimited && type === "File upload") {
            navigateTo("/pricing");
          } else {
            onCreate(type);
          }
        }}
      />
    </>
  );
}

CreateOptionButton.propTypes = {
  label: string.isRequired,
  onCreate: func.isRequired,
  onOpen: func,
  primary: bool,
};
