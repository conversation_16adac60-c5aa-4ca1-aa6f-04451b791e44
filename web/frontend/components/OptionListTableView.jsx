import { array, bool, func, object, string } from "prop-types";
import { useCallback, useEffect, useState } from "react";

import {
  Badge,
  Box,
  IndexFilters,
  IndexTable,
  Text,
  useIndexResourceState,
  useSetIndexFiltersMode,
} from "@shopify/polaris";

/** @typedef {'Option' | 'Option set'} ItemType */

/**
 * @param {{
 *  items: ({
 *   id: string,
 *   itemId: number,
 *   type: string,
 *   title: string,
 *   optionsText: string,
 *   productsText: string,
 *   optionTypeText: string,
 *   valuesText: string,
 *   onClick: () => void,
 *  })[],
 *  isLoading?: boolean,
 *  isLimitReached?: boolean,
 *  onDuplicate?: (optionSetIds: number[], optionIds: number[]) => void,
 *  onDelete?: (optionSetIds: number[], optionIds: number[]) => void,
 *  onOptionSetEdit?: (optionSet: Partial<IProductOptionSet>) => void,
 *  sortSelected: { sortBy: string, isDescending: boolean },
 *  setSortSelected: (sortSelected: { sortBy: string, isDescending: boolean }) => void,
 *  setTitleFilter: (title: string) => void,
 *  setTypeFilter: (type: ItemType[]) => void,
 *  titleFilter: string,
 * }} props
 */
export default function OptionListTableView({
  items,
  isLoading,
  isLimitReached,
  titleFilter,
  sortSelected,
  onDelete,
  onOptionSetEdit,
  onDuplicate,
  setSortSelected,
  setTitleFilter,
  setTypeFilter,
}) {
  const [selected, setSelected] = useState(0);

  const tabs = ["All", "Option set", "Option"].map((item, index) => ({
    content: item,
    index,
    onAction: () => {},
    id: `${item}-${index}`,
    isLocked: index === 0,
    actions: [],
  }));

  const { mode, setMode } = useSetIndexFiltersMode();

  const handleSelectTab = useCallback(
    (/** @type {import("react").SetStateAction<number>} */ selectedTabIndex) => {
      setSelected(selectedTabIndex);
      // @ts-ignore
      setTypeFilter(selectedTabIndex === 0 ? [] : [tabs[selectedTabIndex].content]);
    },
    [setTypeFilter, tabs]
  );

  /** @type {import('@shopify/polaris').SortButtonChoice[]} */
  const sortOptions = [
    { label: "Type", value: "type asc", directionLabel: "A-Z" },
    { label: "Type", value: "type desc", directionLabel: "Z-A" },
    { label: "Title", value: "title asc", directionLabel: "A-Z" },
    { label: "Title", value: "title desc", directionLabel: "Z-A" },
  ];

  const resourceName = {
    singular: "option",
    plural: "options",
  };

  const { selectedResources, allResourcesSelected, handleSelectionChange, clearSelection } =
    useIndexResourceState(items);

  /** @type {(type: ItemType) => number[]} */
  const getSelectedItemIds = (type) =>
    items.flatMap((item) => (selectedResources.includes(item.id) && item.type === type ? item.itemId : []));

  useEffect(() => {
    clearSelection();
  }, [items, clearSelection]);

  return (
    <>
      <IndexFilters
        loading={isLoading}
        sortOptions={sortOptions}
        sortSelected={[`${sortSelected.sortBy} ${sortSelected.isDescending ? "desc" : "asc"}`]}
        onSort={(value) => {
          const [sortBy, isDescending] = value[0].split(" ");

          setSortSelected({ sortBy, isDescending: isDescending === "desc" });
        }}
        queryValue={titleFilter}
        queryPlaceholder="Searching in all"
        onQueryChange={setTitleFilter}
        onQueryClear={() => setTitleFilter("")}
        cancelAction={{
          onAction: () => setTitleFilter(""),
          disabled: false,
          loading: false,
        }}
        tabs={tabs}
        selected={selected}
        onSelect={handleSelectTab}
        canCreateNewView={false}
        filters={[]}
        appliedFilters={[]}
        onClearAll={() => {
          setTitleFilter("");
          setTypeFilter([]);
        }}
        mode={mode}
        setMode={setMode}
      />

      <IndexTable
        promotedBulkActions={
          !isLimitReached
            ? [
                ...(selectedResources.some((id) => items.find((item) => item.id === id && item.type === "Option"))
                  ? [
                      {
                        content: "Add options to products",
                        onAction: () => onOptionSetEdit?.({ optionIds: getSelectedItemIds("Option") }),
                      },
                    ]
                  : []),
                {
                  content: "Duplicate",
                  onAction: () => onDuplicate?.(getSelectedItemIds("Option set"), getSelectedItemIds("Option")),
                },
              ]
            : []
        }
        bulkActions={[
          {
            content: "Delete",
            onAction: () => onDelete?.(getSelectedItemIds("Option set"), getSelectedItemIds("Option")),
          },
        ]}
        resourceName={resourceName}
        itemCount={items.length}
        selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
        onSelectionChange={handleSelectionChange}
        headings={[
          { title: "Type" },
          { title: "Title" },
          { title: "Options" },
          { title: "Products" },
          { title: "Option type" },
          { title: "Values" },
        ]}
      >
        {items.map(({ id, type, title, optionsText, productsText, optionTypeText, valuesText, onClick }, index) => (
          <IndexTable.Row
            disabled={isLoading}
            id={id}
            key={`${id}-${Math.random()}`}
            selected={selectedResources.includes(id)}
            position={index}
            onClick={() => onClick()}
          >
            <IndexTable.Cell>
              <Box
                paddingBlockStart="200"
                paddingBlockEnd="200"
              >
                <Badge tone={type === "Option set" ? "info" : undefined}>{type}</Badge>
              </Box>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <Text
                variant="bodyMd"
                fontWeight="bold"
                as="span"
              >
                {title}
              </Text>
            </IndexTable.Cell>
            <IndexTable.Cell>{optionsText}</IndexTable.Cell>
            <IndexTable.Cell>{productsText}</IndexTable.Cell>
            <IndexTable.Cell>{optionTypeText}</IndexTable.Cell>
            <IndexTable.Cell>{valuesText}</IndexTable.Cell>
          </IndexTable.Row>
        ))}
      </IndexTable>
    </>
  );
}

OptionListTableView.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  items: array.isRequired,
  isLoading: bool,
  isLimitReached: bool,
  titleFilter: string.isRequired,
  setTitleFilter: func.isRequired,
  setTypeFilter: func.isRequired,
  onDuplicate: func.isRequired,
  onDelete: func.isRequired,
  onOptionSetEdit: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  sortSelected: object.isRequired,
  setSortSelected: func.isRequired,
};
