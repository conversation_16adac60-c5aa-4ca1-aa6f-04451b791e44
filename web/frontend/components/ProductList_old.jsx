import { arrayOf, func, string } from 'prop-types';
import { useCallback, useMemo, useState } from 'react';

import { Avatar, Filters, ResourceList, Text } from '@shopify/polaris';

import { useAppQuery } from '../utilities/hooks';

/**
 * @param {{
 *   selectedIds: string[],
 *   onSelectedIdsChange: (newSelectedIds: string[]) => void,
 * }} props
 */
export default function ProductList({ selectedIds, onSelectedIdsChange }) {
  const [titleFilter, setTitleFilter] = useState('');

  /** @type {TypedStateOptional<string>} */
  const [cursor, setCursor] = useState();

  /** @type {TypedStateOptional<string>} */
  const [paginationType, setPaginationType] = useState();

  // old
  // const url = useMemo(() => {
  //   const query = new URLSearchParams();

  //   if (titleFilter) query.append('searchTerm', titleFilter);
  //   if (cursor) query.append('cursor', cursor);
  //   if (paginationType) query.append('paginationType', paginationType);

  //   // Show selected products first
  //   if (selectedIds.length) {
  //     selectedIds.forEach((id) => query.append('selectedIds[]', id));
  //   }

  //   return `/api/products/get?${query.toString()}`;
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [titleFilter, cursor, paginationType]);

  // rumi
  const productFetch = useMemo(() => {
    const query = new URLSearchParams();
    const productIds = new Set();

    if (titleFilter) query.append('searchTerm', titleFilter);
    if (cursor) query.append('cursor', cursor);
    if (paginationType) query.append('paginationType', paginationType);

    // Move selectedIds to productIds set
    if (selectedIds.length) {
      selectedIds.forEach((id) => productIds.add(id));
    }

    const apiUrl = `/api/products/get?${query.toString()}`;

    return { apiUrl, productIds }; // Returning both URL and productIds set
  }, [titleFilter, cursor, paginationType, selectedIds]);

  /** @type {UseQueryResult<ProductsGetResponse>} */
  // const { isLoading: isProductsLoading, data } = useAppQuery(url, { debounceMilliseconds: 250 });

  // rumi
  /** @type {UseQueryResult<ProductsGetResponse>} */
  const { isLoading: isProductsLoading, data } = useAppQuery(productFetch.apiUrl, {
    fetchOptions: {
      method: 'POST',
      // @ts-ignore
      body: { selectedIds: Array.from(productFetch.productIds) },
    },
    debounceMilliseconds: 250,
  });

  const products = useMemo(
    () => data?.products.map((product) => ({ ...product, id: product.id.toString() })) || [],
    [data],
  );

  const pageInfo = useMemo(() => data?.pageInfo, [data]);

  const handleTitleFilterChange = useCallback((/** @type {string} */ newTitleFilter) => {
    setTitleFilter(newTitleFilter);
    setCursor(undefined);
    setPaginationType(undefined);
  }, []);

  return (
    <ResourceList
      resourceName={{ singular: 'product', plural: 'products' }}
      loading={isProductsLoading}
      flushFilters
      filterControl={
        <Filters
          filters={[]}
          queryPlaceholder="Search products"
          queryValue={titleFilter}
          onQueryChange={handleTitleFilterChange}
          onQueryClear={() => handleTitleFilterChange('')}
          onClearAll={() => handleTitleFilterChange('')}
        />
      }
      items={products}
      renderItem={({ id, title, imageUrl }, index) => (
        <ResourceList.Item
          key={index}
          id={id}
          verticalAlignment="center"
          onClick={() => {
            const newProductIds = selectedIds.includes(id)
              ? selectedIds.filter((productId) => productId === id)
              : [...(selectedIds || []), id];

            onSelectedIdsChange(newProductIds);
          }}
          media={<Avatar source={imageUrl} />}
          accessibilityLabel={`View details for ${title}`}
        >
          <Text as="h3" variant="bodyMd" fontWeight="bold">
            {title}
          </Text>
        </ResourceList.Item>
      )}
      selectable
      selectedItems={selectedIds}
      pagination={{
        hasNext: !!pageInfo?.hasNextPage && !isProductsLoading,
        hasPrevious: !!pageInfo?.hasPreviousPage && !isProductsLoading,
        onPrevious: () => {
          setPaginationType('before');
          setCursor(products[0].cursor);
        },
        onNext: () => {
          setPaginationType('after');
          setCursor(products[products.length - 1].cursor);
        },
      }}
      onSelectionChange={(newSelectedIds) =>
        onSelectedIdsChange(
          newSelectedIds === 'All' ? products.map((item) => item.id) : newSelectedIds,
        )
      }
    />
  );
}

ProductList.propTypes = {
  selectedIds: arrayOf(string).isRequired,
  onSelectedIdsChange: func.isRequired,
};
