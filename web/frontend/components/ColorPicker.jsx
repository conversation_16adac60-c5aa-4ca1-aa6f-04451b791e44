import { func, string } from 'prop-types';
import { useCallback, useEffect, useState } from 'react';

import {
  BlockStack,
  Box,
  Card,
  ColorPicker as PolarisColorPicker,
  Popover,
  Text,
  TextField,
  hexToRgb,
  hsbToHex,
  rgbToHsb,
} from '@shopify/polaris';

/**
 * @param {{
 *   color: string | undefined,
 *   onColorChange: (color: string) => void,
 *   label?: string,
 * }} props
 */
export default function ColorPicker({ color = '#ffffff', onColorChange, label }) {
  const [isActive, setActive] = useState(false);

  const [hsbaColor, setHSBAColor] = useState({ hue: 300, brightness: 1, saturation: 0.7 });
  const [hexColor, setHexColor] = useState(hsbToHex(hsbaColor));

  const handleColorChange = useCallback(
    (/** @type {string} */ newColor) => {
      if (newColor !== color) {
        onColorChange(newColor);
      }
    },
    [color, onColorChange],
  );

  useEffect(() => {
    setHSBAColor(rgbToHsb(hexToRgb(color)));
    setHexColor(color);
  }, [color]);

  return (
    <Popover
      active={isActive}
      preferredAlignment="left"
      onClose={() => {
        setActive(false);
        handleColorChange(hexColor);
      }}
      activator={
        <>
          {label && (
            <Box paddingBlockEnd="100">
              <Text as="p">{label}</Text>
            </Box>
          )}

          <div
            style={{ width: '48px', height: '48px' }}
            onClick={() => {
              if (isActive) {
                handleColorChange(hexColor);
              }

              setActive(!isActive);
            }}
            aria-hidden="true"
          >
            <div
              className="Polaris-DropZone Polaris-DropZone--hasOutline Polaris-DropZone--sizeSmall"
              style={{ backgroundColor: color }}
            />
          </div>
        </>
      }
    >
      <Card>
        <BlockStack gap="200">
          <PolarisColorPicker
            color={hsbaColor}
            onChange={(value) => {
              setHSBAColor(value);
              setHexColor(hsbToHex(value));
            }}
          />

          <TextField
            placeholder="#ffffff"
            value={hexColor || ''}
            label="Hex value"
            labelHidden
            onChange={(newHexColor) => {
              setHexColor(newHexColor);
              setHSBAColor(rgbToHsb(hexToRgb(newHexColor || '#ffffff')));
            }}
            autoComplete="color"
          />
        </BlockStack>
      </Card>
    </Popover>
  );
}

ColorPicker.propTypes = {
  color: string,
  onColorChange: func.isRequired,
  label: string,
};
