import { arrayOf, bool, func, number, object, string } from 'prop-types';
import { useState } from 'react';

import { But<PERSON>, OptionList, Popover } from '@shopify/polaris';

/**
 * @param {{
 *   valueType: string,
 *   values: { id: string, label: string }[],
 *   selectedItems: string[],
 *   onSelectionChange: (selectedValues: string[]) => void,
 *   isLoading?: boolean,
 *   isFullWidth?: boolean,
 * }} props
 */
export default function SelectValuesButton({
  valueType,
  values,
  selectedItems,
  onSelectionChange,
  isLoading,
  isFullWidth,
}) {
  const [isOptionsOpen, setOptionsOpen] = useState(false);

  const isAllSelected = values.every((value) => selectedItems.includes(value.id));

  return (
    <Popover
      active={isOptionsOpen}
      activator={
        <div style={!isFullWidth ? { maxWidth: '10rem', minWidth: '7rem' } : undefined}>
          <Button
            onClick={() => setOptionsOpen((oldOptionsOpen) => !oldOptionsOpen)}
            disabled={!values.length}
            fullWidth
            disclosure
            size="large"
            loading={isLoading}
          >
            {isAllSelected
              ? `All ${valueType}s`
              : `${selectedItems.length || 'Select'} ${valueType}${
                  !selectedItems.length || selectedItems.length > 1 ? 's' : ''
                }`}
          </Button>
        </div>
      }
      onClose={() => setOptionsOpen(false)}
    >
      <OptionList
        onChange={(newSelection) => {
          if (isAllSelected && !newSelection.includes('All')) {
            onSelectionChange([]);
            return;
          }

          const newSelectedValues = newSelection.flatMap((id) =>
            id === 'All' ? [] : id.substring(6),
          );

          if (
            (!isAllSelected && newSelection.includes('All')) ||
            values.every((value) => newSelectedValues.includes(value.id))
          ) {
            onSelectionChange(values.map(({ id }) => id));
            return;
          }

          onSelectionChange(newSelectedValues);
        }}
        sections={[
          { options: [{ value: 'All', label: 'All' }] },
          {
            title: `${valueType[0].toUpperCase()}${valueType.substring(1, valueType.length)}s`,
            options: values.map(({ id, label }) => ({ label, value: `value-${id}` })),
          },
        ]}
        selected={[
          ...(isAllSelected ? ['All'] : []),
          ...selectedItems.map((value) => `value-${value}`),
        ]}
        allowMultiple
      />
    </Popover>
  );
}

SelectValuesButton.propTypes = {
  valueType: string.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  values: arrayOf(object).isRequired,
  selectedItems: arrayOf(string).isRequired,
  onSelectionChange: func.isRequired,
  isLoading: bool,
  isFullWidth: number,
};
