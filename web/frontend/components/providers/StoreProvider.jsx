// @ts-ignore
import PropTypes from "prop-types";
import { createContext, useCallback, useContext, useEffect, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { useAppMutation, useAppQuery } from "storeware-tanstack-query";
import { handelUpdateShopDetails } from "../../services/shop.service";

// Create the context
export const StoreContext = createContext({
  shopDetails: null,
  refetchShopDetails: () => {},
});

// Create a custom hook to use the store context
export const useStore = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error("useStore must be used within a EasyFlowProvider");
  }
  return context;
};

/**
 * @param {{
 *   children: React.ReactNode,
 * }} props
 */
const StoreProvider = ({ children }) => {
  const location = useLocation();
  const chargeId = new URLSearchParams(window.location.search).get("charge_id");

  const { mutateAsync: updateShopDetails } = useAppMutation({
    mutationKey: ["updateShopDetails"],
    mutationFn: async (/** @type {IShopDetails} */ shopData) => handelUpdateShopDetails(shopData),
    onSuccess: (data, shopData) => {
      if (data?.status === "success") {
        localStorage.setItem("shopDetails", JSON.stringify(shopData));
      }
    },
    onError: (error) => {
      console.error("Shop details update error", error);
    },
    options: {
      retry: 2,
    },
  });

  const checkUpdates = useCallback(async (/** @type {IShopDetails} */ newShopDetails) => {
    if (localStorage.getItem("shopDetails") !== JSON.stringify(newShopDetails)) {
      await updateShopDetails(newShopDetails);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initialShopDetails = useMemo(
    () => JSON.parse(document.getElementById("ef__shop-details")?.textContent || "{}"),
    []
  );

  useEffect(() => {
    checkUpdates(initialShopDetails);
  }, [checkUpdates, initialShopDetails]);

  /** @type {UseQueryResult<IShopDetails>} */
  const { data: shopDetails, refetch: refetchShopDetails } = useAppQuery({
    url: chargeId ? `/api/shop/details?chargeId=${chargeId}` : "/api/shop/details",
    reactQueryOptions: {
      enabled: !location.pathname.includes("/customization") && !location.pathname.includes("/settings"),
      initialData: initialShopDetails,
      staleTime: 1000,
    },
  });

  // eslint-disable-next-line react/jsx-no-constructed-context-values
  const storeValue = {
    shopDetails,
    refetchShopDetails,
  };

  useEffect(() => {
    if (shopDetails) {
      checkUpdates(shopDetails);
    }
  }, [shopDetails, checkUpdates]);

  return (
    <StoreContext.Provider
      // @ts-ignore
      value={storeValue}
    >
      {storeValue.shopDetails ? children : null}
    </StoreContext.Provider>
  );
};

StoreProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default StoreProvider;
