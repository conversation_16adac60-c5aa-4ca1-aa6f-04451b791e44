/* eslint-disable react/prop-types */
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from "react";

const AppErrorContext = createContext(null);

export const useAppErrors = () => useContext(AppErrorContext);

// @ts-ignore
const ErrorProvider = ({ children }) => {
  const [errors, setErrors] = useState({
    onboarding: [],
    optionSet: [],
    option: [],
    settings: [],
    pricing: [],
    customization: [],
  });

  const location = window.location.pathname;

  // Function to reset errors by key
  const resetAppErrorByKey = useCallback((/** @type {String} */ key) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [key]: [],
    }));
  }, []);

  // Function to set errors by key
  const setAppErrorByKey = useCallback((/** @type {String} */ key, /** @type {[]} */ errorMessages) => {
    setErrors((prevErrors) => ({
      ...prevErrors,
      [key]: errorMessages,
    }));
  }, []);

  // Reset errors when navigating to a new page
  useEffect(() => {
    const pageErrors = {
      "/": ["onboarding", "optionSet", "option"],
      "/settings": ["settings"],
      "/pricing": ["pricing"],
      "/customization": ["customization"],
    };

    Object.keys(pageErrors).forEach((path) => {
      if (location === path) {
        // @ts-ignore
        pageErrors[path].forEach(resetAppErrorByKey);
      }
    });
  }, [location, resetAppErrorByKey]);

  const getAllErrors = useCallback(() => {
    /**
     * @type {any[]}
     */
    let allErrors = [];

    Object.values(errors).forEach((errorObj) => {
      if (Object.keys(errorObj).length > 0) {
        allErrors.push(...Object.values(errorObj));
      }
    });

    return {
      hasErrors: allErrors.length > 0,
      // @ts-ignore
      allErrors,
    };
  }, [errors]);

  const { hasErrors, allErrors } = getAllErrors();

  const contextValue = useMemo(
    () => ({
      appErrors: errors,
      setAppErrorByKey,
      resetAppErrorByKey,
      hasAppErrors: hasErrors,
      allAppErrors: allErrors,
    }),
    [errors, setAppErrorByKey, resetAppErrorByKey, hasErrors, allErrors]
  );

  // @ts-ignore
  return <AppErrorContext.Provider value={contextValue}>{children}</AppErrorContext.Provider>;
};

export default ErrorProvider;
