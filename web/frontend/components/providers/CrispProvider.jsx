import { Crisp } from "crisp-sdk-web";
import lodash from "lodash";
import { useEffect } from "react";
import { useAppQuery } from "../../utilities/hooks";

const { get } = lodash;

const websiteId = "57c11e5d-c42e-4758-a784-9a30be2fac1a";

const CrispProvider = () => {
  useEffect(() => {
    Crisp.configure(websiteId, { autoload: false });
  }, []);

  /** @type {UseQueryResult<CrispSessionResponse>} */
  const { data: crispSessionData } = useAppQuery("/api/store/crisp-session");

  useEffect(() => {
    if (crispSessionData) {
      Crisp.setTokenId(crispSessionData.crispSessionId);
      if (crispSessionData.user) {
        Crisp.user.setEmail(get(crispSessionData, "user.email", ""));
      }
      if (crispSessionData.session) {
        Crisp.session.setData({
          ...crispSessionData.session,
        });
      }
      Crisp.load();
    }
  }, [crispSessionData]);

  return null;
};

export default CrispProvider;
