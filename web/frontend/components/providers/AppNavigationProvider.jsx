import { NavMenu } from "@shopify/app-bridge-react";
import { Link } from "react-router-dom";
import { mainNavigationMenu } from "../../configs/routes";

const AppNavigationProvider = () => (
  <NavMenu>
    {mainNavigationMenu.map((route) => (
      <Link
        to={route.destination}
        key={`${route.label}-${Math.random()}`}
        rel={route?.rel ? "home" : ""}
      >
        {route.label}
      </Link>
    ))}
  </NavMenu>
);

export default AppNavigationProvider;
