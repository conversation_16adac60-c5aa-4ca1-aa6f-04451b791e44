import { arrayOf, func, object } from "prop-types";
import { useEffect, useMemo, useState } from "react";

import {
  BlockStack,
  Box,
  Button,
  Card,
  EmptyState,
  FormLayout,
  InlineStack,
  Modal,
  ResourceItem,
  ResourceList,
  Select,
  Text,
  TextField,
} from "@shopify/polaris";
import { DeleteIcon, DuplicateIcon, EditIcon, XIcon } from "@shopify/polaris-icons";

// eslint-disable-next-line import/no-relative-packages, import/no-extraneous-dependencies
import { useNavigate } from "react-router-dom";
// eslint-disable-next-line import/no-relative-packages
import { defaultActionType, defaultConditionRelation, defaultRule } from "../../defaults";
import { useAppQuery } from "../utilities/hooks";
import CustomCombobox from "./CustomCombobox";
import SelectValuesButton from "./SelectValuesButton";

/**
 * @param {{
 *   optionSet: ProductOptionSetInput,
 *   onOptionSetChange: (optionSet: ProductOptionSetInput) => void,
 *   options: IProductOption[],
 *   onTutorialOpen: (videoUrl: string) => void,
 *   shopDetails: IShopDetails,
 * }} props
 */
export default function RuleEditor({ optionSet, onOptionSetChange, options, onTutorialOpen, shopDetails }) {
  const navigate = useNavigate();
  /**
   * @type {TypedStateOptional<{
   *   isNew: boolean,
   *   index: number,
   *   rule: Exclude<ProductOptionSetInput['rules'], undefined>[number],
   * }>}
   */
  const [ruleToEdit, setRuleToEdit] = useState();

  const optionSetOptions = useMemo(
    () => (optionSet.optionIds || []).flatMap((optionId) => options.find((option) => option.id === optionId) || []),
    [optionSet.optionIds, options]
  );

  const productOptionsQueryParams = useMemo(() => {
    const params = new URLSearchParams();

    optionSet.productIds?.forEach((productId) => {
      params.append("productIds[]", productId.toString());
    });

    return params;
  }, [optionSet.productIds]);

  /** @type {UseQueryResult<ProductsOptionsGetResponse>} */
  const { data: productsOptionsData, isLoading: isShopifyProductOptionsLoading } = useAppQuery(
    `/api/products/options?${productOptionsQueryParams}`,
    {
      reactQueryOptions: {
        enabled: optionSet.productSelectionMethod === "manual" && !!optionSet.productIds?.length,
      },
    }
  );

  const shopifyProductOptions = useMemo(() => Object.values(productsOptionsData || {}).flat(), [productsOptionsData]);

  // TODO: make a utility function for settings rules, conditions and actions
  /**
   * @param {number} index
   * @param {Exclude<ProductOptionSetInput['rules'], undefined>[number] | undefined} rule
   */
  const setRule = (index, rule) => {
    const newRules = [...(optionSet.rules || [])];

    if (rule) {
      newRules[index] = { ...newRules[index], ...rule };
    } else {
      newRules.splice(index, 1);
    }

    onOptionSetChange({ ...optionSet, rules: newRules });
  };

  /**
   * @param {number} conditionIndex
   * @param {Exclude<
   *   Exclude<ProductOptionSetInput['rules'], undefined>[number]['conditions'],
   *   undefined
   * >[number] | undefined} condition
   * @param {boolean} [isResetValues]
   */
  const setCondition = (conditionIndex, condition, isResetValues = false) => {
    const newConditions = [...(ruleToEdit?.rule.conditions ?? [])];

    if (condition) {
      newConditions[conditionIndex] = { ...newConditions[conditionIndex], ...condition };

      if (isResetValues) {
        newConditions[conditionIndex].values = [];
      }

      const isSingleValue = optionSetOptions
        .find((option) => option.id === newConditions[conditionIndex].optionId)
        ?.values.some((value) => !value.title);

      if (isSingleValue) {
        newConditions[conditionIndex].values = ["__any__"];
      }
    } else {
      newConditions.splice(conditionIndex, 1);
    }

    setRuleToEdit(
      ruleToEdit && {
        ...ruleToEdit,
        rule: { ...ruleToEdit.rule, conditions: newConditions },
      }
    );
  };

  /**
   * @param {number} actionIndex
   * @param {Exclude<
   *   Exclude<ProductOptionSetInput['rules'], undefined>[number]['actions'],
   *   undefined
   * >[number] | undefined} action
   */
  const setAction = (actionIndex, action) => {
    const newConditions = [...(ruleToEdit?.rule.actions ?? [])];

    if (action) {
      newConditions[actionIndex] = { ...newConditions[actionIndex], ...action };
    } else {
      newConditions.splice(actionIndex, 1);
    }

    setRuleToEdit(
      ruleToEdit && {
        ...ruleToEdit,
        rule: { ...ruleToEdit.rule, actions: newConditions },
      }
    );
  };

  useEffect(() => {
    // Make sure that there is at least one condition and action
    if (ruleToEdit === undefined) return;

    if (ruleToEdit?.rule?.conditions?.length === 0) {
      setCondition(0, {});
    }

    if (ruleToEdit?.rule?.actions?.length === 0) {
      setAction(0, {});
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ruleToEdit]);

  return (
    <>
      <Card>
        <BlockStack gap="200">
          <InlineStack
            blockAlign="center"
            gap="400"
          >
            <Text
              variant="headingMd"
              as="h2"
            >
              Rules
            </Text>

            <Button
              onClick={() => onTutorialOpen("https://www.youtube.com/embed/qrKdbaTTpN0?si=qvaWxGyYHcnfqm73&autoplay=1")}
              variant="plain"
            >
              Watch tutorial
            </Button>
          </InlineStack>

          {optionSet.rules?.length ? (
            <>
              <ResourceList
                resourceName={{ singular: "rule", plural: "rules" }}
                items={optionSet.rules || []}
                renderItem={(rule, id, ruleIndex) => {
                  const { name, actions } = rule;
                  const subtitle =
                    actions
                      ?.map((action) => {
                        const mainActionType = action?.type
                          ? `${action.type.charAt(0).toUpperCase()}${action.type.slice(1)}`
                          : "Hide";

                        const optionTitle =
                          optionSetOptions.find((option) => option.id === action?.optionId)?.title || "";

                        return mainActionType && optionTitle ? `${mainActionType} values of ${optionTitle}` : "";
                      })
                      .filter((str) => str) // Filter out empty strings
                      .join(", ") || "No actions are set";

                  return (
                    <ResourceItem
                      id={`rule-id-${id}`}
                      key={id}
                      onClick={() => {
                        setRuleToEdit({
                          isNew: false,
                          index: ruleIndex,
                          rule:
                            /** @type {Partial<IProductOptionRule>} */
                            ({ ...rule }),
                        });
                      }}
                      accessibilityLabel={`View details for ${name}`}
                    >
                      <InlineStack
                        align="space-between"
                        blockAlign="center"
                        gap="200"
                      >
                        <BlockStack>
                          <Text
                            variant="bodyMd"
                            fontWeight="bold"
                            as="h3"
                          >
                            {name}
                          </Text>

                          <Text
                            as="span"
                            tone="subdued"
                          >
                            {subtitle}
                          </Text>
                        </BlockStack>

                        {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions */}
                        <div onClick={(event) => event.stopPropagation()}>
                          <InlineStack gap="200">
                            <Button
                              onClick={() => {
                                setRuleToEdit({
                                  isNew: false,
                                  index: ruleIndex,
                                  rule: /** @type {Partial<IProductOptionRule>} */ ({ ...rule }),
                                });
                              }}
                              icon={EditIcon}
                            />

                            <Button
                              onClick={() => {
                                const newRules = [...(optionSet.rules || [])];

                                const duplicatedRule =
                                  /** @type {Partial<IProductOptionRule>} */
                                  ({ ...newRules[ruleIndex], name: `Copy of ${name}` });

                                setRuleToEdit({
                                  isNew: true,
                                  index: optionSet.rules?.length || 0,
                                  rule: duplicatedRule,
                                });
                              }}
                              icon={DuplicateIcon}
                            />

                            <Button
                              onClick={() => setRule(ruleIndex, undefined)}
                              icon={DeleteIcon}
                            />
                          </InlineStack>
                        </div>
                      </InlineStack>
                    </ResourceItem>
                  );
                }}
              />

              <InlineStack align="end">
                <Button
                  variant="primary"
                  onClick={() => {
                    setRuleToEdit({
                      isNew: true,
                      index: optionSet.rules?.length || 0,
                      rule: { ...defaultRule, conditions: [], actions: [] },
                    });
                  }}
                >
                  Create rule
                </Button>
              </InlineStack>
            </>
          ) : (
            <Card padding="0">
              <EmptyState
                heading="Create rule"
                image=""
              >
                <p>
                  Rules allow you to hide/show options or values based on the customer’s previous choices. For example,
                  if you sell T-shirts with a custom text option and the customer selects &apos;Add custom text&apos;,
                  you can set a rule to display the text box.
                </p>

                <div style={{ marginTop: "1rem", marginBottom: "-3.5rem" }}>
                  <InlineStack
                    align="center"
                    blockAlign="center"
                    gap="200"
                  >
                    {/* TODO: Use the custom setter function (e.g., setValue) in all cases,
                        where we add a value (e.g., option values) */}
                    <Button
                      variant="primary"
                      onClick={() => {
                        setRuleToEdit({
                          isNew: true,
                          index: optionSet.rules?.length || 0,
                          rule: { ...defaultRule, conditions: [], actions: [] },
                        });
                      }}
                    >
                      Create rule
                    </Button>
                  </InlineStack>
                </div>
              </EmptyState>
            </Card>
          )}
        </BlockStack>
      </Card>

      <Modal
        sectioned
        open={ruleToEdit !== undefined}
        title={ruleToEdit?.isNew ? "Create rule" : "Edit rule"}
        onClose={() => setRuleToEdit(undefined)}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: () => setRuleToEdit(undefined),
          },
        ]}
        primaryAction={{
          disabled: !ruleToEdit?.rule.name,
          content: ruleToEdit?.isNew ? "Create rule" : "Edit rule",
          onAction: () => {
            if (ruleToEdit) {
              setRule(ruleToEdit.index, ruleToEdit.rule);
            }

            setRuleToEdit(undefined);
          },
        }}
      >
        {ruleToEdit !== undefined && optionSet.rules && (
          <FormLayout>
            <TextField
              label="Rule name"
              value={ruleToEdit.rule.name}
              onChange={(newName) => setRuleToEdit({ ...ruleToEdit, rule: { ...ruleToEdit.rule, name: newName } })}
              autoComplete="rule-name"
            />

            <InlineStack
              blockAlign="center"
              gap="200"
            >
              <Text
                fontWeight="bold"
                as="span"
              >
                IF
              </Text>

              <Select
                label=""
                options={[
                  { label: "Any", value: "any" },
                  { label: "All", value: "all" },
                ]}
                value={ruleToEdit.rule.isAllConditionsRequired ? "all" : "any"}
                onChange={(newValue) =>
                  setRuleToEdit({
                    ...ruleToEdit,
                    rule: { ...ruleToEdit.rule, isAllConditionsRequired: newValue === "all" },
                  })
                }
              />

              <Text
                as="span"
                tone="subdued"
              >
                of these conditions are true:
              </Text>
            </InlineStack>

            {ruleToEdit.rule.conditions?.map((condition, conditionIndex) => (
              // eslint-disable-next-line react/no-array-index-key
              <FormLayout.Group
                condensed
                key={conditionIndex}
              >
                <Select
                  label=""
                  options={[
                    { label: "EasyFlow options", value: "EasyFlow" },
                    { label: `Shopify options`, value: "Shopify" },
                  ]}
                  value={condition.isShopifyOption ? "Shopify" : "EasyFlow"}
                  onChange={(newValue) => setCondition(conditionIndex, { isShopifyOption: newValue === "Shopify" })}
                />

                {!shopDetails.isFeatureLimited || !condition.isShopifyOption
                  ? [
                      condition.isShopifyOption ? (
                        <CustomCombobox
                          key={0}
                          label=""
                          isAllowMultiple={false}
                          values={[
                            ...(condition.optionId && typeof condition.optionId === "string"
                              ? [{ id: condition.optionId, label: condition.optionId }]
                              : []),
                            ...shopifyProductOptions.map(({ name }) => ({ id: name, label: name })),
                            // Deduplicate values
                          ].filter(
                            (value, index, self) =>
                              index === self.findIndex((previousValue) => previousValue.id === value.id)
                          )}
                          selectedItems={condition.optionId ? [condition.optionId.toString()] : []}
                          onSelectionChange={(newSelectedValues) =>
                            setCondition(conditionIndex, {
                              optionId: newSelectedValues[newSelectedValues.length - 1],
                            })
                          }
                          isLoading={isShopifyProductOptionsLoading}
                        />
                      ) : (
                        <Select
                          key={0}
                          label=""
                          options={[
                            { label: "Select option", value: "" },
                            ...optionSetOptions.flatMap((option) => ({
                              label: option.nickname || option.title,
                              value: option.id.toString(),
                            })),
                          ]}
                          value={condition.optionId?.toString()}
                          onChange={(newOptionId) =>
                            setCondition(
                              conditionIndex,
                              { optionId: newOptionId ? parseInt(newOptionId, 10) : undefined },
                              true
                            )
                          }
                        />
                      ),

                      <Select
                        key={1}
                        label=""
                        options={
                          /**
                           * @type {{ label: string, value: IProductOptionRuleCondition['relation'] }[]}
                           */ ([
                            {
                              label:
                                condition.isShopifyOption ||
                                optionSetOptions
                                  .find((option) => option.id === condition.optionId)
                                  ?.values.some((value) => value.title)
                                  ? "is one of"
                                  : "is not empty",
                              value: "in",
                            },
                            {
                              label:
                                condition.isShopifyOption ||
                                optionSetOptions
                                  .find((option) => option.id === condition.optionId)
                                  ?.values.some((value) => value.title)
                                  ? "is not one of"
                                  : "is empty",
                              value: "not-in",
                            },
                          ])
                        }
                        value={condition.relation || defaultConditionRelation}
                        onChange={(/** @type {IProductOptionRuleCondition['relation']}} */ newRelation) =>
                          setCondition(conditionIndex, { relation: newRelation })
                        }
                      />,
                    ]
                  : [
                      <Box
                        paddingBlockStart="100"
                        width="15rem"
                      >
                        <Button
                          variant="plain"
                          onClick={() => navigate("/pricing")}
                        >
                          Upgrade plan to use Shopify variants
                        </Button>
                      </Box>,
                    ]}

                <InlineStack
                  align="space-between"
                  blockAlign="start"
                  gap="200"
                  wrap={false}
                >
                  {(!shopDetails.isFeatureLimited || !condition.isShopifyOption) &&
                    (condition.isShopifyOption ? (
                      <CustomCombobox
                        label=""
                        values={[
                          ...(condition.values ? condition.values.map((value) => ({ id: value, label: value })) : []),
                          ...(shopifyProductOptions
                            .find((option) => option.name === condition.optionId)
                            ?.values.map((value) => ({ id: value, label: value })) || []),
                          // Deduplicate values
                        ].filter(
                          (value, index, self) =>
                            index === self.findIndex((previousValue) => previousValue.id === value.id)
                        )}
                        selectedItems={condition.values || []}
                        onSelectionChange={(newSelectedValues) =>
                          setCondition(conditionIndex, { values: newSelectedValues })
                        }
                        isLoading={isShopifyProductOptionsLoading}
                      />
                    ) : (
                      <SelectValuesButton
                        valueType="value"
                        values={
                          options
                            .find((option) => option.id === condition.optionId)
                            ?.values.flatMap((value) => (value.title ? { id: value.title, label: value.title } : [])) ||
                          []
                        }
                        selectedItems={condition.values || []}
                        onSelectionChange={(newSelections) => setCondition(conditionIndex, { values: newSelections })}
                      />
                    ))}

                  {(ruleToEdit.rule.conditions?.length || 0) > 1 && (
                    <Box paddingBlockStart="200">
                      <Button
                        variant="plain"
                        icon={XIcon}
                        onClick={() => setCondition(conditionIndex, undefined)}
                      />
                    </Box>
                  )}
                </InlineStack>
              </FormLayout.Group>
            ))}

            <Button
              variant="plain"
              onClick={() => setCondition(ruleToEdit.rule.conditions?.length || 0, {})}
            >
              + Add condition
            </Button>

            <InlineStack
              blockAlign="center"
              gap="200"
            >
              <Text
                fontWeight="bold"
                as="span"
              >
                THEN
              </Text>

              <Text
                as="span"
                tone="subdued"
              >
                all of these will happen:
              </Text>
            </InlineStack>

            {ruleToEdit.rule.actions?.map((action, actionIndex) => {
              const actionOptionValues =
                options.find((option) => option.id === action.optionId)?.values.flatMap((value) => value.title || []) ||
                [];

              return (
                // eslint-disable-next-line react/no-array-index-key
                <FormLayout.Group
                  condensed
                  key={actionIndex}
                >
                  <Select
                    label=""
                    options={
                      /** @type {{ label: string, value: IProductOptionRuleAction['type'] }[]} */ ([
                        { label: "Hide option or values", value: "hide" },
                        { label: "Show option or values", value: "show" },
                      ])
                    }
                    value={action.type || defaultActionType}
                    onChange={(/** @type {IProductOptionRuleAction['type']}} */ newActionType) =>
                      setAction(actionIndex, { type: newActionType })
                    }
                  />
                  <Select
                    label=""
                    options={[
                      { label: "Select option", value: "" },
                      ...optionSetOptions.map((option) => ({
                        label: option.nickname || option.title,
                        value: option.id.toString(),
                      })),
                    ]}
                    value={action.optionId?.toString()}
                    onChange={(newOptionId) =>
                      setAction(actionIndex, {
                        optionId: parseInt(newOptionId, 10),
                      })
                    }
                  />

                  <InlineStack
                    blockAlign="center"
                    gap="100"
                    wrap={false}
                  >
                    <div style={{ width: "10rem" }}>
                      <SelectValuesButton
                        valueType="value"
                        values={actionOptionValues.map((value) => ({ id: value, label: value }))}
                        selectedItems={action.values === "All" ? actionOptionValues : action.values || []}
                        onSelectionChange={(newSelections) =>
                          setAction(actionIndex, {
                            values: actionOptionValues.every((value) => newSelections.includes(value))
                              ? "All"
                              : newSelections,
                          })
                        }
                      />
                    </div>

                    {(ruleToEdit.rule.actions?.length || 0) > 1 && (
                      <Button
                        variant="plain"
                        icon={XIcon}
                        onClick={() => setAction(actionIndex, undefined)}
                      />
                    )}
                  </InlineStack>
                </FormLayout.Group>
              );
            })}

            <Button
              variant="plain"
              onClick={() => setAction(ruleToEdit.rule.actions?.length || 0, {})}
            >
              + Add action
            </Button>
          </FormLayout>
        )}
      </Modal>
    </>
  );
}

RuleEditor.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  optionSet: object.isRequired,
  onOptionSetChange: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: arrayOf(object).isRequired,
  onTutorialOpen: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  shopDetails: object.isRequired,
};
