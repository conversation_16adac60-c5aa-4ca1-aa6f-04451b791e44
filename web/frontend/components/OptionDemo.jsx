import { array } from 'prop-types';
import React from 'react';

import { Button, InlineStack, Text } from '@shopify/polaris';

/** @type {Record<ProductOptionType, String> } */
export const productOptionImages = {
  Checkbox: 'checkbox',
  Dropdown: 'dropdown',
  'Image swatch': 'image-swatch',
  'Radio button': 'radio',
  Button: 'button',
  'Text box': 'text',
  'Multi-line text box': 'text-area',
  'Number field': 'number',
  'Date picker': 'date',
  'Color swatch': 'color-swatch',
};

/**
 * @param {{
 *    options: (Partial<IProductOption> | ProductOptionInput)[],
 * }} props
 */

export default function OptionDemo({ options }) {
  return (
    <InlineStack>
      <img src="assets/product-skeleton.png" alt="empty" width={150} />

      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <div>
          <Text variant="headingLg" as="h2">
            Product Name
          </Text>
        </div>

        {options.map((option) => (
          // eslint-disable-next-line react/no-array-index-key
          <React.Fragment key={option.title}>
            {' '}
            {/* Use a unique identifier from 'option' or 'index' */}
            <div>
              <Text variant="headingSm" as="h6">
                {option.title?.length ? option.title : 'Option Title'}
                {}
              </Text>
            </div>
            {option.type && (
              <img src={`assets/${productOptionImages[option.type]}.png`} alt="empty" width={250} />
            )}
          </React.Fragment>
        ))}

        <div>
          <Button disabled>Add to Cart</Button>
        </div>
      </div>
    </InlineStack>
  );
}

OptionDemo.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  options: array.isRequired,
};
