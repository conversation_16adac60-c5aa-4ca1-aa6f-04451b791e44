import { arrayOf, bool, func, node, object, string as stringProp } from "prop-types";

import {
  Bleed,
  BlockStack,
  Box,
  Button,
  Card,
  Divider,
  FormLayout,
  InlineStack,
  Layout,
  Select,
  SkeletonBodyText,
  SkeletonDisplayText,
  Text,
  TextField,
} from "@shopify/polaris";

import ColorPicker from "./ColorPicker";

/**
 * @typedef {{
 *   groupTitle?: string,
 *   groupSelector: string,
 *   group: readonly {
 *     label: string,
 *     propertyName: string,
 *     type: "color" | "size-px" | "size-percent" | "font-weight" | "font-style",
 *     defaultValue?: string,
 *     propertyToValue?: (property: string | undefined) => string | undefined,
 *     valueToProperty?: (newValue: string, oldProperty: string | undefined) => string | undefined,
 *   }[],
 *   isInline?: boolean,
 * }} Option
 */

/**
 * @param {{
 *   title: string,
 *   description: React.ReactNode,
 *   isLoading: boolean,
 *   values: StyleOptions['cssCustomizations'],
 *   onValuesChange: (values: StyleOptions['cssCustomizations']) => void,
 *   options: Option[],
 * }} props
 */
export default function StylingSection({ title, description, isLoading, values, onValuesChange, options }) {
  return (
    <Layout.AnnotatedSection
      title={!isLoading ? title : <SkeletonDisplayText />}
      description={!isLoading ? description : <SkeletonBodyText lines={2} />}
    >
      <Card>
        <Bleed
          marginBlockStart="400"
          marginInline="400"
        >
          <Box
            padding="200"
            paddingInlineEnd="600"
          >
            <InlineStack align="end">
              <Button
                variant="plain"
                onClick={() => {
                  const newValues = { ...values };

                  options.forEach(({ groupSelector: currentGroupSelector, group: currentGroup }) => {
                    currentGroup.forEach(({ propertyName, defaultValue }) => {
                      if (defaultValue && newValues[currentGroupSelector]) {
                        newValues[currentGroupSelector][propertyName] = defaultValue;
                      } else {
                        delete newValues[currentGroupSelector]?.[propertyName];
                      }
                    });

                    if (newValues[currentGroupSelector] && !Object.keys(newValues[currentGroupSelector]).length) {
                      delete newValues[currentGroupSelector];
                    }
                  });

                  onValuesChange(newValues);
                }}
              >
                Reset to default
              </Button>
            </InlineStack>
          </Box>
        </Bleed>
        <FormLayout>
          {!isLoading
            ? options
                .reduce(
                  (groups, currentGroup) =>
                    currentGroup.isInline
                      ? [...groups.slice(0, groups.length - 1), [...groups[groups.length - 1], currentGroup]]
                      : [...groups, [currentGroup]],
                  /** @type {Option[][]} */ ([])
                )
                .map((inlineGroups, groupIndex) => {
                  const { groupTitle } = inlineGroups[0];
                  return (
                    <BlockStack
                      key={`${groupTitle || "Default"}-${Math.random()}`}
                      gap="200"
                    >
                      {groupIndex !== 0 && groupTitle && (
                        <Bleed marginInline="400">
                          <Divider />
                        </Bleed>
                      )}
                      <Box>
                        <Box paddingBlockEnd="100">
                          <InlineStack align="space-between">
                            {groupTitle ? (
                              <Text
                                as="h2"
                                variant="headingSm"
                              >
                                {groupTitle}
                              </Text>
                            ) : (
                              <div />
                            )}
                          </InlineStack>
                        </Box>
                        <FormLayout.Group condensed>
                          {inlineGroups.flatMap(({ groupSelector, group }) =>
                            group.map(
                              ({ label, type, propertyName, defaultValue, propertyToValue, valueToProperty }) => {
                                const value = values[groupSelector]?.[propertyName];
                                const property = value || defaultValue;

                                const changeHandler = (/** @type {string} */ newValue) => {
                                  /** @type {string | undefined} */
                                  let propertyValue = newValue;

                                  if (valueToProperty) {
                                    propertyValue = valueToProperty(propertyValue, property);
                                  }

                                  if (propertyValue) {
                                    onValuesChange({
                                      ...values,
                                      [groupSelector]: {
                                        ...values[groupSelector],
                                        [propertyName]: propertyValue,
                                      },
                                    });
                                  }
                                };

                                const inputValue = propertyToValue ? propertyToValue(property) : property;

                                let input;

                                switch (type) {
                                  case "color":
                                    input = (
                                      <ColorPicker
                                        key={label}
                                        label={label}
                                        color={inputValue || "#fffffe"}
                                        onColorChange={changeHandler}
                                      />
                                    );
                                    break;
                                  case "size-px":
                                  case "size-percent": {
                                    input = (
                                      <TextField
                                        type="number"
                                        key={label}
                                        label={label}
                                        suffix={type === "size-px" ? "px" : "%"}
                                        min={type === "size-percent" ? 0 : undefined}
                                        max={type === "size-percent" ? 100 : undefined}
                                        value={inputValue && parseFloat(inputValue || "0").toString()}
                                        onChange={(newValue) => {
                                          const propertyValue = newValue + (type === "size-px" ? "px" : "%");

                                          changeHandler(propertyValue);
                                        }}
                                        autoComplete={`${label}-input`}
                                      />
                                    );
                                    break;
                                  }
                                  case "font-weight":
                                  case "font-style": {
                                    /** @type {import('@shopify/polaris').SelectOption[]} */
                                    const selectOptions =
                                      type === "font-style"
                                        ? [
                                            { label: "Normal", value: "normal" },
                                            { label: "Italic", value: "italic" },
                                          ]
                                        : [
                                            { label: "Extra light", value: "100" },
                                            { label: "Light", value: "300" },
                                            { label: "Normal", value: "400" },
                                            { label: "Medium", value: "500" },
                                            { label: "Semi Bold", value: "600" },
                                            { label: "Bold", value: "700" },
                                            { label: "Extra Bold", value: "800" },
                                          ];

                                    input = (
                                      <Select
                                        key={label}
                                        label={label}
                                        options={selectOptions}
                                        value={inputValue}
                                        onChange={changeHandler}
                                      />
                                    );
                                    break;
                                  }
                                  default:
                                    break;
                                }

                                return input;
                              }
                            )
                          )}
                        </FormLayout.Group>
                      </Box>
                    </BlockStack>
                  );
                })
            : [
                <SkeletonDisplayText key={0} />,
                <SkeletonBodyText
                  lines={2}
                  key={1}
                />,
                <SkeletonDisplayText key={2} />,
                <SkeletonBodyText
                  lines={2}
                  key={3}
                />,
                <SkeletonDisplayText key={4} />,
                <SkeletonBodyText
                  lines={2}
                  key={5}
                />,
                <SkeletonDisplayText key={6} />,
                <SkeletonBodyText
                  lines={2}
                  key={7}
                />,
              ]}
        </FormLayout>
      </Card>
    </Layout.AnnotatedSection>
  );
}

StylingSection.propTypes = {
  title: stringProp.isRequired,
  description: node.isRequired,
  isLoading: bool.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  values: object.isRequired,
  onValuesChange: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: arrayOf(object).isRequired,
};
