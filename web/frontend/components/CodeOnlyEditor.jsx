// @ts-nocheck
/* eslint-disable import/no-extraneous-dependencies */
import { html } from "@codemirror/lang-html";
import CodeMirror from "@uiw/react-codemirror";
import { func, string } from "prop-types";
import { useEffect, useState } from "react";

import { Box, Text } from "@shopify/polaris";

/**
 * @param {{
 *   htmlContent: string | undefined,
 *   onHtmlContentChange: (htmlContent: string) => void,
 *   label?: string,
 * }} props
 */
export default function CodeOnlyEditor({ htmlContent = "", onHtmlContentChange, label }) {
  const [editorContent, setEditorContent] = useState(htmlContent);

  useEffect(() => {
    // Sanitize the initial HTML content to remove any script tags
    const sanitizedContent = removeScriptTags(htmlContent);
    setEditorContent(sanitizedContent);

    // If the sanitized content is different from the original, notify the parent
    if (sanitizedContent !== htmlContent) {
      onHtmlContentChange(sanitizedContent);
    }
  }, [htmlContent, onHtmlContentChange]);

  /**
   * Removes script tags from HTML content
   * @param {string} content - The HTML content to sanitize
   * @returns {string} - The sanitized HTML content
   */
  const removeScriptTags = (content) => {
    // Remove <script>...</script> tags
    let sanitized = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");

    // Remove inline script attributes (e.g., onclick, onerror, etc.)
    sanitized = sanitized.replace(/\s+on\w+\s*=\s*("[^"]*"|'[^']*'|[^>\s]*)/gi, "");

    // Remove javascript: URLs
    sanitized = sanitized.replace(/javascript:[^\s"']+/gi, "");

    return sanitized;
  };

  const handleChange = (content) => {
    setEditorContent(content);

    // Sanitize the content by removing script tags before passing it to the parent
    const sanitizedContent = removeScriptTags(content);

    // Make sure we're passing the sanitized HTML content without any script tags
    onHtmlContentChange(sanitizedContent);
  };

  return (
    <>
      <style>
        {`
         /* Always show text cursor inside CodeMirror */
          .html-editor-wrapper .cm-editor,
          .html-editor-wrapper .cm-editor * {
            cursor: text !important;
          }
      `}
      </style>
      <div>
        {label && (
          <Box paddingBlockEnd="100">
            <Text as="p">{label}</Text>
          </Box>
        )}
        <div className="html-editor-wrapper">
          <CodeMirror
            value={editorContent}
            height="300px"
            extensions={[html()]}
            basicSetup={{
              foldGutter: false,
              dropCursor: false,
              allowMultipleSelections: false,
              indentOnInput: false,
              lineNumbers: true,
              highlightActiveLine: true,
              highlightSelectionMatches: true,
            }}
            onChange={handleChange}
            theme="light"
          />
        </div>
      </div>
    </>
  );
}

CodeOnlyEditor.propTypes = {
  htmlContent: string,
  onHtmlContentChange: func.isRequired,
  label: string,
};
