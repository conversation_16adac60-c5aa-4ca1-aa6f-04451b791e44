import { Crisp } from "crisp-sdk-web";
import { bool, func, string } from "prop-types";

import { BlockStack, Box, Button, InlineStack, Text } from "@shopify/polaris";

import configs from "../configs";
import { useTrackEvent } from "../utilities/hooks";

// const extensionUUID = "83d04f43-e63e-4a8d-82b9-e4f0b41ef22f";
// const testExtensionUUID = 'e266466e-457c-40e3-b808-b597d88681cc';

// export const appBlockDeepLinkPath = `/themes/current/editor?template=product&addAppBlockId=${extensionUUID}/app-block&target=mainSection`;

/**
 * @param {{
 *   shop: string,
 *   productHandle: string | undefined,
 *   isExtensionEnableClicked: boolean,
 *   onExtensionEnableClick: () => void,
 *   onWatchTutorialClick: () => void,
 * }} props
 */
export default function EnableExtension({
  shop,
  productHandle,
  isExtensionEnableClicked,
  onExtensionEnableClick,
  onWatchTutorialClick,
}) {
  const trackEvent = useTrackEvent();

  return (
    <div style={{ display: "flex", flexWrap: "wrap", gap: "2rem" }}>
      <div style={{ minWidth: "22rem", flex: "1 1 0" }}>
        <BlockStack gap="400">
          <Text
            variant="headingSm"
            as="h3"
          >
            1. Add App Block to your Default Product Page
          </Text>

          <p>
            Open the Default Product Page in your Theme Editor and add the App Block where you want to show the options
            on the product page.
          </p>

          <p>The App Block supports all Shopify 2.0 themes.</p>

          <InlineStack
            blockAlign="center"
            gap="400"
          >
            <Button
              variant="primary"
              onClick={async () => {
                trackEvent("click add app block");
                window.open(`shopify://admin${configs.appBlockDeepLinkPath}`, "_blank");
                onExtensionEnableClick();
              }}
            >
              Add App Block to Product Page
            </Button>

            <Button
              variant="plain"
              onClick={() => onWatchTutorialClick()}
            >
              Watch tutorial
            </Button>
          </InlineStack>

          <Text
            variant="headingSm"
            as="h3"
          >
            2. View Option Set on your store
          </Text>

          <p>Open the product page of a product where you added the option set to check if its visible.</p>
        </BlockStack>

        <br />

        <Button
          disabled={!isExtensionEnableClicked || !productHandle}
          onClick={async () => {
            trackEvent("click open store");
            window.open(`https://${shop}/products/${productHandle}`, "_blank");
          }}
        >
          View options on store
        </Button>

        <Box paddingBlockStart="300">
          <Button
            variant="plain"
            onClick={() => {
              Crisp.message.send(
                "text",
                `The options I created are not visible on the product page. My Shopify link: ${shop}`
              );

              Crisp.chat.open();
            }}
          >
            Can&apos;t see options on product page?
          </Button>
        </Box>
      </div>

      <img
        src="/assets/enable-extension.png"
        alt="Enable extension"
        style={{ width: "30rem" }}
      />
    </div>
  );
}

EnableExtension.propTypes = {
  shop: string.isRequired,
  productHandle: string,
  isExtensionEnableClicked: bool.isRequired,
  onExtensionEnableClick: func.isRequired,
  onWatchTutorialClick: func.isRequired,
};
