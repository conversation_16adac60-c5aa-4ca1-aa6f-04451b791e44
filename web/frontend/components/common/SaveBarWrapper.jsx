/* eslint-disable react/prop-types */
import { SaveBar } from "@shopify/app-bridge-react";

export const saveBarId = "easyflow-save-bar";

/**
 * @param {{
 *   saveActionCallback: () => void,
 *   discardActionCallback: () => void,
 *   loading: boolean,
 * }} props
 * @returns
 */
const SaveBarWrapper = ({ saveActionCallback, discardActionCallback, loading = false }) => (
  <SaveBar id={saveBarId}>
    <button
      type="button"
      variant="primary"
      loading={loading ? "true" : undefined}
      onClick={saveActionCallback}
    >
      Save
    </button>
    <button
      type="button"
      onClick={discardActionCallback}
    >
      Discard
    </button>
  </SaveBar>
);

export default SaveBarWrapper;
