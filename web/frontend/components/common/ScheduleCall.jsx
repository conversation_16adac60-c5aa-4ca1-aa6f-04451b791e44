/* eslint-disable react/prop-types */
import { MediaCard, Text } from '@shopify/polaris';
import { PhoneIcon, RedoIcon, XSmallIcon } from '@shopify/polaris-icons';
import { useEffect, useState } from 'react';

const now = new Date();

/**
 * @param {{
 *   scheduleCallActionCallback?: () => void,
 *   title: string,
 *   description: any,
 *   pageType: string,
 *   isPortrait?: boolean,
 *   showActions?: boolean
 * }} props
 */
function ScheduleCall({
  title = 'Get help from our experts to create your options and option sets',
  description = (
    <>
      Need assistance? Our experts can quickly set up your store’s options and option sets. Schedule
      a meeting now to consult an expert for{' '}
      <Text as="span" variant="bodySm" fontWeight="semibold">
        FREE
      </Text>
      .
    </>
  ),
  scheduleCallActionCallback,
  pageType = '',
  isPortrait = false,
  showActions = true,
}) {
  const scheduleCallId = `schedule_call_${pageType}_options`;

  const [currentOptions, setCurrentOptions] = useState({
    show: true,
    later: 'lifetime',
  });

  useEffect(() => {
    const options = localStorage.getItem(scheduleCallId);
    if (options) {
      const newOp = JSON.parse(options);
      if (!newOp.show) {
        if (newOp.later !== 'lifetime') {
          const isDateToday = new Date(newOp.later).getDate() === now.getDate();
          if (isDateToday) {
            localStorage.setItem(
              scheduleCallId,
              JSON.stringify({
                ...newOp,
                show: true,
              }),
            );
          } else {
            setCurrentOptions({
              show: false,
              later: newOp.later,
            });
          }
        } else {
          setCurrentOptions(newOp);
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scheduleCallId]);

  const scheduleCallAction = async (/** @type {string} */ actionType) => {
    let newOptions = currentOptions;
    if (actionType === 'dismiss') {
      newOptions = { show: false, later: 'lifetime' };
    } else {
      // @ts-ignore
      newOptions = {
        show: false,
        later: `${now.getFullYear()}-${now.getMonth()}-${now.getDate() + 7}`,
      };
    }
    setCurrentOptions(newOptions);
    localStorage.setItem(scheduleCallId, JSON.stringify(newOptions));
    if (scheduleCallActionCallback) scheduleCallActionCallback();
  };
  return (
    currentOptions &&
    currentOptions.show && (
      <MediaCard
        portrait={isPortrait}
        title={title}
        primaryAction={{
          icon: PhoneIcon,
          content: 'Schedule a call',
          onAction: () => window.open('https://storeware.io/easyflow/talk-with-expert', '_blank'),
        }}
        popoverActions={
          showActions
            ? [
                {
                  content: 'Remind me later',
                  icon: RedoIcon,
                  onAction: () => scheduleCallAction('later'),
                },
                {
                  content: 'Dismiss',
                  icon: XSmallIcon,
                  onAction: () => scheduleCallAction('dismiss'),
                  destructive: true,
                },
              ]
            : []
        }
        size="small"
        description={description}
      >
        <img
          alt=""
          width="100%"
          height="100%"
          style={{
            objectFit: 'cover',
            objectPosition: 'center',
          }}
          src="https://storage.googleapis.com/szamlazz-hu-app.appspot.com/assets/schedule_call.png"
        />
      </MediaCard>
    )
  );
}

export default ScheduleCall;
