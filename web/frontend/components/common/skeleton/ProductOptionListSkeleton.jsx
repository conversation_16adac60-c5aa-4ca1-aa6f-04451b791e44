import { Bleed, BlockStack, <PERSON>, <PERSON>, <PERSON><PERSON>r, InlineStack } from "@shopify/polaris";
import React from "react";
import SkeletonLoader from "./Skeleton";

const ProductOptionListSkeleton = () => (
  <Box>
    <Card>
      <Box paddingBlockEnd="400">
        <BlockStack gap="200">
          <InlineStack
            gap="200"
            align="space-between"
          >
            <InlineStack gap="200">
              <SkeletonLoader style={{ height: 15, width: 40 }} />
              <SkeletonLoader style={{ height: 15, width: 60 }} />
              <SkeletonLoader style={{ height: 15, width: 50 }} />
            </InlineStack>
            <InlineStack gap="200">
              <SkeletonLoader style={{ height: 15, width: 60 }} />
              <SkeletonLoader style={{ height: 15, width: 50 }} />
            </InlineStack>
          </InlineStack>
          <Bleed marginInline="400">
            <Divider />
          </Bleed>
          <BlockStack gap="200">
            {[...Array(10)].map((_, _index) => (
              <>
                <InlineStack
                  gap="200"
                  align="space-between"
                >
                  <SkeletonLoader style={{ height: 15, width: 40 }} />
                  <SkeletonLoader style={{ height: 15, width: 60 }} />
                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                </InlineStack>
                {_index !== 1 && (
                  <Bleed marginInline="400">
                    <Divider />
                  </Bleed>
                )}
              </>
            ))}
          </BlockStack>
        </BlockStack>
      </Box>
    </Card>
  </Box>
);

export default ProductOptionListSkeleton;
