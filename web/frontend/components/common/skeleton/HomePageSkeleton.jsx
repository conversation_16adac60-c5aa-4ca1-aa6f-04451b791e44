/* eslint-disable react/no-array-index-key */
import { Bleed, BlockStack, Box, Card, Divider, InlineGrid, InlineStack } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const HomePageSkeleton = () => (
  <BlockStack gap="400">
    <Box paddingBlockEnd="400">
      <BlockStack gap="400">
        <Box>
          <Card>
            <Box paddingBlockEnd="400">
              <BlockStack gap="200">
                <BlockStack>
                  <SkeletonLoader style={{ height: 15, width: 400 }} />
                </BlockStack>
                <Bleed marginInline="400">
                  <Divider />
                </Bleed>
                <SkeletonLoader style={{ height: 15, width: 200 }} />
                <SkeletonLoader style={{ height: 15, width: 400 }} />
                <InlineStack gap="200">
                  <SkeletonLoader style={{ height: 20, width: 50 }} />
                  <SkeletonLoader style={{ height: 20, width: 40 }} />
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Box>
        <Box>
          <Card>
            <Box paddingBlockEnd="400">
              <BlockStack gap="200">
                <InlineStack
                  gap="200"
                  align="space-between"
                >
                  <InlineStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 40 }} />
                    <SkeletonLoader style={{ height: 15, width: 60 }} />
                    <SkeletonLoader style={{ height: 15, width: 50 }} />
                  </InlineStack>
                  <InlineStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 60 }} />
                    <SkeletonLoader style={{ height: 15, width: 50 }} />
                  </InlineStack>
                </InlineStack>
                <Bleed marginInline="400">
                  <Divider />
                </Bleed>
                <BlockStack gap="200">
                  {[...Array(10)].map((_, _index) => (
                    <>
                      <InlineStack
                        gap="200"
                        align="space-between"
                      >
                        <SkeletonLoader style={{ height: 15, width: 40 }} />
                        <SkeletonLoader style={{ height: 15, width: 60 }} />
                        <SkeletonLoader style={{ height: 15, width: 50 }} />
                        <SkeletonLoader style={{ height: 15, width: 50 }} />
                        <SkeletonLoader style={{ height: 15, width: 50 }} />
                        <SkeletonLoader style={{ height: 15, width: 50 }} />
                        <SkeletonLoader style={{ height: 15, width: 50 }} />
                      </InlineStack>
                      {_index !== 9 && (
                        <Bleed marginInline="400">
                          <Divider />
                        </Bleed>
                      )}
                    </>
                  ))}
                </BlockStack>
              </BlockStack>
            </Box>
          </Card>
        </Box>
        <Box>
          <BlockStack
            gap="200"
            align="space-between"
          >
            <SkeletonLoader style={{ height: 20, width: 100 }} />
            <InlineGrid
              gap="200"
              columns={3}
            >
              {[...Array(2)].map((_, _index) => (
                <Card key={_index}>
                  <Box
                    minHeight="150px"
                    minWidth="200px"
                  >
                    <BlockStack gap="400">
                      <Box
                        minHeight="180px"
                        background="bg-fill-secondary"
                      />
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 150 }} />
                        <SkeletonLoader style={{ height: 15, width: 250 }} />
                        <SkeletonLoader style={{ height: 15, width: 150 }} />
                      </BlockStack>
                    </BlockStack>
                  </Box>
                </Card>
              ))}

              <Box paddingBlockEnd="400">
                <BlockStack
                  gap="400"
                  align="space-between"
                >
                  <Card>
                    <Box minHeight="90px">
                      <BlockStack gap="400">
                        <Box background="bg-fill-secondary" />
                        <BlockStack gap="200">
                          <SkeletonLoader style={{ height: 15, width: 150 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                        </BlockStack>
                      </BlockStack>
                    </Box>
                  </Card>
                  <Card>
                    <Box minHeight="90px">
                      <BlockStack gap="400">
                        <Box background="bg-fill-secondary" />
                        <BlockStack gap="200">
                          <SkeletonLoader style={{ height: 15, width: 150 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                          <SkeletonLoader style={{ height: 15, width: 250 }} />
                        </BlockStack>
                      </BlockStack>
                    </Box>
                  </Card>
                </BlockStack>
              </Box>
            </InlineGrid>
          </BlockStack>
        </Box>
        <Box>
          <Card>
            <Box minHeight="180px">
              <InlineStack gap="400">
                <Card background="bg-fill-secondary">
                  <Box
                    minHeight="180px"
                    minWidth="200px"
                  >
                    &nbsp;
                  </Box>
                </Card>
                <Box paddingBlockStart="400">
                  <BlockStack gap="400">
                    <SkeletonLoader style={{ height: 15, width: 200 }} />
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 400 }} />
                      <SkeletonLoader style={{ height: 15, width: 500 }} />
                      <SkeletonLoader style={{ height: 15, width: 400 }} />
                    </BlockStack>

                    <InlineStack gap="200">
                      <SkeletonLoader style={{ height: 20, width: 50 }} />
                      <SkeletonLoader style={{ height: 20, width: 40 }} />
                    </InlineStack>
                  </BlockStack>
                </Box>
              </InlineStack>
            </Box>
          </Card>
        </Box>
        <Box>
          <BlockStack
            gap="200"
            align="space-between"
          >
            <SkeletonLoader style={{ height: 20, width: 100 }} />
            <InlineGrid
              gap="200"
              columns={3}
            >
              {[...Array(4)].map((_, _index) => (
                <Card key={_index}>
                  <Box
                    minHeight="100px"
                    minWidth="180px"
                  >
                    <BlockStack gap="400">
                      <Box
                        minHeight="100px"
                        background="bg-fill-secondary"
                      />
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 150 }} />
                        <SkeletonLoader style={{ height: 15, width: 250 }} />
                        <SkeletonLoader style={{ height: 15, width: 150 }} />
                      </BlockStack>
                    </BlockStack>
                  </Box>
                </Card>
              ))}
            </InlineGrid>
          </BlockStack>
        </Box>
      </BlockStack>
    </Box>
  </BlockStack>
);

export default HomePageSkeleton;
