import { BlockStack, Box, Card, Text } from "@shopify/polaris";
import PreviewOptionSkeleton from "../../options-templates/preview/PreviewOptionSkeleton";
import SkeletonLoader from "./Skeleton";

const ProductOptionPreviewCardSkeleton = () => (
  <Card>
    <BlockStack gap="400">
      <Text
        as="h2"
        variant="headingMd"
      >
        Preview
      </Text>
      <PreviewOptionSkeleton />
      <Box>
        <BlockStack gap="400">
          <SkeletonLoader style={{ height: 20, width: 150 }} />
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 230 }} />
          </BlockStack>
        </BlockStack>
      </Box>
    </BlockStack>
  </Card>
);

export default ProductOptionPreviewCardSkeleton;
