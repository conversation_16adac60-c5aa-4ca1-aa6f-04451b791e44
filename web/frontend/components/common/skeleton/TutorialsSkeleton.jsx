/* eslint-disable react/no-array-index-key */
import { BlockStack, Box, Card, InlineGrid } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const TutorialsSkeleton = () => (
  <Box>
    <BlockStack
      gap="200"
      align="space-between"
    >
      <SkeletonLoader style={{ height: 20, width: 100 }} />
      <InlineGrid
        gap="200"
        columns={3}
      >
        {[...Array(2)].map((_, _index) => (
          <Card key={_index}>
            <Box
              minHeight="150px"
              minWidth="200px"
            >
              <BlockStack gap="400">
                <Box
                  minHeight="180px"
                  background="bg-fill-secondary"
                />
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 15, width: 150 }} />
                  <SkeletonLoader style={{ height: 15, width: 250 }} />
                  <SkeletonLoader style={{ height: 15, width: 150 }} />
                </BlockStack>
              </BlockStack>
            </Box>
          </Card>
        ))}

        <Box paddingBlockEnd="400">
          <BlockStack
            gap="400"
            align="space-between"
          >
            <Card>
              <Box minHeight="90px">
                <BlockStack gap="400">
                  <Box background="bg-fill-secondary" />
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 150 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                  </BlockStack>
                </BlockStack>
              </Box>
            </Card>
            <Card>
              <Box minHeight="90px">
                <BlockStack gap="400">
                  <Box background="bg-fill-secondary" />
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 150 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                    <SkeletonLoader style={{ height: 15, width: 250 }} />
                  </BlockStack>
                </BlockStack>
              </Box>
            </Card>
          </BlockStack>
        </Box>
      </InlineGrid>
    </BlockStack>
  </Box>
);

export default TutorialsSkeleton;
