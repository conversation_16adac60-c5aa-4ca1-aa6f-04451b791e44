import { BlockSta<PERSON>, Box, ButtonGroup, Card, InlineStack, Page, ProgressBar, Text } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const OnboardPageSkeleton = () => (
  <div className="onboard-page-container">
    <Page title="Product Options">
      <Card>
        <BlockStack gap="200">
          <Text
            as="h2"
            variant="headingSm"
          >
            Get started with EasyFlow
          </Text>

          <InlineStack gap="400">
            <Text
              variant="bodyLg"
              as="p"
            >
              1/3 Steps: Learn the Basics
            </Text>

            <div style={{ width: 298, marginTop: 5 }}>
              <ProgressBar
                progress={(1 / 3) * 100}
                tone="success"
              />
            </div>
          </InlineStack>

          <InlineStack align="end">
            <ButtonGroup>
              <SkeletonLoader style={{ width: 50, height: 25 }} />
              <SkeletonLoader style={{ width: 50, height: 25 }} />
            </ButtonGroup>
          </InlineStack>
        </BlockStack>
      </Card>
      <Box paddingBlockStart="400">
        <SkeletonLoader style={{ width: 950, height: 530 }} />
      </Box>
    </Page>
  </div>
);

export default OnboardPageSkeleton;
