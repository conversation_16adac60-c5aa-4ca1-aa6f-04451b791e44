import { BlockStack, Box, Card, InlineStack, Layout, SkeletonBodyText } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const SettingsPageSkeleton = () => (
  <Layout>
    <Layout.AnnotatedSection
      title="Customize how price add-ons work"
      description="Make sure to enable the EasyFlow App Embed for these settings to work properly. If the app embed is enabled the add-on product will be removed from the cart if the base product is removed."
    >
      <Card>
        <BlockStack gap="200">
          <Box paddingBlockEnd="400">
            <SkeletonLoader style={{ height: 25, width: 250 }} />
          </Box>

          {["", "", "", ""].map((c, index) => (
            <InlineStack
              gap="200"
              // eslint-disable-next-line react/no-array-index-key
              key={`card_${index}`}
            >
              <SkeletonLoader style={{ height: 15, width: 15 }} />
              <SkeletonLoader style={{ height: 15, width: 400 }} />
            </InlineStack>
          ))}

          <Box paddingBlockStart="400">
            <InlineStack gap="200">
              <SkeletonLoader style={{ height: 10, width: 100 }} />

              <SkeletonLoader style={{ height: 10, width: 100 }} />
            </InlineStack>
          </Box>
        </BlockStack>
      </Card>
    </Layout.AnnotatedSection>

    <Layout.AnnotatedSection title="Customize how add-on prices are displayed">
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="200">
            {["", ""].map((c, index) => (
              <InlineStack
                gap="200"
                // eslint-disable-next-line react/no-array-index-key
                key={`card_${index}`}
              >
                <SkeletonLoader style={{ height: 15, width: 15 }} />
                <SkeletonLoader style={{ height: 15, width: 400 }} />
              </InlineStack>
            ))}
          </BlockStack>
          <SkeletonLoader style={{ height: 10, width: 250 }} />
        </BlockStack>
      </Card>
    </Layout.AnnotatedSection>

    <Layout.AnnotatedSection
      title="Customize system text"
      description="Enable the EasyFlow App Embed if you want the add-on product to be removed from the cart if the base product is removed."
    >
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="200">
            {["", "", "", ""].map((c, index) => (
              <InlineStack
                gap="200"
                // eslint-disable-next-line react/no-array-index-key
                key={`card_${index}`}
              >
                <SkeletonLoader style={{ height: 15, width: 400 }} />
              </InlineStack>
            ))}
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 150 }} />
            <SkeletonLoader style={{ height: 20, width: 350 }} />
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            {["", "", "", ""].map((c, index) => (
              <InlineStack
                gap="200"
                // eslint-disable-next-line react/no-array-index-key
                key={`card_${index}`}
              >
                <SkeletonLoader style={{ height: 15, width: 400 }} />
              </InlineStack>
            ))}
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            {["", "", "", "", ""].map((c, index) => (
              <InlineStack
                gap="200"
                // eslint-disable-next-line react/no-array-index-key
                key={`card_${index}`}
              >
                <SkeletonLoader style={{ height: 15, width: 400 }} />
              </InlineStack>
            ))}
          </BlockStack>
        </Card>
      </BlockStack>
    </Layout.AnnotatedSection>

    <Layout.AnnotatedSection
      title="(Advanced) Customize app embed behavior"
      description={
        <BlockStack gap="200">
          <SkeletonBodyText />

          <SkeletonLoader style={{ height: 10, width: 150 }} />
        </BlockStack>
      }
    >
      <BlockStack gap="400">
        <Card>
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 150 }} />
            <SkeletonLoader style={{ height: 10, width: 350 }} />
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 150 }} />
            <SkeletonLoader style={{ height: 10, width: 350 }} />
            <SkeletonLoader style={{ height: 10, width: 350 }} />
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 150 }} />
            <SkeletonLoader style={{ height: 10, width: 350 }} />
            <SkeletonLoader style={{ height: 10, width: 200 }} />
          </BlockStack>
        </Card>
        <Card>
          <BlockStack gap="200">
            <SkeletonLoader style={{ height: 10, width: 150 }} />
            <SkeletonLoader style={{ height: 10, width: 15 }} />
            <SkeletonLoader style={{ height: 10, width: 400 }} />
          </BlockStack>
        </Card>
      </BlockStack>
    </Layout.AnnotatedSection>
  </Layout>
);

export default SettingsPageSkeleton;
