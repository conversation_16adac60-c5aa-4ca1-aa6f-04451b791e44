import { BlockStack, Box, InlineStack } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const EditValueSkeleton = () => (
  <Box>
    <BlockStack gap="200">
      <InlineStack gap="200">
        <SkeletonLoader style={{ height: 25, width: 25 }} />
        <SkeletonLoader style={{ height: 25, width: 100 }} />
        <SkeletonLoader style={{ height: 25, width: 100 }} />
        <SkeletonLoader style={{ height: 25, width: 100 }} />
        <SkeletonLoader style={{ height: 25, width: 25 }} />
        <SkeletonLoader style={{ height: 25, width: 100 }} />
      </InlineStack>
      <InlineStack gap="200">
        <SkeletonLoader style={{ height: 25, width: 100 }} />
      </InlineStack>
    </BlockStack>
  </Box>
);

export default EditValueSkeleton;
