import { BlockStack, <PERSON>, Card, Divider, InlineGrid, InlineStack, Layout, Page } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const OptionPageSkeleton = () => (
  <div className="option-page-wrapper">
    <Page
      title="Untitled option"
      backAction={{ content: "Back", onAction: () => {} }}
    >
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            <Card>
              <InlineStack
                gap="400"
                align="start"
              >
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 10, width: 100 }} />
                  <SkeletonLoader style={{ height: 25, width: 270 }} />
                  <SkeletonLoader style={{ height: 10, width: 70 }} />
                </BlockStack>
                <BlockStack gap="200">
                  <SkeletonLoader style={{ height: 10, width: 100 }} />
                  <SkeletonLoader style={{ height: 25, width: 300 }} />
                </BlockStack>
              </InlineStack>
            </Card>

            <Card>
              <BlockStack gap="200">
                <InlineStack
                  blockAlign="center"
                  gap="400"
                >
                  <SkeletonLoader style={{ height: 10, width: 100 }} />
                  <SkeletonLoader style={{ height: 20, width: 20 }} />
                </InlineStack>

                <BlockStack gap="400">
                  <InlineStack
                    gap="400"
                    blockAlign="center"
                    align="space-between"
                  >
                    <InlineStack
                      gap="400"
                      blockAlign="center"
                    >
                      <SkeletonLoader style={{ height: 20, width: 20 }} />
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 10, width: 50 }} />
                        <SkeletonLoader style={{ height: 30, width: 100 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 10, width: 50 }} />
                        <SkeletonLoader style={{ height: 30, width: 100 }} />
                      </BlockStack>
                      <InlineStack
                        gap="200"
                        blockAlign="center"
                      >
                        <SkeletonLoader style={{ height: 10, width: 100 }} />
                        <SkeletonLoader style={{ height: 20, width: 20 }} />
                        <SkeletonLoader style={{ height: 10, width: 50 }} />
                        <SkeletonLoader style={{ height: 20, width: 20 }} />
                        <SkeletonLoader style={{ height: 20, width: 20 }} />
                      </InlineStack>
                    </InlineStack>

                    <InlineStack
                      gap="200"
                      blockAlign="center"
                    >
                      <SkeletonLoader style={{ height: 20, width: 20 }} />
                      <SkeletonLoader style={{ height: 20, width: 20 }} />
                    </InlineStack>
                  </InlineStack>
                  <Divider />
                  <InlineStack
                    gap="200"
                    align="start"
                  >
                    <SkeletonLoader style={{ height: 28, width: 100 }} />
                  </InlineStack>
                </BlockStack>
              </BlockStack>
            </Card>

            <InlineGrid
              columns={2}
              gap="200"
            >
              <Card background="bg-fill-secondary">
                <Box
                  minHeight="305px"
                  minWidth="280px"
                >
                  &nbsp;
                </Box>
              </Card>
              <Card background="bg-fill-secondary">
                <Box
                  minHeight="305px"
                  minWidth="280px"
                >
                  &nbsp;
                </Box>
              </Card>
            </InlineGrid>
          </BlockStack>
        </Layout.Section>

        <Layout.Section variant="oneThird">
          <BlockStack gap="400">
            <Card>
              <Box
                minHeight="180px"
                minWidth="200px"
              >
                &nbsp;
              </Box>
            </Card>
            <Card>
              <Box
                minHeight="480px"
                minWidth="200px"
              >
                &nbsp;
              </Box>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
    </Page>
  </div>
);

export default OptionPageSkeleton;
