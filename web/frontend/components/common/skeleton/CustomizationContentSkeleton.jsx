/* eslint-disable react/no-array-index-key */
import {
  <PERSON>leed,
  BlockStack,
  Box,
  Card,
  Divider,
  InlineGrid,
  InlineStack,
  Layout,
  Link,
  List,
  Text,
} from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const CustomizationContentSkeleton = () => (
  <>
    <Box paddingBlockEnd="400">
      <InlineGrid
        columns={{ sm: 1, md: 2 }}
        gap="400"
      >
        <Card>
          <Box>
            <BlockStack gap="200">
              <BlockStack>
                <SkeletonLoader style={{ height: 15, width: 250 }} />
              </BlockStack>
              <SkeletonLoader style={{ height: 15, width: 300 }} />
              <SkeletonLoader style={{ height: 15, width: 200 }} />
              <InlineStack gap="200">
                <SkeletonLoader style={{ height: 20, width: 50 }} />
              </InlineStack>
            </BlockStack>
          </Box>
        </Card>
        <Box>
          <Card>
            <Box>
              <BlockStack gap="200">
                <BlockStack>
                  <SkeletonLoader style={{ height: 15, width: 250 }} />
                </BlockStack>
                <SkeletonLoader style={{ height: 15, width: 300 }} />
                <SkeletonLoader style={{ height: 15, width: 200 }} />
                <InlineStack gap="200">
                  <SkeletonLoader style={{ height: 20, width: 100 }} />
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Box>
      </InlineGrid>
    </Box>
    <Layout>
      <Layout.AnnotatedSection
        title="Font styles"
        description="Customize the style of fonts on the storefront."
      >
        <Card>
          <Box padding="200">
            <BlockStack gap="400">
              <InlineStack align="end">
                <Bleed marginBlockStart="200">
                  <SkeletonLoader style={{ height: 15, width: 80 }} />
                </Bleed>
              </InlineStack>
              {["", "", "", ""].map((c, index) => (
                <BlockStack gap="200">
                  {index !== 0 && (
                    <Bleed marginInline="600">
                      <Divider />
                    </Bleed>
                  )}
                  <InlineStack
                    gap="200"
                    align="space-between"
                    key={`card_${index}`}
                  >
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 70 }} />
                      <SkeletonLoader style={{ height: 45, width: 45 }} />
                    </BlockStack>
                    <InlineStack gap="400">
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Layout.AnnotatedSection>
      <Layout.AnnotatedSection
        title="Button styles"
        description="Customize the style of buttons on the storefront."
      >
        <Card>
          <Box padding="200">
            <BlockStack gap="400">
              <InlineStack align="end">
                <Bleed marginBlockStart="200">
                  <SkeletonLoader style={{ height: 15, width: 80 }} />
                </Bleed>
              </InlineStack>
              <BlockStack gap="200">
                <InlineStack
                  gap="400"
                  align="space-between"
                >
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 70 }} />
                    <SkeletonLoader style={{ height: 25, width: 250 }} />
                  </BlockStack>
                  <BlockStack gap="200">
                    <SkeletonLoader style={{ height: 15, width: 70 }} />
                    <SkeletonLoader style={{ height: 25, width: 250 }} />
                  </BlockStack>
                </InlineStack>
              </BlockStack>
              {["", "", ""].map((c, index) => (
                <BlockStack gap="200">
                  {index !== 0 && (
                    <Bleed marginInline="600">
                      <Divider />
                    </Bleed>
                  )}
                  <InlineStack
                    gap="200"
                    align="space-between"
                    key={`card_${index}`}
                  >
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 70 }} />
                      <SkeletonLoader style={{ height: 45, width: 45 }} />
                    </BlockStack>
                    <InlineStack gap="400">
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Layout.AnnotatedSection>
      <Layout.AnnotatedSection
        title="Swatch styles"
        description="Customize the style of the image and color swatches on the storefront."
      >
        <Card>
          <Box padding="200">
            <BlockStack gap="400">
              <InlineStack align="end">
                <Bleed marginBlockStart="200">
                  <SkeletonLoader style={{ height: 15, width: 80 }} />
                </Bleed>
              </InlineStack>
              {["", ""].map((c, index) => (
                <BlockStack gap="200">
                  {index !== 0 && (
                    <Bleed marginInline="600">
                      <Divider />
                    </Bleed>
                  )}
                  <InlineStack
                    gap="200"
                    align="space-between"
                    key={`card_${index}`}
                  >
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 70 }} />
                      <SkeletonLoader style={{ height: 45, width: 45 }} />
                    </BlockStack>
                    <InlineStack gap="400">
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Layout.AnnotatedSection>
      <Layout.AnnotatedSection
        title="Input styles"
        description="Customize the style of text box, multi-line text box, number field and date picker on the storefront."
      >
        <Card>
          <Box padding="200">
            <BlockStack gap="400">
              <InlineStack align="end">
                <Bleed marginBlockStart="200">
                  <SkeletonLoader style={{ height: 15, width: 80 }} />
                </Bleed>
              </InlineStack>
              {[""].map((c, index) => (
                <BlockStack gap="200">
                  {index !== 0 && (
                    <Bleed marginInline="600">
                      <Divider />
                    </Bleed>
                  )}
                  <InlineStack
                    gap="200"
                    align="space-between"
                    key={`card_${index}`}
                  >
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 70 }} />
                      <SkeletonLoader style={{ height: 45, width: 45 }} />
                    </BlockStack>
                    <InlineStack gap="400">
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Layout.AnnotatedSection>
      <Layout.AnnotatedSection
        title="Dropdown"
        description="Customize the style of dropdowns on the storefront."
      >
        <Card>
          <Box padding="200">
            <BlockStack gap="400">
              <InlineStack align="end">
                <Bleed marginBlockStart="200">
                  <SkeletonLoader style={{ height: 15, width: 80 }} />
                </Bleed>
              </InlineStack>
              {[""].map((c, index) => (
                <BlockStack gap="200">
                  {index !== 0 && (
                    <Bleed marginInline="600">
                      <Divider />
                    </Bleed>
                  )}
                  <InlineStack
                    gap="200"
                    align="space-between"
                    key={`card_${index}`}
                  >
                    <BlockStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 70 }} />
                      <SkeletonLoader style={{ height: 45, width: 45 }} />
                    </BlockStack>
                    <InlineStack gap="400">
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                      <BlockStack gap="200">
                        <SkeletonLoader style={{ height: 15, width: 70 }} />
                        <SkeletonLoader style={{ height: 25, width: 140 }} />
                      </BlockStack>
                    </InlineStack>
                  </InlineStack>
                </BlockStack>
              ))}
            </BlockStack>
          </Box>
        </Card>
      </Layout.AnnotatedSection>
      <Layout.AnnotatedSection
        title="Customize option styles with CSS"
        description={
          <BlockStack gap="200">
            <Text as="p">
              You can write your own CSS code using the classes below to customize the style of the options.
            </Text>

            <Text as="p">
              Don&apos;t want to code?{" "}
              <Link
                url="mailto:<EMAIL>?subject=EasyFlow Custom Styles"
                target="_blank"
              >
                Contact us
              </Link>{" "}
              and we can make the styling for you free of charge.
            </Text>

            <Text
              as="p"
              fontWeight="semibold"
            >
              CSS classes:
            </Text>

            <List type="bullet">
              <List.Item>ef__product-option-root</List.Item>

              <List.Item>ef__option-title</List.Item>
              <List.Item>ef__option-title-[option type]</List.Item>
              <List.Item>ef__option-selected-values</List.Item>
              <List.Item>ef__option-selected-values-price</List.Item>
              <List.Item>ef__option-title-required</List.Item>

              <List.Item>ef__option-description</List.Item>
              <List.Item>ef__option-description-[option type]</List.Item>

              <List.Item>ef__option-values</List.Item>

              <List.Item>ef__option-value</List.Item>
              <List.Item>ef__option-value-[option type]</List.Item>

              <List.Item>ef__option-value-price</List.Item>

              <List.Item>ef__option-value-description</List.Item>
              <List.Item>ef__option-value-description-[option type]</List.Item>

              <List.Item>ef__options-addon-total</List.Item>
              <List.Item>ef__options-addon-total-amount</List.Item>

              <List.Item>ef__character-counter</List.Item>
            </List>

            <Text
              as="p"
              variant="bodySm"
            >
              where [option type] is one of the following: checkbox, dropdown, image-swatch, radio-button, button,
              text-box, multi-line-text-box, number-field, date-picker, color-swatch
            </Text>
          </BlockStack>
        }
      >
        <Card>
          <BlockStack gap="200">
            <Text
              as="h2"
              variant="headingSm"
            >
              CSS Code
            </Text>

            <Box
              minHeight="450px"
              padding="200"
              borderRadius="200"
              borderColor="border-inverse"
              borderWidth="0165"
            />
          </BlockStack>
        </Card>
      </Layout.AnnotatedSection>
    </Layout>
  </>
);

export default CustomizationContentSkeleton;
