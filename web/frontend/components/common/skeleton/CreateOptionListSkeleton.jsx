import { BlockStack, Box, Card, InlineStack } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

const CreateOptionListSkeleton = () => (
  <Card>
    <Box padding="300">
      <BlockStack
        align="center"
        inlineAlign="center"
        gap="300"
      >
        <SkeletonLoader style={{ height: 20, width: 250 }} />
        <BlockStack
          gap="100"
          align="center"
          inlineAlign="center"
        >
          <SkeletonLoader style={{ height: 15, width: 400 }} />
          <SkeletonLoader style={{ height: 15, width: 380 }} />
          <SkeletonLoader style={{ height: 15, width: 350 }} />
        </BlockStack>
        <div style={{ marginTop: "20px" }}>
          <InlineStack gap="400">
            <SkeletonLoader style={{ height: 20, width: 100 }} />
            <SkeletonLoader style={{ height: 20, width: 100 }} />
          </InlineStack>
        </div>
      </BlockStack>
    </Box>
  </Card>
);

export default CreateOptionListSkeleton;
