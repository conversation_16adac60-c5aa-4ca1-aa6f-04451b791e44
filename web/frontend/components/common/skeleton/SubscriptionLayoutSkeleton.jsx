/* eslint-disable react/prop-types */
// @ts-nocheck
/* eslint-disable react/no-array-index-key */
import { BlockStack, Box, Card, Divider, Grid, InlineGrid, InlineStack, Page } from "@shopify/polaris";
import SkeletonLoader from "./Skeleton";

/**
 *
 * @param {{isLayoutSkeleton?: boolean, showFreePlan?: boolean, pageTitle?: string, subTitle?: string}} isLayoutSkeleton
 * @returns
 */
const SubscriptionLayoutSkeleton = ({
  isLayoutSkeleton = true,
  showFreePlan = true,
  pageTitle = "Choose the perfect plan for your store",
  subTitle = "Explore our flexible pricing options and choose the one that fits your needs",
}) =>
  isLayoutSkeleton ? (
    <Page
      narrowWidth
      title={pageTitle}
      subtitle={subTitle}
      backAction={{ content: "Back" }}
    >
      <Box>
        <div className="subscription-wrapper">
          <BlockStack gap="400">
            <Card padding="0">
              <BlockStack>
                <Box
                  borderStartEndRadius="050"
                  padding="400"
                >
                  <InlineStack
                    align="space-between"
                    blockAlign="center"
                  >
                    <InlineStack gap="200">
                      <SkeletonLoader style={{ height: 15, width: 15 }} />
                      <SkeletonLoader style={{ height: 15, width: 350 }} />
                    </InlineStack>
                    <SkeletonLoader style={{ height: 15, width: 20 }} />
                  </InlineStack>
                </Box>

                <Divider />
                <div className="">
                  <Box padding="400">
                    <BlockStack gap="300">
                      <SkeletonLoader style={{ height: 15, width: 450 }} />
                    </BlockStack>
                  </Box>
                </div>
              </BlockStack>
            </Card>
            <div className="pro_plans">
              <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 2 }}>
                <div style={{ display: "flex" }}>
                  <Grid.Cell key="pro_plans">
                    <Card padding="0">
                      <Box>
                        <BlockStack>
                          <Box padding="400">
                            <BlockStack gap="400">
                              <InlineStack
                                align="space-between"
                                wrap={false}
                              >
                                <BlockStack
                                  gap="100"
                                  inlineAlign="start"
                                >
                                  <InlineStack
                                    align="start"
                                    gap="200"
                                  >
                                    <SkeletonLoader style={{ height: 15, width: 50 }} />
                                    <SkeletonLoader style={{ height: 15, width: 100 }} />
                                  </InlineStack>
                                  <BlockStack
                                    align="start"
                                    gap="200"
                                  >
                                    <SkeletonLoader style={{ height: 15, width: 100 }} />
                                  </BlockStack>
                                </BlockStack>
                                <BlockStack inlineAlign="end">
                                  <SkeletonLoader style={{ height: 15, width: 100 }} />
                                </BlockStack>
                              </InlineStack>
                            </BlockStack>
                          </Box>
                          <Divider />
                          <div className="plan_card_feature">
                            <Box padding="400">
                              <BlockStack gap="200">
                                {["", "", "", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                                  <InlineStack
                                    gap="200"
                                    wrap={false}
                                    key={`${index}-${Math.random()}`}
                                    blockAlign="center"
                                  >
                                    <SkeletonLoader style={{ height: 15, width: 20 }} />
                                    <SkeletonLoader style={{ height: 15, width: 200 }} />
                                  </InlineStack>
                                ))}
                              </BlockStack>
                            </Box>
                          </div>
                          <Divider />
                          <div className="plan_card_footer">
                            <Box padding="400">
                              <SkeletonLoader style={{ height: 25, width: 250 }} />
                            </Box>
                          </div>
                        </BlockStack>
                      </Box>
                    </Card>
                  </Grid.Cell>
                </div>

                <div className="visionary_plan">
                  <Grid.Cell key="visionary_plan">
                    <Card padding="0">
                      <BlockStack>
                        <Box padding="400">
                          <BlockStack gap="400">
                            <InlineStack
                              align="space-between"
                              wrap={false}
                            >
                              <BlockStack
                                gap="100"
                                inlineAlign="start"
                              >
                                <InlineStack
                                  align="start"
                                  gap="200"
                                >
                                  <SkeletonLoader style={{ height: 15, width: 50 }} />
                                  <SkeletonLoader style={{ height: 15, width: 100 }} />
                                </InlineStack>
                                <BlockStack
                                  align="start"
                                  gap="200"
                                >
                                  <SkeletonLoader style={{ height: 15, width: 100 }} />
                                </BlockStack>
                              </BlockStack>
                              <BlockStack inlineAlign="end">
                                <SkeletonLoader style={{ height: 15, width: 100 }} />
                                <InlineStack blockAlign="end">
                                  {/* <SkeletonLoader style={{ height: 15, width: 100 }} />
                                <SkeletonLoader style={{ height: 15, width: 100 }} /> */}
                                </InlineStack>
                              </BlockStack>
                            </InlineStack>
                          </BlockStack>
                        </Box>
                        <Divider />
                        <div className="plan_card_feature">
                          <Box padding="400">
                            <BlockStack gap="200">
                              {["", "", "", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                                <InlineStack
                                  gap="200"
                                  wrap={false}
                                  key={`${index}-${Math.random()}`}
                                  blockAlign="center"
                                >
                                  <SkeletonLoader style={{ height: 15, width: 20 }} />
                                  <SkeletonLoader style={{ height: 15, width: 200 }} />
                                </InlineStack>
                              ))}
                            </BlockStack>
                          </Box>
                        </div>
                        <Divider />
                        <div className="plan_card_footer">
                          <Box padding="400">
                            <SkeletonLoader style={{ height: 25, width: 250 }} />
                          </Box>
                        </div>
                      </BlockStack>
                    </Card>
                  </Grid.Cell>
                </div>
              </Grid>
            </div>
            {showFreePlan && (
              <>
                <Box>
                  <InlineStack
                    blockAlign="center"
                    gap="400"
                  >
                    <Box width="40%">
                      <Divider borderColor="border" />
                    </Box>
                    <SkeletonLoader style={{ height: 15, width: 80 }} />
                    <Box width="40%">
                      <Divider borderColor="border" />
                    </Box>
                  </InlineStack>
                </Box>

                <div className="free-plan">
                  <Card padding="0">
                    <BlockStack>
                      <Box
                        borderStartEndRadius="050"
                        padding="400"
                      >
                        <InlineStack
                          align="space-between"
                          blockAlign="center"
                        >
                          <BlockStack gap="100">
                            <InlineStack
                              align="start"
                              gap="200"
                            >
                              <SkeletonLoader style={{ height: 15, width: 60 }} />
                              <SkeletonLoader style={{ height: 15, width: 80 }} />
                            </InlineStack>
                            <SkeletonLoader style={{ height: 15, width: 180 }} />
                          </BlockStack>
                          <BlockStack align="end">
                            <InlineStack
                              gap="400"
                              blockAlign="center"
                            >
                              <InlineStack>
                                <SkeletonLoader style={{ height: 15, width: 90 }} />
                              </InlineStack>

                              <Box>
                                <SkeletonLoader style={{ height: 20, width: 60 }} />
                              </Box>
                            </InlineStack>
                          </BlockStack>
                        </InlineStack>
                      </Box>

                      <Divider />
                      <div className="plan_card_feature">
                        <Box padding="400">
                          <BlockStack gap="300">
                            <Box>
                              <InlineGrid
                                columns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
                                gap="200"
                              >
                                {["", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                                  <InlineStack
                                    gap="200"
                                    wrap={false}
                                    key={`${index}-${Math.random()}`}
                                    blockAlign="center"
                                  >
                                    <SkeletonLoader style={{ height: 15, width: 20 }} />
                                    <SkeletonLoader style={{ height: 15, width: 200 }} />
                                  </InlineStack>
                                ))}
                              </InlineGrid>
                            </Box>
                          </BlockStack>
                        </Box>
                      </div>
                    </BlockStack>
                  </Card>
                </div>
              </>
            )}
          </BlockStack>
        </div>
      </Box>
    </Page>
  ) : (
    <div className="subscription-wrapper">
      <BlockStack gap="400">
        <Card padding="0">
          <BlockStack>
            <Box
              borderStartEndRadius="050"
              padding="400"
            >
              <InlineStack
                align="space-between"
                blockAlign="center"
              >
                <InlineStack gap="200">
                  <SkeletonLoader style={{ height: 15, width: 15 }} />
                  <SkeletonLoader style={{ height: 15, width: 350 }} />
                </InlineStack>
                <SkeletonLoader style={{ height: 15, width: 20 }} />
              </InlineStack>
            </Box>

            <Divider />
            <div className="">
              <Box padding="400">
                <BlockStack gap="300">
                  <SkeletonLoader style={{ height: 15, width: 450 }} />
                </BlockStack>
              </Box>
            </div>
          </BlockStack>
        </Card>
        <div className="pro_plans">
          <Grid columns={{ xs: 1, sm: 1, md: 2, lg: 2 }}>
            <div style={{ display: "flex" }}>
              <Grid.Cell key="pro_plans">
                <Card padding="0">
                  <Box>
                    <BlockStack>
                      <Box padding="400">
                        <BlockStack gap="400">
                          <InlineStack
                            align="space-between"
                            wrap={false}
                          >
                            <BlockStack
                              gap="100"
                              inlineAlign="start"
                            >
                              <InlineStack
                                align="start"
                                gap="200"
                              >
                                <SkeletonLoader style={{ height: 15, width: 50 }} />
                                <SkeletonLoader style={{ height: 15, width: 100 }} />
                              </InlineStack>
                              <BlockStack
                                align="start"
                                gap="200"
                              >
                                <SkeletonLoader style={{ height: 15, width: 100 }} />
                              </BlockStack>
                            </BlockStack>
                            <BlockStack inlineAlign="end">
                              <SkeletonLoader style={{ height: 15, width: 100 }} />
                            </BlockStack>
                          </InlineStack>
                        </BlockStack>
                      </Box>
                      <Divider />
                      <div className="plan_card_feature">
                        <Box padding="400">
                          <BlockStack gap="200">
                            {["", "", "", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                              <InlineStack
                                gap="200"
                                wrap={false}
                                key={`${index}-${Math.random()}`}
                                blockAlign="center"
                              >
                                <SkeletonLoader style={{ height: 15, width: 20 }} />
                                <SkeletonLoader style={{ height: 15, width: 200 }} />
                              </InlineStack>
                            ))}
                          </BlockStack>
                        </Box>
                      </div>
                      <Divider />
                      <div className="plan_card_footer">
                        <Box padding="400">
                          <SkeletonLoader style={{ height: 25, width: 250 }} />
                        </Box>
                      </div>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
            </div>

            <div className="visionary_plan">
              <Grid.Cell key="visionary_plan">
                <Card padding="0">
                  <BlockStack>
                    <Box padding="400">
                      <BlockStack gap="400">
                        <InlineStack
                          align="space-between"
                          wrap={false}
                        >
                          <BlockStack
                            gap="100"
                            inlineAlign="start"
                          >
                            <InlineStack
                              align="start"
                              gap="200"
                            >
                              <SkeletonLoader style={{ height: 15, width: 50 }} />
                              <SkeletonLoader style={{ height: 15, width: 100 }} />
                            </InlineStack>
                            <BlockStack
                              align="start"
                              gap="200"
                            >
                              <SkeletonLoader style={{ height: 15, width: 100 }} />
                            </BlockStack>
                          </BlockStack>
                          <BlockStack inlineAlign="end">
                            <SkeletonLoader style={{ height: 15, width: 100 }} />
                            <InlineStack blockAlign="end">
                              {/* <SkeletonLoader style={{ height: 15, width: 100 }} />
                                <SkeletonLoader style={{ height: 15, width: 100 }} /> */}
                            </InlineStack>
                          </BlockStack>
                        </InlineStack>
                      </BlockStack>
                    </Box>
                    <Divider />
                    <div className="plan_card_feature">
                      <Box padding="400">
                        <BlockStack gap="200">
                          {["", "", "", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                            <InlineStack
                              gap="200"
                              wrap={false}
                              key={`${index}-${Math.random()}`}
                              blockAlign="center"
                            >
                              <SkeletonLoader style={{ height: 15, width: 20 }} />
                              <SkeletonLoader style={{ height: 15, width: 200 }} />
                            </InlineStack>
                          ))}
                        </BlockStack>
                      </Box>
                    </div>
                    <Divider />
                    <div className="plan_card_footer">
                      <Box padding="400">
                        <SkeletonLoader style={{ height: 25, width: 250 }} />
                      </Box>
                    </div>
                  </BlockStack>
                </Card>
              </Grid.Cell>
            </div>
          </Grid>
        </div>
        {showFreePlan && (
          <>
            <Box>
              <InlineStack
                blockAlign="center"
                gap="400"
              >
                <Box width="40%">
                  <Divider borderColor="border" />
                </Box>
                <SkeletonLoader style={{ height: 15, width: 80 }} />
                <Box width="40%">
                  <Divider borderColor="border" />
                </Box>
              </InlineStack>
            </Box>

            <div className="free-plan">
              <Card padding="0">
                <BlockStack>
                  <Box
                    borderStartEndRadius="050"
                    padding="400"
                  >
                    <InlineStack
                      align="space-between"
                      blockAlign="center"
                    >
                      <BlockStack gap="100">
                        <InlineStack
                          align="start"
                          gap="200"
                        >
                          <SkeletonLoader style={{ height: 15, width: 60 }} />
                          <SkeletonLoader style={{ height: 15, width: 80 }} />
                        </InlineStack>
                        <SkeletonLoader style={{ height: 15, width: 180 }} />
                      </BlockStack>
                      <BlockStack align="end">
                        <InlineStack
                          gap="400"
                          blockAlign="center"
                        >
                          <InlineStack>
                            <SkeletonLoader style={{ height: 15, width: 90 }} />
                          </InlineStack>

                          <Box>
                            <SkeletonLoader style={{ height: 20, width: 60 }} />
                          </Box>
                        </InlineStack>
                      </BlockStack>
                    </InlineStack>
                  </Box>

                  <Divider />
                  <div className="plan_card_feature">
                    <Box padding="400">
                      <BlockStack gap="300">
                        <Box>
                          <InlineGrid
                            columns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
                            gap="200"
                          >
                            {["", "", "", "", "", "", "", "", "", ""].map((benefit, index) => (
                              <InlineStack
                                gap="200"
                                wrap={false}
                                key={`${index}-${Math.random()}`}
                                blockAlign="center"
                              >
                                <SkeletonLoader style={{ height: 15, width: 20 }} />
                                <SkeletonLoader style={{ height: 15, width: 200 }} />
                              </InlineStack>
                            ))}
                          </InlineGrid>
                        </Box>
                      </BlockStack>
                    </Box>
                  </div>
                </BlockStack>
              </Card>
            </div>
          </>
        )}
      </BlockStack>
    </div>
  );

export default SubscriptionLayoutSkeleton;
