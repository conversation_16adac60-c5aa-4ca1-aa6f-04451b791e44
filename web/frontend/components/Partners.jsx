import { func } from "prop-types";

import { BlockStack, Box, Button, Card, InlineGrid, MediaCard, Text, Tooltip } from "@shopify/polaris";
import { XIcon } from "@shopify/polaris-icons";

/**
 * @param {{
 *   onDismiss: () => void,
 * }} props
 */
export default function Partners({ onDismiss }) {
  return (
    <Card>
      <BlockStack gap="400">
        <InlineGrid columns="1fr auto">
          <Text
            variant="headingMd"
            as="h5"
          >
            App partners
          </Text>
          <Tooltip content="Dismiss">
            <Button
              variant="plain"
              tone="critical"
              onClick={() => {
                onDismiss();
                localStorage.setItem("isAppPartnersDismissed", "true");
              }}
              accessibilityLabel="Dismiss"
              icon={XIcon}
            />
          </Tooltip>
        </InlineGrid>
        <InlineGrid
          alignItems="center"
          gap="400"
          columns={{ xs: 1, sm: 2, md: 2, lg: 2 }}
        >
          <Box>
            <MediaCard
              title={
                <BlockStack gap="200">
                  <Text
                    variant="headingSm"
                    as="h2"
                  >
                    PageFly Landing Page Builder
                  </Text>

                  <Text
                    variant="bodyXs"
                    as="h3"
                  >
                    4.9 ⭐ (10,884)
                  </Text>
                </BlockStack>
              }
              primaryAction={{
                content: "Learn more",
                onAction: () => window.open("https://pagefly.io/?ref=easyflow&target=app-listing", "_blank"),
              }}
              description="Build & Design Stunning Store Pages Without Code. More Customization To Convert Your Visitors."
              size="small"
            >
              <img
                alt=""
                width="100%"
                style={{ objectFit: "cover", objectPosition: "center" }}
                src="https://cdn.shopify.com/app-store/listing_images/f85ee597169457da8ee70b6652cae768/icon/CKmsycCOx_YCEAE=.png"
              />
            </MediaCard>
          </Box>
          <Box>
            <MediaCard
              title={
                <BlockStack gap="200">
                  <Text
                    variant="headingSm"
                    as="h2"
                  >
                    Slide Cart Drawer - Cart Upsell
                  </Text>

                  <Text
                    variant="bodyXs"
                    as="h3"
                  >
                    4.5 ⭐ (229)
                  </Text>
                </BlockStack>
              }
              primaryAction={{
                content: "Learn more",
                onAction: () => window.open("https://apps.shopify.com/swatches-popup-cart", "_blank"),
              }}
              description="Easily customize the Slide Cart Drawer Upsell to perfectly fit your brand, with rewards boost AOV."
              size="small"
            >
              <img
                alt=""
                width="100%"
                style={{ objectFit: "cover", objectPosition: "center" }}
                src="https://cdn.shopify.com/app-store/listing_images/a1a10ea540e1a68828acd39c045ee87d/icon/CP-32ZDKtfgCEAE=.png"
              />
            </MediaCard>
          </Box>
          <Box>
            <MediaCard
              title={
                <BlockStack gap="200">
                  <Text
                    variant="headingSm"
                    as="h2"
                  >
                    UpPromote Affiliate Marketing
                  </Text>

                  <Text
                    variant="bodyXs"
                    as="h3"
                  >
                    4.9 ⭐ (3,626)
                  </Text>
                </BlockStack>
              }
              primaryAction={{
                content: "Learn more",
                onAction: () => window.open("https://partners.secomapp.com/apps/affiliate/EASYFLOW_APP", "_blank"),
              }}
              description="Complete, easy referral & affiliate program app, trusted by top brands & experts for a revenue boost."
              size="small"
            >
              <img
                alt=""
                width="100%"
                style={{ objectFit: "cover", objectPosition: "center" }}
                src="https://cdn.shopify.com/app-store/listing_images/9c2f67b482aeae04937fd544c0bfe6a8/icon/CMjv-JmUpv0CEAE=.png"
              />
            </MediaCard>
          </Box>
          <Box>
            <MediaCard
              title={
                <BlockStack gap="200">
                  <Text
                    variant="headingSm"
                    as="h2"
                  >
                    SEOAnt ‑ AI SEO Optimizer
                  </Text>

                  <Text
                    variant="bodyXs"
                    as="h3"
                  >
                    4.8 ⭐ (1,289)
                  </Text>
                </BlockStack>
              }
              primaryAction={{
                content: "Learn more",
                onAction: () => window.open("https://share.seoant.com/app/11667a7d6fed0501Fn", "_blank"),
              }}
              description="All-in-one SEO booster tools to increase traffic and sales, improve site speed and Google rankings"
              size="small"
            >
              <img
                alt=""
                width="100%"
                style={{ objectFit: "cover", objectPosition: "center" }}
                src="https://cdn.shopify.com/app-store/listing_images/89f080b72a27e71e4d48c9ad2493e78d/icon/CKvTs8ntkvkCEAE=.png"
              />
            </MediaCard>
          </Box>
        </InlineGrid>
      </BlockStack>
    </Card>
  );
}

Partners.propTypes = {
  onDismiss: func.isRequired,
};
