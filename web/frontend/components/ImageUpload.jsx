import { func, object, string } from 'prop-types';

import { DropZone, Text } from '@shopify/polaris';

const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

/**
 * @param {{
 *   imageFile: File | undefined,
 *   imageURL: string | undefined,
 *   onImageFileChange: (imageFile: File) => void,
 * }} props
 */
export default function ImageUpload({ imageFile, imageURL, onImageFileChange }) {
  const isImageExists = !!(imageFile || imageURL);

  return (
    <div style={{ width: '48px', height: '48px' }}>
      <DropZone
        allowMultiple={false}
        onDrop={(_, acceptedFiles) => onImageFileChange(acceptedFiles[0])}
      >
        {isImageExists && (
          <div style={{ display: 'flex', justifyContent: 'center', textAlign: 'center' }}>
            {imageFile?.type && !allowedMimeTypes.includes(imageFile.type) ? (
              <Text variant="bodySm" as="p">
                File type is not supported. Please upload a JPEG, PNG or a GIF image.
              </Text>
            ) : (
              <img
                src={imageFile ? window.URL.createObjectURL(imageFile) : imageURL}
                alt="Thumbnail of uploaded file"
                style={{ maxWidth: '48px', maxHeight: '48px' }}
              />
            )}
          </div>
        )}

        {!isImageExists && <DropZone.FileUpload />}
      </DropZone>
    </div>
  );
}

ImageUpload.propTypes = {
  // eslint-disable-next-line react/forbid-prop-types
  imageFile: object,
  imageURL: string,
  onImageFileChange: func.isRequired,
};
