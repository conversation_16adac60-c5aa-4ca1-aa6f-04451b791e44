import { bool, func, string } from 'prop-types';

import { Modal } from '@shopify/polaris';

/**
 * @param {{
 *   isOpen: boolean,
 *   onClose: (isConfirmed: boolean) => void,
 *   title: string,
 *   prompt: string,
 *   actionLabel: string,
 *   isLoading?: boolean,
 *   isDestructive?: boolean,
 * }} props
 */
export default function PromptModal({
  isOpen,
  onClose,
  title,
  prompt,
  actionLabel,
  isLoading,
  isDestructive,
}) {
  return (
    <Modal
      open={isOpen}
      onClose={() => onClose(false)}
      title={title}
      primaryAction={{
        content: actionLabel,
        onAction: () => onClose(true),
        destructive: isDestructive,
        loading: isLoading,
      }}
      secondaryActions={[{ content: 'Cancel', onAction: () => onClose(false) }]}
    >
      <Modal.Section>
        <p>{prompt}</p>
      </Modal.Section>
    </Modal>
  );
}

PromptModal.propTypes = {
  isOpen: bool.isRequired,
  onClose: func.isRequired,
  title: string.isRequired,
  prompt: string.isRequired,
  actionLabel: string.isRequired,
  isLoading: bool,
  isDestructive: bool,
};
