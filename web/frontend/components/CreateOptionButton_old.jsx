import { bool, func, string } from 'prop-types';
import { useEffect, useState } from 'react';

import { ActionList, Button, Icon, Popover } from '@shopify/polaris';

import { productOptionIcons, productOptionTypes } from '../pages/EditOptionPage';
import navigateToAppPath from '../utilities';

/**
 * @param {{
 *   label: string,
 *   onCreate: (option: ProductOptionType) => void,
 *   onOpen?: () => void,
 *   primary?: boolean,
 *   isFeatureLimited: boolean,
 * }} props
 */
export default function CreateOptionButton({ label, onCreate, onOpen, primary, isFeatureLimited }) {
  const [isMenuOpen, setMenuOpen] = useState(false);

  useEffect(() => {
    if (onOpen && isMenuOpen) {
      onOpen();
    }
  }, [onOpen, isMenuOpen]);

  return (
    <Popover
      active={isMenuOpen}
      activator={
        <Button variant={primary ? 'primary' : undefined} onClick={() => setMenuOpen(!isMenuOpen)}>
          {label}
        </Button>
      }
      autofocusTarget="first-node"
      onClose={() => setMenuOpen(false)}
    >
      <Popover.Pane fixed>
        <Popover.Section>
          <p>Choose option type</p>
        </Popover.Section>
      </Popover.Pane>

      <Popover.Pane>
        <ActionList
          actionRole="menuitem"
          items={productOptionTypes.map((type) => ({
            content: isFeatureLimited && type === 'File upload' ? `${type} (Upgrade plan)` : type,
            prefix: <Icon source={productOptionIcons[type]} />,
            onAction: () =>
              isFeatureLimited && type === 'File upload'
                ? navigateToAppPath('/pricing')
                : onCreate(type),
          }))}
        />
      </Popover.Pane>
    </Popover>
  );
}

CreateOptionButton.propTypes = {
  label: string.isRequired,
  onCreate: func.isRequired,
  onOpen: func,
  primary: bool,
  isFeatureLimited: bool.isRequired,
};
