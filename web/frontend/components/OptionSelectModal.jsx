import { arrayOf, bool, func, object } from "prop-types";
import { useState } from "react";

import { Modal } from "@shopify/polaris";

import ProductOptionList from "./ProductOptionList";

/**
 * @param {{
 *   isOpen: boolean,
 *   onClose: (selectedOptionIds?: number[]) => void,
 *   options: IProductOption[],
 * }} props
 */
export default function OptionSelectModal({ isOpen, onClose, options }) {
  const [selectedOptionIds, setSelectedOptionIds] = useState(/** @type {number[]} */ ([]));

  return (
    <Modal
      open={isOpen}
      title="Add options to option sets"
      onClose={onClose}
      primaryAction={{ content: "Add options", onAction: () => onClose(selectedOptionIds) }}
      secondaryActions={[{ content: "Cancel", onAction: onClose }]}
    >
      <ProductOptionList
        isPlain
        options={options}
        onSelectionChange={(newSelectedIds) =>
          setSelectedOptionIds(
            newSelectedIds.flatMap((selection) =>
              selection.startsWith("option-") ? Number(selection.substring(7)) : []
            )
          )
        }
      />
    </Modal>
  );
}

OptionSelectModal.propTypes = {
  isOpen: bool.isRequired,
  onClose: func.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  options: arrayOf(object).isRequired,
};
