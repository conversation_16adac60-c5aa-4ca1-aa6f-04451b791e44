import { arrayOf, bool, func, shape, string } from 'prop-types';

import {
  Badge,
  BlockStack,
  Box,
  Button,
  Card,
  InlineStack,
  SkeletonBodyText,
  SkeletonDisplayText,
  Text,
} from '@shopify/polaris';

/**
 * @param {{
 *  loading: boolean,
 *  title: string,
 *  description: string,
 *  price: string,
 *  features: string[],
 *  featuredText?: string,
 *  button: {
 *    content: string,
 *    props: {
 *      disabled: boolean,
 *      loading: boolean,
 *      variant: "primary" | "plain" | "tertiary" | "monochromePlain" | undefined
 *      onClick: () => void,
 *     },
 *  },
 *  frequency: string,
 * }} props
 */
export default function PricingCard({
  loading,
  title,
  description,
  price,
  features,
  featuredText,
  button,
  frequency,
}) {
  return (
    <div
      style={{
        width: '19rem',
        borderRadius: '.75rem',
        position: 'relative',
        zIndex: '0',
      }}
    >
      {featuredText && !loading ? (
        <div style={{ position: 'absolute', top: '-15px', right: '6px', zIndex: '100' }}>
          <Badge size="large" tone="success">
            {featuredText}
          </Badge>
        </div>
      ) : null}
      <Card>
        <BlockStack gap="400">
          <BlockStack gap="200" align="start">
            {!loading ? (
              <>
                <Text as="p" variant="headingLg">
                  {title}
                </Text>
                {description ? (
                  <Text as="p" variant="bodySm" tone="subdued">
                    {description}
                  </Text>
                ) : null}
              </>
            ) : (
              <>
                <SkeletonDisplayText size="medium" />
                <SkeletonBodyText lines={2} />
              </>
            )}
          </BlockStack>

          {!loading ? (
            <InlineStack blockAlign="end" gap="100" align="start">
              <Text as="p" variant="heading2xl">
                {price}
              </Text>

              <Box paddingBlockEnd="200">
                <Text as="p" variant="bodySm">
                  / {frequency}
                </Text>
              </Box>
            </InlineStack>
          ) : (
            <Box paddingBlockEnd="200">
              <SkeletonDisplayText size="small" />
            </Box>
          )}

          <BlockStack gap="100">
            {(!loading ? features : Array.from({ length: 8 }))?.map((feature, id) =>
              !loading ? (
                // eslint-disable-next-line react/no-array-index-key
                <div key={id} style={{ visibility: feature ? 'visible' : 'hidden' }}>
                  <Text tone="subdued" as="p" variant="bodyMd">
                    {feature || 'Feature'}
                  </Text>
                </div>
              ) : (
                // eslint-disable-next-line react/no-array-index-key
                <Box key={id} paddingBlockEnd="300">
                  <SkeletonBodyText lines={1} />
                </Box>
              ),
            )}
          </BlockStack>

          <Box paddingBlockStart="200" paddingBlockEnd="200">
            <Button
              variant={button.props.variant}
              onClick={button.props.onClick}
              fullWidth
              disabled={loading || button.props.disabled}
              loading={button.props.loading}
            >
              {!loading ? button.content : ''}
            </Button>
          </Box>
        </BlockStack>
      </Card>
    </div>
  );
}

PricingCard.propTypes = {
  loading: bool,
  title: string.isRequired,
  description: string,
  price: string.isRequired,
  features: arrayOf(string).isRequired,
  featuredText: string,
  button: shape({
    content: string.isRequired,
    props: shape({
      variant: string.isRequired,
      onClick: func.isRequired,
    }).isRequired,
  }).isRequired,
  frequency: string.isRequired,
};
