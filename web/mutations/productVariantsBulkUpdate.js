const productVariantsBulkUpdateMutation = `
mutation productVariantsBulkUpdate($productId: ID!, $variants: [ProductVariantsBulkInput!]!) {
  productVariantsBulkUpdate(productId: $productId, variants: $variants) {
    product {
      id
    }
    productVariants {
      id
      title,
      displayName,
      updatedAt,
      createdAt,
      price
    }
    userErrors {
      field
      message
    }
  }
}
`;

export default productVariantsBulkUpdateMutation;
