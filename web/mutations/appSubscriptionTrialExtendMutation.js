const appSubscriptionTrialExtendMutation = `
mutation AppSubscriptionTrialExtend($id: ID!, $days: Int!) {
  appSubscriptionTrialExtend(id: $id, days: $days) {
    userErrors {
      field
      message
      code
    }
    appSubscription {
      id
      name
      createdAt
      currentPeriodEnd
      lineItems {
        id
        plan {
          pricingDetails {
            __typename
            ... on AppRecurringPricing {
              price {
                amount
              }
              interval
              discount {
                priceAfterDiscount {
                  amount
                }
              }
            }

            ... on AppUsagePricing {
              balanceUsed {
                amount
              }
              cappedAmount {
                amount
              }
              interval
              terms
            }
          }
        }
      }
      status
      test
      trialDays
    }
  }
}`;

export default appSubscriptionTrialExtendMutation;
