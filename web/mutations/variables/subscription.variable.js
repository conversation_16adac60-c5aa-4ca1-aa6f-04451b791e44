// eslint-disable-next-line import/no-extraneous-dependencies
import { appSubscriptionReplacement } from "easyflow-enums";
import lodash from "lodash";
import { generateSubscriptionLineItems, generateSubscriptionReturnUrl } from "../../api/helpers/subscription.helper.js";

const { get } = lodash;

/**
 *
 * @param {IShop | undefined} shop
 * @param {import("../../types/subscriptionPlan").ISubscriptionPlan} plan
 * @param {string} url
 * @param {import("../../types/SubscriptionPlanCoupon").ISubscriptionPlanCoupon | null | undefined} coupon
 * @returns
 */
const generateSubscriptionVariables = (shop, plan, url, coupon) => {
  let isTest = get(process.env, "SHOPIFY_SUBSCRIPTION_TEST_MODE", "false") === "true" || get(plan, "test", false);
  const shopEmail = get(shop, "email", null);

  if (shopEmail && shopEmail.includes("@wpdeveloper.com")) {
    isTest = true;
  }

  return {
    name: get(plan, "slug"),
    returnUrl: generateSubscriptionReturnUrl(url, plan),
    lineItems: generateSubscriptionLineItems(plan, coupon),
    replacementBehavior: appSubscriptionReplacement.APPLY_IMMEDIATELY,
    test: isTest,
    trialDays: get(plan, "trial_days", undefined),
  };
};

// eslint-disable-next-line import/prefer-default-export
export { generateSubscriptionVariables };
