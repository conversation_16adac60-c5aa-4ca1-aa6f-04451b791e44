const appSubscriptionCreateMutation = `
  mutation appSubscriptionCreate(
    $name: String!
    $lineItems: [AppSubscriptionLineItemInput!]!
    $returnUrl: URL!
    $test: Boolean!
    $trialDays: Int
  ) {
    appSubscriptionCreate(
      name: $name
      lineItems: $lineItems
      returnUrl: $returnUrl
      test: $test
      trialDays: $trialDays
    ) {
      appSubscription {
        id
        name
        createdAt
        currentPeriodEnd
        returnUrl
        status
        test
        trialDays
        lineItems {
          id
          plan {
            pricingDetails {
              __typename
              ... on AppRecurringPricing {
                interval
                discount {
                  durationLimitInIntervals
                  priceAfterDiscount {
                    amount
                    currencyCode
                  }
                  remainingDurationInIntervals
                  value
                }
                price {
                  amount
                  currencyCode
                }
              }
              ... on AppUsagePricing {
                balanceUsed{
                  amount
                  currencyCode
                }
                cappedAmount{
                  amount
                  currencyCode
                }
                interval
                terms
              }
            }
          }
        }
      }
      confirmationUrl
      userErrors {
        field
        message
      }
    }
  }
`;

export default appSubscriptionCreateMutation;
