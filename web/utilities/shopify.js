import { queue } from "async";

import { ApiVersion, BillingInterval, LogSeverity } from "@shopify/shopify-api";
// @ts-ignore
// eslint-disable-next-line import/no-unresolved
import { restResources } from "@shopify/shopify-api/rest/admin/2024-04";
import { shopifyApp } from "@shopify/shopify-app-express";

import lodash from "lodash";
import appSubscriptionTrialExtendMutation from "../mutations/appSubscriptionTrialExtendMutation.js";
import appUsageRecordCreate from "../mutations/appUsageRecordCreate.js";
import appSubscriptionCreateMutation from "../mutations/subscriptionCreate.js";
import { generateAppUsageRecordCreateVariables } from "../mutations/variables/appUsageRecordCreate.variable.js";
import { generateSubscriptionVariables } from "../mutations/variables/subscription.variable.js";
import { appQuery, appSubscriptionsQuery } from "../queries/appQueries.js";
import { shopQuery } from "../queries/shopQueries.js";
import trackEvent, { captureException, captureMessage, captureUserErrors } from "./analytics.js";
import { DatastoreSessionStorage, getShopProperties, setShopProperties } from "./datastore.js";

const { get } = lodash;

/** @typedef {{ message: string, field?: string[] }[]} UserErrors */

/**
 * @typedef {{
 *   currentAppInstallation: {
 *     activeSubscriptions: {
 *       name: string,
 *       lineItems: {
 *         id: string,
 *         plan: { pricingDetails: { cappedAmount?: { amount: string } } }
 *       }[]
 *     }[]
 *   }
 * } | undefined} CurrentAppInstallation
 */

export const isProduction = process.env.NODE_ENV === "production";

// export const metafieldNamespace = '$app:product_options';
// export const metafieldKey = 'product_options';
// export const metaobjectType = '$app:product_option';

export const metafieldNamespace = "product_options";

export const optionsMetafieldKey = "options";
export const ranksMetafieldKey = "ranks";
export const optionSetsMetafieldKey = "option_sets";
export const rulesMetafieldKey = "rules";
export const stylesMetafieldKey = "styles";
export const preferencesMetafieldKey = "preferences";
export const watermarkMetafieldKey = "watermark";

export const metaobjectType = "$app:product_option";
export const addonProductType = "23p_po_generated";

/* ----------------------------------- Shopify configuration ------------------------------------ */

// The transactions with Shopify will always be marked as test transactions,
// unless NODE_ENV is production.
/** @type {import('@shopify/shopify-api').BillingConfig} */
export const billingConfig = {
  "Recurring billing plan": {
    interval: BillingInterval.Every30Days,
    amount: 12.99,
    currencyCode: "USD",
    trialDays: 7,
  },
  "Recurring billing plan (24-06-10)": {
    interval: BillingInterval.Every30Days,
    amount: 17.99,
    currencyCode: "USD",
    trialDays: 7,
  },
};

const shopify = shopifyApp({
  api: {
    // @ts-ignore
    apiVersion: get(process.env, "SHOPIFY_API_VERSION", ApiVersion.January25),
    restResources,
    billing: billingConfig,
    logger: { level: isProduction ? LogSeverity.Error : LogSeverity.Debug },
  },
  auth: {
    path: "/api/auth",
    callbackPath: "/api/auth/callback",
  },
  webhooks: {
    path: "/api/webhooks",
  },
  sessionStorage: new DatastoreSessionStorage(),
});

export default shopify;

/* -------------------------------------- Helper functions -------------------------------------- */

/**
 * @param {string} shop
 * @param {string} actionDescription
 */
export async function loadSession(shop, actionDescription) {
  const sessionId = shopify.api.session.getOfflineId(shop);
  const session = await shopify.config.sessionStorage.loadSession(sessionId);

  if (!session) {
    captureMessage(`No session data while ${actionDescription}`, shop);
    return undefined;
  }

  return session;
}

/** @type {Map<string, import('async').QueueObject<() => Promise<any>>>} */
const queryQueues = new Map();

/**
 * @template {Record<any, any>} TResult
 *
 * @param {string} queryString
 * @param {import('@shopify/shopify-api').Session} session
 * @param {Record<string, any>} [variables]
 * @param {(userError: UserErrors[number]) => boolean} [userErrorFilter]
 */
export async function sendQuery(queryString, session, variables, userErrorFilter) {
  const { shop } = session;

  let shopQueue = queryQueues.get(shop);

  if (!shopQueue) {
    shopQueue = queue(async (task) => task(), 40);
    queryQueues.set(shop, shopQueue);
  }

  const resultPromise = shopQueue.push(async () => {
    const client = new shopify.api.clients.Graphql({ session });

    /** @type {import('@shopify/graphql-client').ClientResponse<TResult>} */
    const queryResponse = await client.request(queryString, { variables });

    const userErrors = /** @type {{ userErrors: UserErrors } | undefined} */ (
      Object.values(queryResponse.data || {}).find(
        (value) => value && typeof value === "object" && value.userErrors?.length
      )
    )?.userErrors?.filter(userErrorFilter || (() => true));

    if (userErrors) {
      captureUserErrors(userErrors, shop);
    }

    return queryResponse;
  });

  const errorPromise = shopQueue.error();
  const result = await Promise.race([resultPromise, errorPromise]);

  return result ? result.data : errorPromise;
}

/**
 * @param {import('@shopify/shopify-api').Session} session
 */
export async function getShopId(session) {
  const shopProperties = await getShopProperties(session.shop);

  let globalId = shopProperties?.globalId;

  if (!globalId) {
    // TODO: populate all shops' global IDs and get them for new stores in the
    // `APP_SUBSCRIPTIONS_UPDATE` hook.
    /** @type {{ shop?: { id: string } } | undefined} */
    const shopResult = await sendQuery("{ shop { id } }", session);
    globalId = shopResult?.shop?.id;

    if (globalId) {
      setShopProperties({ globalId }, shopProperties, session.shop);
    }
  }

  return globalId;
}

/**
 * @param {import('@shopify/shopify-api').Session | undefined} session
 * @param {string | undefined} chargeId
 *
 * @returns {Promise<IShopDetails | undefined>}
 */
export async function getShopDetails(session, chargeId) {
  if (!session) {
    return undefined;
  }

  const { shop } = session;

  const [shopProperties, shopResult, activeSubscriptions = []] = await Promise.all([
    getShopProperties(shop),
    /**
     * @type {{
     *   shop?: {
     *     currencyFormats: { moneyWithCurrencyFormat: string },
     *     plan: { partnerDevelopment: boolean }
     *   },
     * } | undefined}
     */ (
      (async () => {
        try {
          const result = await sendQuery(
            `{ shop { currencyFormats { moneyWithCurrencyFormat }, plan { partnerDevelopment } } }`,
            session
          );

          return result;
        } catch (_) {
          // If the previous session is not validate anymore, an error is thrown
          return undefined;
        }
      })()
    ),
    // Check subscription to prevent this returning false before it's updated in the db
    ...(chargeId ? [getActiveSubscriptions(session)] : [[]]),
  ]);

  const isSubscriptionActive = activeSubscriptions?.length > 0;

  if (isSubscriptionActive) {
    handleSubscriptionChange("ACTIVE", shop);
  }

  const updatedIsSubscriptionActive = isSubscriptionActive || shopProperties?.isSubscriptionActive;

  const currency = shopResult?.shop?.currencyFormats.moneyWithCurrencyFormat[0] || "";

  return {
    shop,
    currency,
    isExtensionEnabled: !!shopProperties?.isExtensionEnabled,
    isGrandfathered: !!shopProperties?.isGrandfathered,
    isAddonsFree: !!shopProperties?.isAddonsFree,
    isPartnerDevelopment: !!shopResult?.shop?.plan.partnerDevelopment,
    isSubscriptionActive: updatedIsSubscriptionActive,
    isOn18DollarPlan: !!shopProperties?.isOn18DollarPlan,
    isFeatureLimited:
      !shopProperties?.isGrandfathered && !shopResult?.shop?.plan.partnerDevelopment && !updatedIsSubscriptionActive,
  };
}

/**
 * @param {import('@shopify/shopify-api').Session} session
 */
export async function getSubscriptionRedirect(session) {
  const plans = Object.keys(billingConfig);

  const subscribeUrl = await shopify.api.billing.request({
    session,
    plan: plans[0],
    isTest: !isProduction,
  });

  trackEvent("redirected to subscription page", session.shop);

  return subscribeUrl;
}

/**
 * @param {import('@shopify/shopify-api').Session} session
 */
export async function checkSubscription(session) {
  const plans = Object.keys(billingConfig);
  const isActive = await shopify.api.billing.check({ session, plans, isTest: !isProduction });

  return isActive;
}

/**
 * @param {import('@shopify/shopify-api').Session} session
 */
export async function getActiveSubscriptions(session) {
  // const { activeSubscriptions } = await shopify.api.billing.subscriptions({ session });
  // return activeSubscriptions;

  try {
    const client = new shopify.api.clients.Graphql({ session });
    const { data } = await client.request(appSubscriptionsQuery);
    const { activeSubscriptions = [] } = data.appInstallation || {};
    return activeSubscriptions;
  } catch (error) {
    captureException(error, session.shop);
    return [];
  }
}

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {string} subscriptionId
 * @param {boolean} prorate
 * @param {boolean} isTest
 * @returns
 */
export async function cancelSubscription(session, subscriptionId, prorate = false, isTest = false) {
  const appSubscription = await shopify.api.billing.cancel({
    session,
    subscriptionId,
    prorate,
    isTest,
  });
  return appSubscription;
}

/**
 * @param {import('@shopify/shopify-api').Session} session
 * @param {boolean} isShowWatermark
 */
export async function setWatermarkVisibility(session, isShowWatermark) {
  try {
    /** @type {{ currentAppInstallation?: { id: string } } | undefined} */
    const currentAppInstallationResult = await sendQuery(`{ currentAppInstallation { id } }`, session);

    const metafields = [
      {
        namespace: metafieldNamespace,
        key: watermarkMetafieldKey,
        type: "boolean",
        ownerId: currentAppInstallationResult?.currentAppInstallation?.id,
        value: isShowWatermark.toString(),
      },
    ];

    await sendQuery(
      `mutation MetafieldsSetMutation($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          userErrors { field, message }
        }
      }`,
      session,
      { metafields }
    );
  } catch (error) {
    captureException(error, session.shop);
  }
}

/**
 * @param {string} status
 * @param {string} shop
 * @param {boolean} [isAppInstalled]
 */
export async function handleSubscriptionChange(status, shop, isAppInstalled = true) {
  if (status === "EXPIRED") {
    return;
  }

  const isActive = status === "ACTIVE";

  const [session, shopProperties] = await Promise.all([
    isAppInstalled && loadSession(shop, "handling subscription change"),
    getShopProperties(shop),
  ]);

  /** @type {string | undefined} */
  let email;

  if (isAppInstalled && session && status !== "FROZEN") {
    try {
      /** @type {{ shop: { email: string } } | undefined} */
      const shopResult = await sendQuery("{ shop { email } }", session);

      ({ email } = shopResult?.shop || {});
    } catch (error) {
      captureException(error, shop);
    }
  }

  // Send shop email with analytics
  trackEvent(`subscription ${isActive ? "activated" : "deactivated"}`, shop, {
    email,
    status,
  });

  if (!isActive) {
    delete shopProperties?.isSubscriptionActive;
    delete shopProperties?.isOn18DollarPlan;

    await setShopProperties({}, shopProperties, shop, true);
    return;
  }

  await setShopProperties({ isSubscriptionActive: true }, shopProperties, shop, true);
}

/**
 * @param {string} shop
 */
export async function createMetafieldDefinitions(shop) {
  const session = await loadSession(shop, "creating metafield definitions");
  if (!session) {
    return;
  }

  try {
    const optionDefinitionsPromise = (async () => {
      // Create or update metaobject definitions
      const optionDefinition = {
        name: "Option",
        type: metaobjectType,
        access: { storefront: "PUBLIC_READ" },
        fieldDefinitions: [
          { key: "optionId", name: "Option ID", type: "single_line_text_field" },
          { key: "title", name: "Title", type: "single_line_text_field" },
          { key: "type", name: "Type", type: "single_line_text_field" },
          { key: "inCartName", name: "In-cart Name", type: "single_line_text_field" },
          { key: "placeholderText", name: "Placeholder text", type: "single_line_text_field" },
          { key: "description", name: "Description", type: "single_line_text_field" },
          { key: "isRequired", name: "Required", type: "boolean" },
          { key: "isMultiSelect", name: "Multi-select", type: "boolean" },
          {
            key: "minimumSelectionCount",
            name: "Minimum number of selected values",
            type: "number_integer",
          },
          {
            key: "maximumSelectionCount",
            name: "Maximum number of selected values",
            type: "number_integer",
          },
          {
            key: "isShowSelectedValues",
            name: "Show the selected values next to the option title",
            type: "boolean",
          },
          {
            key: "minimumValue",
            name: "The lower limit for the option value",
            type: "number_integer",
          },
          {
            key: "maximumValue",
            name: "The upper limit for the option value",
            type: "number_integer",
          },
          {
            key: "allowedFileTypes",
            name: "Allowed file types",
            type: "list.single_line_text_field",
          },
          { key: "values", name: "Values", type: "json" },
        ],
      };

      /**
       * @type {{
       *   metaobjectDefinitionCreate: {
       *     metaobjectDefinition?: { id: string; },
       *     userErrors: UserErrors
       *   }
       * } | undefined}
       */
      const optionDefinitionCreateResult = await sendQuery(
        `mutation MetaobjectDefinitionCreateMutation($definition: MetaobjectDefinitionCreateInput!) {
          metaobjectDefinitionCreate(definition: $definition) {
            metaobjectDefinition { id }
            userErrors { field, message }
          }
        }`,
        session,
        { definition: optionDefinition },
        (error) => error.field?.[0] !== "definition" || error.field?.[1] !== "type"
      );

      const result = optionDefinitionCreateResult?.metaobjectDefinitionCreate;
      if (!result?.metaobjectDefinition?.id) {
        return;
      }

      // Create or update metafield definitions
      const optionsMetafieldDefinition = {
        name: "EasyFlow Product options",
        namespace: metafieldNamespace,
        key: optionsMetafieldKey,
        description: "List of options available for the product",
        type: "list.metaobject_reference",
        ownerType: "PRODUCT",
        validations: [{ name: "metaobject_definition_id", value: result.metaobjectDefinition.id }],
      };

      await sendQuery(
        `mutation MetafieldDefinitionCreateMutation($definition: MetafieldDefinitionInput!) {
          metafieldDefinitionCreate(definition: $definition) {
            userErrors { field, message }
          }
        }`,
        session,
        { definition: optionsMetafieldDefinition }
      );
    })();

    const otherMetafieldDefinitions = [
      {
        name: "EasyFlow Product Option ranks",
        namespace: metafieldNamespace,
        key: ranksMetafieldKey,
        description: "Rank of option/sets to be displayed on the product page",
        type: "json",
        ownerType: "PRODUCT",
      },
      {
        name: "EasyFlow Product Option rules",
        namespace: metafieldNamespace,
        key: rulesMetafieldKey,
        description: "List of rules applied to product options",
        type: "json",
        ownerType: "PRODUCT",
      },
      {
        name: "EasyFlow global product option sets",
        namespace: metafieldNamespace,
        key: optionSetsMetafieldKey,
        description: "List of option sets automatically added to products by a condition",
        type: "json",
        ownerType: "SHOP",
      },
      {
        name: "EasyFlow Product Option styles",
        namespace: metafieldNamespace,
        key: stylesMetafieldKey,
        description: "Custom styles to apply to options and values",
        type: "multi_line_text_field",
        ownerType: "SHOP",
      },
      {
        name: "EasyFlow Product Option preferences",
        namespace: metafieldNamespace,
        key: preferencesMetafieldKey,
        description: "Custom preferences for options and values",
        type: "json",
        ownerType: "SHOP",
      },
      {
        name: "SEO Hidden",
        namespace: "seo",
        key: "hidden",
        description: "Hide product from SEO",
        type: "number_integer",
        ownerType: "PRODUCT",
        validations: [
          { name: "min", value: "0" },
          { name: "max", value: "1" },
        ],
      },
    ];

    const otherMetafieldPromises = otherMetafieldDefinitions.map((definition) =>
      sendQuery(
        `mutation MetafieldDefinitionCreateMutation($definition: MetafieldDefinitionInput!) {
          metafieldDefinitionCreate(definition: $definition) {
            userErrors { field, message }
          }
        }`,
        session,
        { definition },
        (error) => error.field?.[0] !== "definition" || error.field?.[1] !== "key"
      )
    );

    await Promise.all([optionDefinitionsPromise, ...otherMetafieldPromises]);
  } catch (error) {
    captureException(error, shop);
  }
}

/**
 * @param {import("@shopify/shopify-api").Session} session
 *
 * !!! WARNING: DO NOT USE THIS FUNCTION IN A PRODUCTION STORE!
 * IT WILL DELETE ALL THE METAFIELDS AND METAOBJECTS OF CREATED OPTIONS !!!
 */
export async function dangerouslyDeleteMetafieldDefinitions(session) {
  // Delete old metafield definitions
  /**
   * @type {[
   *   { metaobjectDefinitions: { nodes: { id: string }[] } } | undefined,
   *   { metafieldDefinitions: { nodes: { id: string }[] } } | undefined,
   * ]}
   */
  const [metaobjectIdsResult, metafieldIdsResult] = await Promise.all([
    await sendQuery(`{ metaobjectDefinitions(first: 50) { nodes { id } } }`, session),
    await sendQuery(
      `{
        metafieldDefinitions(namespace: "${metafieldNamespace}", ownerType: PRODUCT, first: 10) {
          nodes { id }
        }
      }`,
      session
    ),
  ]);

  await Promise.all([
    ...(metaobjectIdsResult?.metaobjectDefinitions.nodes.map((definition) =>
      sendQuery(
        `mutation MetaobjectDefinitionDeleteMutation {
          metaobjectDefinitionDelete(id: "${definition.id}") {
            userErrors { field, message }
          }
        }`,
        session
      )
    ) || []),
    ...(metafieldIdsResult?.metafieldDefinitions.nodes.map((definition) =>
      sendQuery(
        `mutation MetafieldDefinitionDelete {
          metafieldDefinitionDelete(
            id: "${definition.id}",
            deleteAllAssociatedMetafields: true,
          ) {
            userErrors { field, message }
          }
        }`,
        session
      )
    ) || []),
  ]);
}

/**
 * @param {FileData[]} files
 * @param {'IMAGE' | 'FILE'} contentType
 * @param {import('@shopify/shopify-api').Session} session
 */
export async function createUploadUrls(files, contentType, session) {
  const { shop } = session;

  if (
    files.some(({ filename, mimeType }) => typeof filename !== "string" || !filename || typeof mimeType !== "string")
  ) {
    captureMessage("Filename or mime type is not given.", shop, { files });
    throw new Error("Bad file data");
  }

  const inputFiles = files.map(({ filename, mimeType }) => ({
    resource: contentType,
    filename,
    mimeType,
    httpMethod: "PUT",
  }));

  /**
   * @type {{
   *   stagedUploadsCreate: {
   *     stagedTargets?: FileUploadRequest[],
   *     userErrors?: { message: string }[],
   *   }
   * } | undefined}
   */
  const uploadCreateResponse = await sendQuery(
    `mutation StagedUploadsCreateMutation($input: [StagedUploadInput!]!) {
      stagedUploadsCreate(input: $input) {
        stagedTargets { resourceUrl, url, parameters { name, value } }
        userErrors { field, message }
      }
    }`,
    session,
    { input: inputFiles }
  );

  const fileUploadRequests = uploadCreateResponse?.stagedUploadsCreate.stagedTargets;

  if (!fileUploadRequests?.length || !fileUploadRequests.every((uploadRequest) => uploadRequest.url)) {
    captureMessage("Failed to create all URLs", shop, { fileUploadRequests });

    throw new Error(
      uploadCreateResponse?.stagedUploadsCreate?.userErrors?.find(({ message }) => message)?.message ||
        "Failed to create all URLs"
    );
  }

  return /** @type {ImageUploadCreateUrlsResponse['data']} */ (fileUploadRequests);
}

/**
 * @param {FileUploadData[]} files
 * @param {'IMAGE' | 'FILE'} contentType
 * @param {import('@shopify/shopify-api').Session} session
 * @param {boolean} [isFullResolution]
 */
export async function createFiles(files, contentType, session, isFullResolution = false) {
  const { shop } = session;

  if (
    files.some(
      ({ filename, resourceUrl, altText }) =>
        typeof filename !== "string" ||
        !filename ||
        typeof resourceUrl !== "string" ||
        !resourceUrl ||
        (typeof altText !== "string" && altText)
    )
  ) {
    captureMessage("Filename or resource URL is not given.", shop, { files });
    throw new Error("Bad upload data");
  }

  const inputFiles = files.map(({ resourceUrl, filename, altText }) => ({
    originalSource: resourceUrl,
    filename,
    contentType,
    alt: altText || "",
  }));

  /**
   * @type {{
   *   fileCreate: {
   *     files?: ({
   *       id: string,
   *       image?: { url: string },
   *       url?: string,
   *       mediaErrors?: { message: string, details?: string }[],
   *       fileErrors?: { message: string, details?: string }[]
   *     })[]
   *   }
   * } | undefined}
   */
  const fileCreateResponse = await sendQuery(
    `mutation FileCreateMutation($files: [FileCreateInput!]!) {
      fileCreate(files: $files) {
        files {
          ... on MediaImage {
            id
            image { url${isFullResolution ? "" : "(transform: { maxWidth: 256, maxHeight: 256 })"} }
            mediaErrors { message, details }
          }
          ... on GenericFile {
            id
            url
            fileErrors { message, details }
          }
        }
        userErrors { field, message }
      }
    }`,
    session,
    { files: inputFiles }
  );

  let uploadedFiles = fileCreateResponse?.fileCreate.files;
  let retryCount = 0;

  // eslint-disable-next-line no-constant-condition
  while (true) {
    if (uploadedFiles?.some((file) => file.mediaErrors?.length || file.fileErrors?.length)) {
      captureMessage("Media errors in file create response", shop, { uploadedFiles });

      throw new Error(
        uploadedFiles?.find((file) => file.mediaErrors?.length)?.mediaErrors?.[0]?.message ||
          uploadedFiles?.find((file) => file.fileErrors?.length)?.fileErrors?.[0]?.message ||
          "Failed to create all files"
      );
    }

    const processingFiles = uploadedFiles?.filter((file) => !file.image && !file.url);
    if (!processingFiles?.length) {
      break;
    }

    /* eslint-disable-next-line no-await-in-loop */
    await new Promise((resolve) => {
      setTimeout(resolve, 500);
    });

    if (retryCount > 9) {
      captureMessage("Timeout exceeded while waiting for files to be processed", shop, {
        processingFiles,
      });

      throw new Error("File processing timeout exceeded");
    }

    retryCount++;

    /**
     * @type {{
     *   nodes: {
     *     id: string,
     *     image?: { url: string },
     *     url?: string,
     *     mediaErrors?: { message: string, details?: string }[],
     *     fileErrors?: { message: string, details?: string }[],
     *   }[]
     * } | undefined}
     */
    // eslint-disable-next-line no-await-in-loop
    const updatedFiles = await sendQuery(
      `{
        nodes(ids: ${JSON.stringify(processingFiles.map((file) => file.id))}) { 
          ... on MediaImage {
            id
            image { url(transform: { maxWidth: 256, maxHeight: 256 }) }
            mediaErrors { message, details }
          }
          ... on GenericFile {
            id
            url
            fileErrors { message, details }
          }
        }
      }`,
      session
    );

    uploadedFiles = uploadedFiles?.map((file) => {
      const updatedFile = updatedFiles?.nodes.find((newFile) => newFile.id === file.id);
      return updatedFile || file;
    });
  }

  return uploadedFiles?.flatMap(({ id, url, image }) => {
    const fileUrl = image?.url || url;
    return fileUrl && id ? { fileId: id, url: fileUrl } : [];
  });
}

/**
 * @param {import('@shopify/shopify-api').Session | undefined} session
 */
export async function getShopDetailsFromShopify(session) {
  if (!session) return {};
  const shopifyShopResult = await sendQuery(shopQuery, session);
  const shopifyShop = get(shopifyShopResult, "shop");
  return {
    globalId: get(shopifyShop, "id"),
    name: get(shopifyShop, "name"),
    email: get(shopifyShop, "email"),
    domain: get(shopifyShop, "myshopifyDomain"),
    url: get(shopifyShop, "url"),
    phone: get(shopifyShop, "billingAddress.phone"),
    isPartnerDevelopment: get(shopifyShop, "plan.partnerDevelopment"),
    shopifyPlan: get(shopifyShop, "plan"),
    currencyCode: get(shopifyShop, "currencyCode"),
    currencyFormats: get(shopifyShop, "currencyFormats"),
    billingAddress: get(shopifyShop, "billingAddress"),
  };
}

/**
 * @param {import('@shopify/shopify-api').Session | undefined} session
 */
export async function getAppHandle(session) {
  if (!session) return {};
  // eslint-disable-next-line no-useless-catch
  try {
    const {
      app: { handle },
    } = await sendQuery(appQuery, session);
    return handle;
  } catch (error) {
    throw error;
  }
}

/**
 * @param {import('@shopify/shopify-api').Session | undefined} session
 */
export async function getAppURL(session) {
  if (!session) return {};
  // eslint-disable-next-line no-useless-catch
  try {
    const {
      app: {
        installation: { launchUrl },
      },
    } = await sendQuery(appQuery, session);
    return launchUrl;
  } catch (error) {
    throw error;
  }
}
/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {string} query
 * @param {object | undefined} variables
 * @returns
 */
export const shopifyGraphQLClient = async (session, query, variables = undefined) => {
  try {
    const client = new shopify.api.clients.Graphql({ session });
    const { data } = await client.request(query, { variables });
    return data;
  } catch (error) {
    captureException(error, session.shop);
    throw error;
  }
};

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {IShop | undefined} shop
 * @param {import("../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @param {import("../types/SubscriptionPlanCoupon.js").ISubscriptionPlanCoupon | null | undefined} coupon
 * @returns
 */
export const appSubscriptionCreate = async (session, shop, plan, coupon) => {
  const url = await getAppURL(session);
  const variables = generateSubscriptionVariables(shop, plan, url, coupon);

  const {
    appSubscriptionCreate: { appSubscription, confirmationUrl, userErrors },
  } = await shopifyGraphQLClient(session, appSubscriptionCreateMutation, variables);

  if (userErrors.length > 0) {
    throw new Error(userErrors[0].message);
  }

  return { appSubscription, confirmationUrl };
};

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {*} data
 * @returns
 */
export const createUsageCharge = async (session, data) => {
  const variables = generateAppUsageRecordCreateVariables(data);
  const {
    appUsageRecordCreate: { appUsageRecord, userErrors },
  } = await shopifyGraphQLClient(session, appUsageRecordCreate, variables);

  if (userErrors.length > 0) {
    throw new Error(userErrors[0].message);
  }

  return appUsageRecord;
};

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {string} subscriptionId
 * @param {number} extendDays
 * @returns
 */
export const extendsAppSubscriptionTrail = async (session, subscriptionId, extendDays) => {
  const variables = {
    id: subscriptionId,
    days: extendDays,
  };
  const {
    appSubscriptionTrialExtend: { appSubscription, userErrors },
  } = await shopifyGraphQLClient(session, appSubscriptionTrialExtendMutation, variables);

  if (userErrors.length > 0) {
    throw new Error(userErrors[0].message);
  }

  return appSubscription;
};
