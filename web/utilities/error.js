import trackEvent from './analytics.js';
import { setShopProperties } from './datastore.js';

export const criticalErrorTypes = /** @type {const} */ ([
  'bad-invoice-prefix',
  'e-invoice-disabled',
]);

/**
 * @param {ErrorType | undefined} errorType
 */
export function getIsCriticalError(errorType) {
  return (
    !!errorType && /** @type {readonly ErrorType[]} */ (criticalErrorTypes).includes(errorType)
  );
}

/**
 * @param {string} errorMessage
 * @param {Exclude<IShop['lastError'], undefined> & { isCritical?: never }} error
 * @param {IShop | undefined} shopProperties
 * @param {string} shop
 * @param {any} [debugInformation]
 * @param {boolean} [isSave]
 *
 * @returns {Promise<IShop['lastError']>}
 */
export async function handleError(
  errorMessage,
  error,
  shopProperties,
  shop,
  debugInformation = null,
  isSave = true,
) {
  const isCritical = getIsCriticalError(error.type);
  const errorResponse = { ...error, ...(isCritical ? { isCritical: true } : {}) };

  if (isSave) {
    trackEvent(errorMessage, shop, {
      reason: error.type,
      value: error.displayValue,
      ...(debugInformation ? { debugInformation } : {}),
      ...(error.orderId ? { orderId: error.orderId } : {}),
      ...(error.orderName ? { orderName: error.orderName } : {}),
    });

    if (isCritical) {
      await setShopProperties({ lastError: errorResponse }, shopProperties, shop);
    }
  }

  return errorResponse;
}
