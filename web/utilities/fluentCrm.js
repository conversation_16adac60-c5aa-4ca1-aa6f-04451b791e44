import lodash from 'lodash';
import { captureException, captureMessage } from './analytics.js';
import { billingConfig } from './shopify.js';

const { get } = lodash;

export const FLUENT_CRM_CONFIGS = {
  userName: process.env.FLUENT_CRM_API_USER || 'Mamun',
  password: process.env.FLUENT_CRM_API_PASSWORD || 'm46H HLHN dHO8 FwWr kx5X nDBm',
  baseURL: process.env.FLUENT_CRM_API_BASE_URL || 'https://easy-flow.app/wp-json/fluent-crm/v2',
  isEnabled: process.env.FLUENT_CRM_ENABLE === 'true',
};

export const FLUENT_CRM_TAGS = {
  INSTALLED: 'installed',
  UNINSTALLED: 'uninstalled',
  SUBSCRIPTION_UPDATE: 'subscription_update',
};

const FLUENT_CRM_STATUS = {
  SUBSCRIBED: 'subscribed',
  UNSUBSCRIBED: 'unsubscribed',
  PENDING: 'pending',
  BOUNCED: 'bounced',
  COMPLAINED: 'complained',
};

const authToken = Buffer.from(
  `${FLUENT_CRM_CONFIGS.userName}:${FLUENT_CRM_CONFIGS.password}`,
).toString('base64');

/**
 * @typedef {Object} ContactData {
 *  __force_update: string,
 * email: string,
 * status: string,
 * first_name?: string,
 * phone?: string,
 * address_line_1?: string,
 * address_line_2?: string,
 * city?: string,
 * state?: string,
 * country?: string,
 * custom_values?: {
 *  shop_name: string,
 * shop_email: string,
 * shop_domain: string,
 * }
 */

/**
 * Formats the contact data for FluentCRM.
 * @param {Object} data - The contact data to format.
 * @param {string} tagKey - The tag key for FluentCRM.
 * @param {object} [optionalData] - Optional additional data.
 */
const contractDataFormater = (data, tagKey, optionalData = {}) => {
  const plan = Object.keys(billingConfig);
  /**
   * @type {ContactData}
   */
  let finalData = {
    __force_update: 'yes',
    email: get(data, 'email', ''),
    status: FLUENT_CRM_STATUS.SUBSCRIBED,
  };

  /**
   * @type {string[]}
   */
  let finalTags = [];
  /**
   * @type {string[]}
   */
  let finalDetatchTags = [];
  const subscriptionStatus = ['ACTIVE', 'FROZEN', 'CANCELLED', 'EXPIRED'];

  if (tagKey === FLUENT_CRM_TAGS.INSTALLED) {
    finalData = {
      ...finalData,
      first_name: get(data, 'name'),
      phone: get(data, 'phone'),
      address_line_1: get(data, 'billingAddress.address1'),
      address_line_2: get(data, 'billingAddress.address2'),
      city: get(data, 'billingAddress.city'),
      state: get(data, 'billingAddress.province'),
      country: get(data, 'billingAddress.country'),
      custom_values: {
        shop_name: get(data, 'name'),
        shop_email: get(data, 'email'),
        shop_domain: get(data, 'domain'),
      },
    };
    finalTags = [...finalTags, FLUENT_CRM_TAGS.INSTALLED.toUpperCase()];
    finalDetatchTags = [...finalDetatchTags, FLUENT_CRM_TAGS.UNINSTALLED];
  } else if (tagKey === FLUENT_CRM_TAGS.UNINSTALLED) {
    finalTags = [...finalTags, FLUENT_CRM_TAGS.UNINSTALLED.toUpperCase()];
    finalDetatchTags = [
      ...finalDetatchTags,
      FLUENT_CRM_TAGS.INSTALLED,
      plan[0],
      ...subscriptionStatus.map((status) => `subscription_${status}`.toUpperCase()),
    ];
  } else if (tagKey === FLUENT_CRM_TAGS.SUBSCRIPTION_UPDATE) {
    if (get(optionalData, 'status') === 'ACTIVE') {
      finalTags = [
        ...finalTags,
        plan[0].toUpperCase(),
        ...subscriptionStatus
          .filter((status) => status === 'ACTIVE')
          .map((status) => `subscription_${status}`.toUpperCase()),
      ];
      finalDetatchTags = [
        ...finalDetatchTags,
        FLUENT_CRM_TAGS.UNINSTALLED.toUpperCase(),
        ...subscriptionStatus
          .filter((status) => status !== 'ACTIVE')
          .map((status) => `subscription_${status}`.toUpperCase()),
      ];
    } else if (get(optionalData, 'status') === 'FROZEN') {
      finalTags = [
        ...finalTags,
        ...subscriptionStatus
          .filter((status) => status === 'FROZEN')
          .map((status) => `subscription_${status}`.toUpperCase()),
      ];
      finalDetatchTags = [
        ...finalDetatchTags,
        FLUENT_CRM_TAGS.UNINSTALLED.toUpperCase(),
        ...subscriptionStatus
          .filter((status) => status !== 'FROZEN')
          .map((status) => `subscription_${status}`.toUpperCase()),
      ];
    }
  }
  finalData = {
    ...finalData,
    tags: [...finalTags],
    detach_tags: [...finalDetatchTags],
  };
  return finalData;
};

/**
 * Utility function to create or update a contact in FluentCRM with tags.
 * @param {Object} contactData - The contact data to send.
 */
/**
 * Utility function to create or update a contact in FluentCRM with tags.
 * @param {Object} contactData - The contact data to send.
 * @param {string} fluentCrmTagKey - fluentCRM tag key.
 * @param {object} [optionalData] - Optional additional data.
 */
export const createOrUpdateFluentCRMContact = async (
  contactData,
  fluentCrmTagKey,
  optionalData = {},
) => {
  try {
    const payload = contractDataFormater(contactData, fluentCrmTagKey, optionalData);
    const response = await fetch(`${FLUENT_CRM_CONFIGS.baseURL}/subscribers`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', Authorization: `Basic ${authToken}` },
      body: JSON.stringify(payload),
    });
    // Check if the response is okay
    if (!response.ok) {
      captureMessage(
        'Error creating or updating contact',
        get(contactData, 'domain', ''),
        contactData,
      );
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    // Read the response body only once
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating or updating contact:', error.response?.data || error.message);
    captureException(error, get(contactData, 'domain', ''));
    return null;
  }
};

/**
 *
 * @param {string} email
 */
const getFluentCRMContact = async (email) => {
  const response = await fetch(
    `${FLUENT_CRM_CONFIGS.baseURL}/subscribers/0?get_by_email=${email}&with[]=subscriber.custom_values`,
    {
      method: 'GET',
      headers: { Authorization: `Basic ${authToken}` },
    },
  );
  const data = await response.json();
  // @ts-ignore
  const subscriber = data.subscriber || {};
  return subscriber;
};
