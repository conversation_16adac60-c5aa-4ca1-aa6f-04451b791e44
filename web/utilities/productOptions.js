import { PropertyFilter } from "@google-cloud/datastore";
import _ from "lodash";

import productCreateMutation from "../mutations/productCreate.js";
import productUpdateMutation from "../mutations/productUpdate.js";
import productVariantsBulkUpdateMutation from "../mutations/productVariantsBulkUpdate.js";
import { captureMessage } from "./analytics.js";
import datastore, { optionFromStored, orderedGet, runUnlimitedInQuery } from "./datastore.js";
import {
  addonProductType,
  getShopId,
  metafieldNamespace,
  metaobjectType,
  optionSetsMetafieldKey,
  optionsMetafieldKey,
  ranksMetafieldKey,
  rulesMetafieldKey,
  sendQuery,
} from "./shopify.js";

/**
 * @typedef {{
 *   id: string,
 *   ruleSet?: {
 *     appliedDisjunctively: boolean,
 *     rules: {
 *       column: string,
 *       condition: string,
 *       conditionObject?: { metafieldDefinition?: { id: string } },
 *       relation: string,
 *     }[]
 *   }
 * }} Collection
 */

/**
 * @template {IProductOption} ProductOptionType
 *
 * @param {ProductOptionType[]} options
 * @param {CollectedProjection<IProductOption, 'metaobjectId'>[]} storedOptions
 * @param {import('@google-cloud/datastore').Key[]} allocatedIds
 * @param {import("@shopify/shopify-api").Session} session
 *
 * @returns {Promise<
 *   (ProductOptionType & Pick<Stored<IProductOption>, 'metaobjectId' | 'productIds'>)[]
 * >}
 */
export async function updateMetaobjects(options, storedOptions, allocatedIds, session) {
  if (!options.length) {
    return [];
  }

  let usedAllocatedIds = 0;

  // Add the allocated IDs and metaobject IDs to the options
  const optionsWithStoredFields = options.map((option) => {
    let optionId = option.id;

    /** @type {CollectedProjection<IProductOption, "metaobjectId"> | undefined} */
    let storedOption;

    if (!optionId) {
      optionId = Number(allocatedIds[usedAllocatedIds].id);
      usedAllocatedIds++;
    } else {
      storedOption = storedOptions.find((newOption) => Number(newOption[datastore.KEY]?.id) === optionId);
    }

    return {
      ...option,
      id: optionId,
      ...(storedOption ? { metaobjectId: storedOption.metaobjectId } : {}),
    };
  });

  // Create or update all metaobjects corresponding to updated options
  const optionsWithMetaobjectId = await Promise.all(
    optionsWithStoredFields.map(async (option) => {
      const actionType = !option.metaobjectId ? "Create" : "Update";

      const metaobject = {
        ...(actionType === "Update" ? {} : { type: metaobjectType }),
        fields: /** @type {const} */ ([
          "id",
          "title",
          "type",
          "placeholderText",
          "description",
          "isRequired",
          "isMultiSelect",
          "values",
          "inCartName",
          "minimumSelectionCount",
          "maximumSelectionCount",
          "isShowSelectedValues",
          "minimumValue",
          "maximumValue",
          "allowedFileTypes",
        ]).flatMap((propertyName) => {
          let value = option[propertyName];
          value = value !== undefined && value !== null ? value : "";

          return {
            // The field `id` is reserved by Shopify
            key: propertyName === "id" ? "optionId" : propertyName,
            value: typeof value !== "string" ? JSON.stringify(value) : value,
          };
        }),
      };

      /** @type {{ metaobject?: { id: string } } | undefined} */
      const optionDefinitionUpdateResult = (
        await sendQuery(
          `mutation Metaobject${actionType}Mutation(
            ${actionType === "Update" ? "$id: ID!," : ""}
            $metaobject: ${actionType === "Update" ? "MetaobjectUpdateInput" : "MetaobjectCreateInput"}!
          ) {
            metaobject${actionType}(${actionType === "Update" ? "id: $id, " : ""}metaobject: $metaobject) {
              metaobject { id }
              userErrors { field, message }
            }
          }`,
          session,
          { id: option.metaobjectId, metaobject }
        )
      )?.[`metaobject${actionType}`];

      const metaobjectId = optionDefinitionUpdateResult?.metaobject?.id;

      if (!metaobjectId) {
        throw new Error("No metaobject ID after saving metaobject.");
      }

      return { ...option, metaobjectId };
    })
  );

  return optionsWithMetaobjectId;
}

/**
 * @param {string[]} changedProductIds
 * @param {{
 *   update: {
 *     optionSets: IProductOptionSet[],
 *     options: (IProductOption & Pick<Stored<IProductOption>, 'metaobjectId'>)[]
 *   }
 * } | { delete: { optionSetIds: number[], optionIds: number[] } }} changedOptionsAndSets
 * @param {import("@shopify/shopify-api").Session} session
 */
export async function updateMetafields(changedProductIds, changedOptionsAndSets, session) {
  if (!changedProductIds.length) {
    return;
  }

  const { shop } = session;

  // Get all the options and option sets for the changed product IDs, then recalculate the options
  // for each product.

  /** @type {[number[], (IProductOptionSet | DatastoreEntity<IProductOptionSet>)[]]} */
  const [optionIds, changedProductsOptionSets] = await Promise.all([
    // Get option IDs for the changed product IDs
    (async () => {
      /** @type {CollectedProjection<IProductOption, never>[]} */
      const optionKeyResults = await runUnlimitedInQuery(
        "Option",
        changedProductIds,
        (query, chunk) =>
          query
            .hasAncestor(datastore.key(["Shop", shop]))
            .filter(new PropertyFilter("productIds", "IN", chunk))
            .select("__key__"),
        []
      );

      // Remove changed options from the queried IDs, since they have not been saved yet
      return optionKeyResults.map((option) => Number(option[datastore.KEY]?.id));
    })(),

    // Get option sets for the changed product IDs
    (async () => {
      /** @type {DatastoreEntity<IProductOptionSet>[]} */
      const optionSetResults = await runUnlimitedInQuery(
        "Option set",
        changedProductIds,
        (query, chunk) =>
          query.hasAncestor(datastore.key(["Shop", shop])).filter(new PropertyFilter("productIds", "IN", chunk)),
        null
      );

      // Update option sets and their options with the updated/deleted option sets and options
      const changedOptionSetIds =
        "update" in changedOptionsAndSets
          ? changedOptionsAndSets.update.optionSets.map((optionSet) => optionSet.id)
          : changedOptionsAndSets.delete.optionSetIds;

      const deletedOptionIds = "delete" in changedOptionsAndSets ? changedOptionsAndSets.delete.optionIds : [];

      return [
        ...("update" in changedOptionsAndSets ? changedOptionsAndSets.update.optionSets : []),
        ...optionSetResults.flatMap((optionSet) =>
          !changedOptionSetIds.includes(Number(optionSet[datastore.KEY]?.id))
            ? {
                ...optionSet,
                optionIds: optionSet.optionIds.filter((optionId) => !deletedOptionIds.includes(optionId)),
              }
            : []
        ),
        // Don't add global options to products
      ].filter((optionSet) => !optionSet.productSelectionMethod || optionSet.productSelectionMethod === "manual");
    })(),
  ]);

  // Get `metaobjectId`s and `productIds` for the options
  const changedOptionIds =
    "update" in changedOptionsAndSets
      ? changedOptionsAndSets.update.options.map((option) => option.id)
      : changedOptionsAndSets.delete.optionIds;

  const optionKeys = [
    ...new Set([...changedProductsOptionSets.flatMap((optionSet) => optionSet.optionIds), ...optionIds]),
  ].flatMap((id) =>
    // Remove changed options from the query keys, since they have not been saved yet
    !changedOptionIds.includes(id) ? datastore.key(["Shop", shop, "Option", id]) : []
  );

  /**
   * @type {(DatastoreEntity<IProductOption>
   *   | (IProductOption & Pick<Stored<IProductOption>, "metaobjectId">))[]}
   */
  const optionResults = [
    ...(optionKeys.length ? await orderedGet(optionKeys) : []),
    // Add changed options to the query results, since they have not been saved yet
    ...("update" in changedOptionsAndSets ? changedOptionsAndSets.update.options : []),
  ];

  const changedOptionSetsWithOptions = changedProductsOptionSets.map((optionSet) => ({
    ...optionSet,
    options: optionSet.optionIds.flatMap((optionId) => {
      const optionSetOption = optionResults.find(
        (option) => ("id" in option ? option.id : Number(option[datastore.KEY]?.id)) === optionId
      );

      return optionSetOption ? { ...optionSetOption, productIds: optionSet.productIds } : [];
    }),
  }));

  const changedProductsOptions = [
    ...optionResults,
    ...changedOptionSetsWithOptions.flatMap((optionSet) => optionSet.options),
  ];

  // Calculate the options and rules for each product

  /**
   * @type {{
   *   [productId: string]: {
   *     options: (IProductOption & Pick<Stored<IProductOption>, 'metaobjectId'>
   *       | DatastoreEntity<IProductOption>)[],
   *     rules?: IProductOptionSet['rules'],
   *     ranks?: Record<string, number>,
   *   }
   * }}
   */
  const optionDataByProduct = {};

  changedProductsOptions.forEach((option) =>
    option.productIds.forEach((productId) => {
      optionDataByProduct[productId] = {
        options: [...(optionDataByProduct[productId]?.options || []), option],
      };
    })
  );

  changedOptionSetsWithOptions.forEach((optionSet) => {
    optionSet.productIds.forEach((productId) => {
      optionDataByProduct[productId] = {
        ...(optionDataByProduct[productId] || {}),
        rules: [...(optionDataByProduct[productId]?.rules || []), ...(optionSet?.rules || [])],
        ranks: {
          ...optionDataByProduct[productId]?.ranks,
          ...Object.fromEntries(
            optionSet.options.flatMap((option) =>
              optionSet.rank ? [["id" in option ? option.id : Number(option[datastore.KEY]?.id), optionSet.rank]] : []
            )
          ),
        },
      };
    });
  });

  // Ignore non-changed products (by mapping to `changedProductIds`) and deduplicate options
  const productOptionData = changedProductIds.map((productId) => {
    const optionData = optionDataByProduct[productId];

    return /** @type {const} */ ([
      productId,
      {
        ...optionData,
        options: optionData?.options
          ? optionData.options.filter(
              (option, index, self) =>
                index === self.findIndex((previousOption) => previousOption.metaobjectId === option.metaobjectId)
            )
          : [],
      },
    ]);
  });

  // Set the calculated metafields on the product
  if (productOptionData.length) {
    const chunkedProductOptionData = _.chunk(productOptionData, 30);
    // eslint-disable-next-line no-restricted-syntax
    for (const chunkProductOptionData of chunkedProductOptionData) {
      // eslint-disable-next-line no-await-in-loop
      await Promise.all(
        chunkProductOptionData.map(async ([productId, optionData]) => {
          // Calculate which values are hidden initially
          // ! Duplicated on the client side (slightly modified) !
          // ! Any changes made here should be made there too !
          const extendedRules = optionData.rules?.map((rule) => {
            // Check if current rule matches based on selected values
            const isMatches = rule.conditions[rule.isAllConditionsRequired ? "every" : "some"]((condition) => {
              const defaultValues = optionData.options
                .find(
                  (option) => ("id" in option ? option.id : Number(option[datastore.KEY]?.id)) === condition.optionId
                )
                ?.values.flatMap((value) => (value.isDefault ? value.title || [] : []));

              // Single value conditions
              if (condition.values.includes("__any__")) {
                const isNotEmpty = defaultValues?.length;
                return condition.relation === "in" ? isNotEmpty : !isNotEmpty;
              }

              return defaultValues?.some((value) => {
                const isIncludes = condition.values.includes(value);
                return condition.relation === "in" ? isIncludes : !isIncludes;
              });
            });

            // Go over each action and calculate for all values if they are hidden or not
            const extendedActions = rule.actions.map((action) => {
              const actionOption = optionData.options.find(
                (option) => ("id" in option ? option.id : Number(option[datastore.KEY]?.id)) === action.optionId
              );

              /** @type {string[] | true} */
              let hiddenValues =
                action.values === "All"
                  ? actionOption?.values.flatMap((value) => value.title || []) || []
                  : action.values;

              hiddenValues = !actionOption?.values.some((value) => value.title) ? true : hiddenValues;
              hiddenValues =
                (action.type === "show" && !isMatches) || (action.type === "hide" && isMatches) ? hiddenValues : [];

              return { ...action, initiallyHiddenValues: hiddenValues };
            });

            return { ...rule, actions: extendedActions };
          });

          const metafields = [
            {
              namespace: metafieldNamespace,
              key: optionsMetafieldKey,
              type: "list.metaobject_reference",
              ownerId: productId,
              value: JSON.stringify(optionData.options.map(({ metaobjectId }) => metaobjectId)),
            },
            {
              namespace: metafieldNamespace,
              key: ranksMetafieldKey,
              type: "json",
              ownerId: productId,
              value: JSON.stringify(optionData.ranks || {}),
            },
            {
              namespace: metafieldNamespace,
              key: rulesMetafieldKey,
              type: "json",
              ownerId: productId,
              value: JSON.stringify(extendedRules || []),
            },
          ];

          // console.log(metafields);
          // console.log(JSON.stringify(optionData.options.map(({ metaobjectId }) => metaobjectId)));

          await sendQuery(
            `mutation MetafieldsSetMutation($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              userErrors { field, message }
            }
          }`,
            session,
            { metafields }
          );
        }) || []
      );
      // eslint-disable-next-line no-await-in-loop
      await delay(1000);
    }
  }
}

const delay = (/** @type {number | undefined} */ ms) =>
  new Promise((resolve) => {
    setTimeout(resolve, ms);
  });

/**
 * @param {{
 *   update: {
 *     optionSets: IProductOptionSet[],
 *     options: (IProductOption & Pick<Stored<IProductOption>, 'metaobjectId'>)[]
 *   }
 * } | { delete: { optionSetIds: number[], optionIds: number[] } }} changedOptionsAndSets
 *
 * @param {import("@shopify/shopify-api").Session} session
 */
export async function updateGlobalOptionsMetafield(changedOptionsAndSets, session) {
  /** @type {(IProductOptionSet | DatastoreEntity<IProductOptionSet>)[]} */
  let globalOptionSets = "update" in changedOptionsAndSets ? changedOptionsAndSets.update.optionSets : [];

  // Get all global options from the datastore

  /** @type {[DatastoreEntity<IProductOption>[], string | undefined]} */
  const [storedOptions, shopId] = await Promise.all([
    (async () => {
      /** @type {QueryResponse<IProductOptionSet>} */
      const [storedOptionSets] = await datastore
        .createQuery("Option set")
        .hasAncestor(datastore.key(["Shop", session.shop]))
        .filter(new PropertyFilter("productSelectionMethod", "!=", "manual"))
        .run();

      // Remove changed option sets from the queried IDs, since they have not been saved yet
      const changedOptionSetIds =
        "update" in changedOptionsAndSets
          ? changedOptionsAndSets.update.optionSets.map((optionSet) => optionSet.id)
          : changedOptionsAndSets.delete.optionSetIds;

      globalOptionSets = [
        ...globalOptionSets,
        ...storedOptionSets.filter((optionSet) => !changedOptionSetIds.includes(Number(optionSet[datastore.KEY]?.id))),
      ].filter(
        // Deduplicate option sets
        (optionSet, index, self) =>
          index ===
          self.findIndex((previousOptionSet) => {
            const previousOptionSetId =
              "id" in previousOptionSet ? previousOptionSet.id : Number(previousOptionSet[datastore.KEY]?.id);

            const optionSetId = "id" in optionSet ? optionSet.id : Number(optionSet[datastore.KEY]?.id);

            return previousOptionSetId === optionSetId;
          })
      );

      const changedOptionIds =
        "update" in changedOptionsAndSets
          ? changedOptionsAndSets.update.options.map((option) => option.id)
          : changedOptionsAndSets.delete.optionIds;

      const globalOptionIds = [
        ...new Set(
          globalOptionSets
            .flatMap((optionSet) => optionSet.optionIds)
            .filter((optionId) => !changedOptionIds.includes(optionId))
        ),
      ];

      return globalOptionIds.length
        ? orderedGet(globalOptionIds.map((optionId) => datastore.key(["Shop", session.shop, "Option", optionId])))
        : [];
    })(),
    getShopId(session),
  ]);

  if (!shopId) {
    captureMessage("Couldn't get shop ID.", session.shop);
    return;
  }

  const globalOptions = [
    ...storedOptions,
    ...("update" in changedOptionsAndSets ? changedOptionsAndSets.update.options : []),
  ];

  // Map the retrieved global options to the global options sets
  const optionSetsWithOptions = globalOptionSets.map(
    ({ optionIds, productSelectionMethod, productSelectionValues, rules, rank }) => {
      const options = optionIds.flatMap((optionId) => {
        const globalOption = globalOptions.find(
          (option) => ("id" in option ? option.id : Number(option[datastore.KEY]?.id)) === optionId
        );

        // Change option from stored to the client-side format

        /** @type {Omit<IProductOption, 'id'> & { id?: number } | undefined} */
        const clientOption =
          globalOption && !("id" in globalOption)
            ? optionFromStored(globalOption, session.shop)
            : globalOption && { ...globalOption };

        // Change `id` to `optionId` to be consistent with the product metafield type
        delete clientOption?.id;

        return clientOption ? { ...clientOption, optionId } : [];
      });

      return { productSelectionMethod, productSelectionValues, options, rules, rank };
    }
  );

  // Store the global option sets in the shop's metafield
  const metafields = [
    {
      namespace: metafieldNamespace,
      key: optionSetsMetafieldKey,
      type: "json",
      ownerId: shopId,
      value: JSON.stringify(optionSetsWithOptions),
    },
  ];

  await sendQuery(
    `mutation MetafieldsSetMutation($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        userErrors { field, message }
      }
    }`,
    session,
    { metafields }
  );
}

/**
 * @param {IProductOption['values'][number]} value
 * @param {IProductOption} option
 * @param {import('@shopify/shopify-api').Session} session
 * @param {number} [recursionDepth]
 */
async function updateProduct(value, option, session, recursionDepth = 0) {
  const { addonProduct: { id: productId } = {}, price } = value;
  const title = !option.values.some((currentValue) => currentValue.title) ? option.title : value.title;

  const actionName = !productId ? "Create" : "Update";
  let productResult;
  let mutationQuery;
  let productInputVariables = {
    product: {
      title: `${JSON.stringify(title)}`,
      productType: `${addonProductType}`,
    },
  };

  // At the time of writing this, the `productSet` mutation requires an option to be created,
  // which shows up on the addon product in the cart, so we need to use the
  // `product[Create/Update]` mutation.
  if (actionName === "Create") {
    productInputVariables = {
      ...productInputVariables,
      product: {
        ...productInputVariables.product,
        // @ts-ignore
        metafields: [
          {
            namespace: "seo",
            key: "hidden",
            type: "number_integer",
            value: "1",
          },
        ],
      },
    };
    mutationQuery = productCreateMutation;
  } else {
    productInputVariables = {
      ...productInputVariables,
      product: {
        ...productInputVariables.product,
        // @ts-ignore
        id: `${productId}`,
      },
    };
    mutationQuery = productUpdateMutation;
  }

  /**
   * @type {{
   *   product?: { id: string, handle: string, variants: { nodes: { id: string }[] } },
   *   userErrors: UserErrors,
   * } | undefined}
   */
  productResult = await sendQuery(
    mutationQuery,
    session,
    productInputVariables,
    (error) =>
      (error.field?.[0] !== "metafields" || error.field?.[2] !== "key") &&
      (error.field?.[0] !== "handle" || recursionDepth > 4)
  );

  productResult = _.get(productResult, `product${actionName}`);

  // If multiple products with the same title is saved we get a "handle already taken" error,
  // so we need to try again.
  if (productResult?.userErrors.some((error) => error.field?.[0] === "handle")) {
    if (recursionDepth > 4) {
      return {};
    }

    return updateProduct(value, option, session, recursionDepth + 1);
  }

  const product = productResult?.product;
  const productVariant = product?.variants.nodes[0];

  // The `productCreate/Update` mutation doesn't support updating the product variant,
  // so we need to do that separately.
  if (productVariant) {
    const productVariantsBulkUpdateVariables = {
      productId: product?.id,
      variants: [
        {
          id: productVariant?.id,
          price,
        },
      ],
    };
    await sendQuery(productVariantsBulkUpdateMutation, session, productVariantsBulkUpdateVariables);
  }

  return { product, productVariant };
}

/**
 * @param {IProductOption[]} options
 * @param {DatastoreEntity<IProductOption>[]} storedOptions
 * @param {import("@shopify/shopify-api").Session} session
 *
 * @return {Promise<IProductOption[]>}
 */
export async function updateAddonProducts(options, storedOptions, session) {
  /** @type {string[]} */
  let channelIds = [];

  if (options.some((option) => option.values.some((value) => value.price && !value.addonProduct))) {
    /** @type {{ publications: { nodes: { id: string }[] } } | undefined} */
    const publicationsResults = await sendQuery(`{ publications(first: 20) { nodes { id } } }`, session);

    channelIds = publicationsResults?.publications.nodes.map(({ id }) => id) || [];
  }

  const deletedProductIds = [
    ...new Set(
      storedOptions.flatMap((storedOption) => {
        const unsavedOption = options.find((option) => option.id === Number(storedOption[datastore.KEY]?.id));

        if (!unsavedOption) {
          return [];
        }

        return storedOption.values.flatMap((storedValue) => {
          const unsavedValue = unsavedOption.values.find(
            ({ addonProduct }) => addonProduct && addonProduct.id === storedValue.addonProduct?.id
          );

          // Delete addon product if the value is deleted or the price has been unset
          return storedValue.addonType === "new" && !unsavedValue?.price ? storedValue.addonProduct?.id || [] : [];
        });
      })
    ),
  ];

  const [newOptions] = await Promise.all([
    Promise.all(
      options.map(async (option) => ({
        ...option,
        values: await Promise.all(
          option.values.map(async (value) => {
            // Remove addon product from the option if it's deleted
            if (value.addonProduct && deletedProductIds.includes(value.addonProduct.id)) {
              const { addonProduct, ...newValue } = value;
              return newValue;
            }

            // Don't create addon product if it's a linked product or if the price is not set
            if (value.addonType === "existing" || !value.price) {
              return value;
            }

            const { product, productVariant } = await updateProduct(value, option, session);

            const publicationInput = channelIds.map((channelId) => ({ publicationId: channelId }));

            if (product && productVariant && publicationInput.length) {
              await sendQuery(
                `mutation PublishablePublishMutation($input: [PublicationInput!]!) {
                  publishablePublish(id: "${product.id}", input: $input) {
                    userErrors { field, message }
                  }
                }`,
                session,
                { input: publicationInput }
              );
            }

            return product && productVariant
              ? {
                  ...value,
                  ...(productVariant && product.variants.nodes[0]
                    ? {
                        addonProduct: {
                          id: product.id,
                          variantId: productVariant.id,
                          handle: product.handle,
                        },
                      }
                    : {}),
                }
              : value;
          })
        ),
      }))
    ),
    deletedProductIds.map((id) =>
      sendQuery(
        `mutation ProductDeleteMutation {
          productDelete(input: { id: "${id}" }) {
            userErrors { field, message }
          }
        }`,
        session
      )
    ),
  ]);

  return newOptions;
}

/**
 * @param {'file' | 'product'} type
 * @param {DatastoreEntity<IProductOption>[]} options
 * @param {import("@shopify/shopify-api").Session} session
 *
 * @returns {Promise<string[]>}
 */
async function getDeletedIds(type, options, session) {
  const isFile = type === "file";
  const fieldName = isFile ? "values.image.fileId" : "values.addonProduct.id";

  const ids = options.flatMap((option) =>
    option.values.flatMap(({ image, addonProduct }) => (isFile ? image?.fileId : addonProduct?.id) || [])
  );

  // Cannot project `id`s if we query on it, so we need to fetch the keys first
  // See https://cloud.google.com/datastore/docs/concepts/queries?hl=en#limitations_on_projections

  /** @type {CollectedProjection<IProductOption, never>[]} */
  const inUseIdKeyResults = ids.length
    ? await runUnlimitedInQuery(
        "Option",
        ids,
        (query, chunk) =>
          query
            .hasAncestor(datastore.key(["Shop", session.shop]))
            .filter(new PropertyFilter(fieldName, "IN", chunk))
            .select("__key__"),
        []
      )
    : [];

  const inUseIdKeys = inUseIdKeyResults.flatMap((entity) =>
    !options.some((option) => option[datastore.KEY]?.id === entity[datastore.KEY]?.id)
      ? entity[datastore.KEY] || []
      : []
  );

  const inUseIds = inUseIdKeys.length
    ? await runUnlimitedInQuery(
        "Option",
        inUseIdKeys,
        (query, chunk) => query.filter(new PropertyFilter("__key__", "IN", chunk)).select(fieldName),
        [fieldName]
      )
    : [];

  const deletedIds = ids.filter((fileId) => !inUseIds.some((value) => value[fieldName].includes(fileId)));

  return deletedIds;
}

/**
 * @param {DatastoreEntity<IProductOption>[]} options
 * @param {import("@shopify/shopify-api").Session} session
 */
export async function deleteValueArtifacts(options, session) {
  const [deletedFileIds, deletedAddonProductIds] = await Promise.all([
    getDeletedIds("file", options, session),
    getDeletedIds("product", options, session),
  ]);

  await Promise.all([
    ...options.flatMap((option) =>
      option.values.map(({ addonProduct, addonType }) =>
        addonProduct && deletedAddonProductIds.includes(addonProduct.id) && addonType === "new"
          ? sendQuery(
              `mutation ProductDeleteMutation {
                productDelete(input: { id: "${addonProduct.id}" }) {
                  userErrors { field, message }
                }
              }`,
              session
            )
          : []
      )
    ),
    deletedFileIds.length
      ? sendQuery(
          `mutation FileDeleteMutation {
            fileDelete(fileIds: ${JSON.stringify(deletedFileIds)}) {
              userErrors { field, message }
            }
          }`,
          session
        )
      : [],
  ]);
}

/**
 * @param {import("@shopify/shopify-api").Session} session
 */
export async function updateCollections(session) {
  /** @type {{ collections: { nodes: Collection[] } } | undefined} */
  const collectionsResult = await sendQuery(
    `{
      collections(query: "-collection_type:custom", first: 20) {
        nodes {
          id,
          ruleSet {
            appliedDisjunctively,
            rules {
              column,
              condition,
              conditionObject {
                ... on CollectionRuleMetafieldCondition {
                  metafieldDefinition { id }
                }
              },
              relation
            }
          }
        }
      }
    }`,
    session
  );

  await setCollectionRules(collectionsResult?.collections.nodes || [], session);
}

/**
 * @param {Collection[]} collections
 * @param {import("@shopify/shopify-api").Session} session
 */
export async function setCollectionRules(collections, session) {
  if (!collections.length) {
    return;
  }

  await Promise.all(
    collections.map(async (collection) => {
      /** @type {Collection} */
      const collectionInput = {
        id: collection.id,
        ruleSet: {
          appliedDisjunctively: !!collection.ruleSet?.appliedDisjunctively,
          rules: [
            ...(collection.ruleSet?.rules?.flatMap((rule) => {
              const conditionObjectId = rule.conditionObject?.metafieldDefinition?.id;
              const newRule =
                rule.column !== "TYPE" || rule.relation !== "NOT_EQUALS" || rule.condition !== addonProductType
                  ? { ...rule, ...(conditionObjectId ? { conditionObjectId } : {}) }
                  : undefined;

              delete newRule?.conditionObject;

              return newRule || [];
            }) || []),
            // We only want to add this to 'all conditions'
            ...(!collection.ruleSet?.appliedDisjunctively
              ? [{ column: "TYPE", relation: "NOT_EQUALS", condition: addonProductType }]
              : []),
          ],
        },
      };

      await sendQuery(
        `mutation CollectionUpdateMutation($input: CollectionInput!) {
          collectionUpdate(input: $input) {
            userErrors { field, message }
          }
        }`,
        session,
        { input: collectionInput }
      );
    })
  );
}
