import * as Sentry from "@sentry/node";

import getSecret from "./secrets.js";
import { isProduction } from "./shopify.js";

// Load environment variables
const SENTRY_ENABLED = process.env.SENTRY_ENABLED === "true";
const SENTRY_SECRET_KEY = process.env.SENTRY_SECRET_KEY || "productOptionsSentryDSN";

if (SENTRY_ENABLED) {
  // @ts-ignore
  const dsn = await getSecret(SENTRY_SECRET_KEY);
  Sentry.init({
    dsn,
    environment: isProduction ? "production" : "development",
    integrations: [Sentry.extraErrorDataIntegration({ depth: 6 })],
  });
}
