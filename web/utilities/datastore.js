import _ from "lodash";

/* eslint-disable class-methods-use-this */
import { Datastore, PropertyFilter } from "@google-cloud/datastore";
import { Session } from "@shopify/shopify-api";

import { defaultSettings } from "../defaults.js";
import { captureException, captureMessage } from "./analytics.js";
import { googleCloudProjectId } from "./secrets.js";

const namespace = process.env.DATASTORE_NAMESPACE;

const datastore = new Datastore({ namespace, projectId: googleCloudProjectId });

export default datastore;

/* -------------------------------------- Helper functions -------------------------------------- */

/**
 * Chunks HTML content that exceeds the size limit
 *
 * @param {IProductOption['values']} values - The original values array
 * @param {string} htmlContent - The HTML content to chunk
 * @returns {IProductOption['values'] | null} - The new values array with chunked content, or null if no chunking needed
 */
export function chunkHtmlContent(values, htmlContent) {
  const contentBytes = new TextEncoder().encode(htmlContent).length;

  // If HTML content exceeds 1500 bytes, chunk it
  if (contentBytes > 1500) {
    const chunkSize = 1400; // Slightly less than 1500 to be safe
    const chunks = [];
    // Split the HTML content into chunks
    for (let i = 0; i < htmlContent.length; i += chunkSize) {
      chunks.push(htmlContent.substring(i, i + chunkSize));
    }
    // Store the chunks in the values array
    // First value contains metadata about chunking
    const newValues = [...values];
    newValues[0] = {
      ...newValues[0],
      htmlContent: "", // Empty string instead of null to avoid type issues
      htmlContentChunked: true, // Flag to indicate chunking
      htmlContentChunksCount: chunks.length, // Number of chunks
    };

    // Add the chunks as additional values with special properties
    for (let i = 0; i < chunks.length; i++) {
      newValues.push({
        htmlContentChunk: true,
        chunkIndex: i,
        chunkContent: chunks[i],
      });
    }
    return newValues;
  }

  return null;
}

/**
 * Reassembles chunked HTML content
 *
 * @param {IProductOption['values']} values - The values array containing chunked content
 * @returns {IProductOption['values'] | null} - The new values array with reassembled content, or null if reassembly failed
 */
export function reassembleHtmlContent(values) {
  if (!values || values.length === 0) return null;

  const firstValue = values[0];
  if (!firstValue.htmlContentChunked || !firstValue.htmlContentChunksCount) return null;

  // Collect all chunks
  const chunks = [];
  const chunkCount = firstValue.htmlContentChunksCount;

  // Find all chunks in the values array
  for (let i = 0; i < chunkCount; i++) {
    const chunkValue = values.find((value) => value.htmlContentChunk && value.chunkIndex === i);

    if (chunkValue && chunkValue.chunkContent) {
      chunks.push(chunkValue.chunkContent);
    }
  }

  // Reassemble the HTML content
  if (chunks.length === chunkCount) {
    const reassembledHtml = chunks.join("");
    // Create a new values array with just the first value containing the reassembled HTML
    return [
      {
        ...firstValue,
        htmlContent: reassembledHtml,
        htmlContentChunked: undefined,
        htmlContentChunksCount: undefined,
      },
    ];
  }
  return null;
}

/**
 * @template FilterValue
 *
 * @template TData
 * @template {keyof Stored<TData>} TFields
 * @template {TFields[] | TFields | null} TProjectionArrayFields
 *
 * @param {string} kind
 * @param {FilterValue[]} inFilterValues
 * @param {(
 *  query: import('@google-cloud/datastore').Query,
 *  chunk: FilterValue[]
 * ) => import('@google-cloud/datastore').Query} queryBuilder
 * @param {TProjectionArrayFields} projectionArrayFieldNames
 *
 * ! Important: if the query is NOT a projection, `null` for `projectionArrayFieldNames` must
 *   be passed, otherwise the result will be interpreted as a projection. !
 */
export async function runUnlimitedInQuery(kind, inFilterValues, queryBuilder, projectionArrayFieldNames) {
  const chunks = _.chunk(inFilterValues, 30);

  const results = await Promise.all(
    chunks.map(async (chunk) => {
      const queryPromise = queryBuilder(datastore.createQuery(kind), chunk).run();

      const queryResults =
        projectionArrayFieldNames === null
          ? (await queryPromise)[0]
          : await collectProjectedArrays(queryPromise, projectionArrayFieldNames);

      return queryResults;
    })
  );

  // Deduplicate results
  const deduplicatedResults = /**
   * @type {TProjectionArrayFields extends null
   *   ? DatastoreEntity<TData>[]
   *   : CollectedProjection<TData, keyof Stored<TData>>[]}
   */ (
    results
      .flat()
      .filter(
        (value, index, self) =>
          index ===
          self.findIndex(
            (previousValue) =>
              previousValue[datastore.KEY] && previousValue[datastore.KEY]?.id === value[datastore.KEY]?.id
          )
      )
  );

  return deduplicatedResults;
}

/**
 * @template {DatastoreEntity<any>[]} TData
 *
 * @param {import('@google-cloud/datastore').Key[]} keys
 *
 * @returns {Promise<TData>}
 */
//old
// export async function orderedGet(keys) {
//   /** @type {GetResponse<TData[number][]>} */
//   const [datastoreResults] = await datastore.get(keys);
//   const orderedResults = keys.flatMap(
//     (key) =>
//       datastoreResults.find(
//         (result) =>
//           result[datastore.KEY]?.namespace === key.namespace &&
//           result[datastore.KEY]?.path.every(
//             (/** @type {string | number} */ element, /** @type {number} */ index) =>
//               element.toString() === key.path[index].toString(),
//           ),
//       ) || [],
//   );

//   return /** @type {TData} */ (orderedResults);
// }

export async function orderedGet(keys) {
  /** @type {GetResponse<TData[number][]>} */
  // const [datastoreResults] = await datastore.get(keys);

  const chunks = _.chunk(keys, 500);
  const datastoreResults = [];

  for (const chunk of chunks) {
    const [result] = await datastore.get(chunk);
    datastoreResults.push(...result);
  }

  const orderedResults = keys.flatMap(
    (key) =>
      datastoreResults.find(
        (result) =>
          result[datastore.KEY]?.namespace === key.namespace &&
          result[datastore.KEY]?.path.every(
            (/** @type {string | number} */ element, /** @type {number} */ index) =>
              element.toString() === key.path[index].toString()
          )
      ) || []
  );

  return /** @type {TData} */ (orderedResults);
}

/**
 * @template TData
 * @template {keyof Stored<TData>} TFields
 *
 * @param {Promise<ProjectionQueryResponse<TData, TFields>>} queryPromise
 * @param {TFields[] | TFields} arrayFieldNames
 *
 * ! Note: This function cannot always reconstruct the original projected fields.
 *   It only reconstructs them exactly if the array fields don't contain duplicated values
 *   (and even then, the order of the elements might be different). !
 */
export async function collectProjectedArrays(queryPromise, arrayFieldNames) {
  const results = (await queryPromise)[0];

  if (Array.isArray(arrayFieldNames) && !arrayFieldNames.length) {
    return results;
  }

  /** @type {{ [id: string | number | symbol]: CollectedProjection<TData, keyof Stored<TData>> }} */
  const collectedEntities = {};

  results.forEach((entity) => {
    const id = entity[datastore.KEY]?.id || entity[datastore.KEY]?.name;
    if (!id) {
      return;
    }

    // Add all array field values to the group array
    (Array.isArray(arrayFieldNames) ? arrayFieldNames : [arrayFieldNames]).forEach((fieldName) => {
      const arrayValue = collectedEntities[id]?.[fieldName];

      collectedEntities[id] = {
        ...entity,
        ...(collectedEntities[id] || {}),
        [fieldName]: [...(Array.isArray(arrayValue) ? arrayValue : []), entity[fieldName]],
      };
    });
  });

  // Deduplicate array fields
  Object.values(collectedEntities).forEach((entity) => {
    (Array.isArray(arrayFieldNames) ? arrayFieldNames : [arrayFieldNames]).forEach((fieldName) => {
      // @ts-ignore
      // eslint-disable-next-line no-param-reassign
      entity[fieldName] = [...new Set(entity[fieldName])];
    });
  });

  return Object.values(collectedEntities);
}

/**
 * @param {string} shop
 */
export async function getShopProperties(shop) {
  const shopKey = datastore.key(["Shop", shop]);

  /** @type {GetResponse<IShop>} */
  const [shopProperties] = await datastore.get(shopKey);

  return shopProperties;
}

/**
 * @param {Partial<Record<keyof IShop, IShop[keyof IShop]>>} newProperties
 * @param {IShop | undefined} oldProperties
 * @param {string} shop
 * @param {boolean} [isAllowUndefinedProperties]
 */
export async function setShopProperties(newProperties, oldProperties, shop, isAllowUndefinedProperties = false) {
  if (!oldProperties && !isAllowUndefinedProperties) {
    captureMessage("Trying to set shop properties with undefined old properties.", shop);
    return;
  }

  await datastore.upsert({
    key: datastore.key(["Shop", shop]),
    data: { ...oldProperties, ...newProperties },
  });
}

/**
 * @template {DatastoreEntity<{}>[]} TData
 * @param {string} shop
 * @param {import('@google-cloud/datastore').Key[]} [extraKeys]
 *
 * @returns {Promise<[IShop | undefined, ...TData]>}
 */
export async function getValidatedShopProperties(shop, extraKeys) {
  const shopKey = datastore.key(["Shop", shop]);

  /** @type {[DatastoreEntity<IShop> | undefined, ...TData]} */
  const [shopProperties, ...extraData] = await orderedGet([shopKey, ...(extraKeys || [])]);

  const { settings, isSubscriptionActive } = shopProperties || {};

  /** @type {[IShop | undefined, ...TData]} */
  const results = [
    isSubscriptionActive && settings?.apiKey && settings.quantityUnit
      ? { settings: { ...defaultSettings, ...settings }, ...shopProperties }
      : undefined,
    ...extraData,
  ];

  return results;
}

/**
 * @param {(Stored<IProductOptionSet> & { id?: number })
 *   | DatastoreEntity<IProductOptionSet>} storedOptionSet
 * @param {string} shop
 *
 * @returns {IProductOptionSet | undefined}
 */
export function optionSetFromStored(storedOptionSet, shop) {
  let id = "id" in storedOptionSet ? storedOptionSet.id : undefined;
  id = datastore.KEY in storedOptionSet ? Number(storedOptionSet[datastore.KEY]?.id) : id;

  if (!id) {
    captureMessage("Option set has no ID", shop, { storedOption: storedOptionSet });
    return undefined;
  }

  return {
    ...storedOptionSet,
    id,
    createdAt: storedOptionSet.createdAt.toISOString(),
  };
}

/**
 * @param {(Stored<IProductOption> & { id?: number })
 *   | DatastoreEntity<IProductOption>} storedOption
 * @param {string} shop
 *
 * @returns {IProductOption | undefined}
 */
export function optionFromStored(storedOption, shop) {
  // Remove `metaobjectId` (and other server-only properties)
  const { metaobjectId, ...clientSideOption } = storedOption;

  let id = "id" in storedOption ? storedOption.id : undefined;
  id = datastore.KEY in storedOption ? Number(storedOption[datastore.KEY]?.id) : id;

  if (!id) {
    captureMessage("Option has no ID", shop, { storedOption });
    return undefined;
  }

  // Process HTML content reassembly if needed
  if (clientSideOption.type === "HTML" && clientSideOption.values && clientSideOption.values.length > 0) {
    const firstValue = clientSideOption.values[0];

    // Check if this HTML content was chunked
    if (firstValue.htmlContentChunked && firstValue.htmlContentChunksCount) {
      const reassembledValues = reassembleHtmlContent(clientSideOption.values);
      if (reassembledValues) {
        clientSideOption.values = reassembledValues;
      }
    }
  }

  return {
    ...clientSideOption,
    id,
    createdAt: clientSideOption.createdAt.toISOString(),
  };
}

/**
 * @param {IProductOption & Pick<Stored<IProductOption>, 'metaobjectId'>} option
 *
 * @returns {Stored<IProductOption>}
 */
export function optionToStored(option) {
  const { id, ...storedOption } = option;

  // Process HTML content chunking if needed
  if (option.type === "HTML" && option.values && option.values.length > 0) {
    const htmlValue = option.values[0];
    if (htmlValue.htmlContent) {
      const chunkedValues = chunkHtmlContent(storedOption.values, htmlValue.htmlContent);
      if (chunkedValues) {
        storedOption.values = chunkedValues;
      }
    }
  }

  return { ...storedOption, createdAt: option.createdAt ? new Date(option.createdAt) : new Date() };
}

/**
 * @param {IProductOptionSet} optionSet
 *
 * @returns {Stored<IProductOptionSet>}
 */
export function optionSetToStored(optionSet) {
  const { id, ...storedOption } = optionSet;

  return {
    ...storedOption,
    createdAt: optionSet.createdAt ? new Date(optionSet.createdAt) : new Date(),
  };
}

/* --------------------------------------- Session store ---------------------------------------- */

/** @typedef {import('@shopify/shopify-app-session-storage').SessionStorage} SessionStorage */

// https://github.com/Shopify/shopify-app-js/blob/main/packages/shopify-app-session-storage/implementing-session-storage.md

/** @implements {SessionStorage} */
export class DatastoreSessionStorage {
  /**
   * @param {Session} session
   */
  async storeSession(session) {
    try {
      const key = datastore.key(["Session", session.id]);
      await datastore.upsert({ key, data: session.toObject() });

      return true;
    } catch (error) {
      captureException(error, session.id);
      return false;
    }
  }

  /**
   * @param {string} id
   */
  async loadSession(id) {
    try {
      const key = datastore.key(["Session", id]);

      /** @type {GetResponse<SessionParams>} */
      const [sessionRecord] = await datastore.get(key);

      if (!sessionRecord) {
        return undefined;
      }

      // Remove the Datastore key, so that it doesn't get leaked
      delete sessionRecord[datastore.KEY];

      return new Session(sessionRecord);
    } catch (error) {
      captureException(error, id);
      return undefined;
    }
  }

  /**
   * @param {string} id
   */
  async deleteSession(id) {
    try {
      const key = datastore.key(["Session", id]);
      await datastore.delete(key);

      return true;
    } catch (error) {
      captureException(error, id);
      return false;
    }
  }

  /**
   * @param {string[]} ids
   */
  async deleteSessions(ids) {
    try {
      const keys = ids.map((id) => datastore.key(["Session", id]));
      await datastore.delete(keys);

      return true;
    } catch (error) {
      captureException(error, ids[0]);
      return false;
    }
  }

  /**
   * @param {string} shop
   */
  async findSessionsByShop(shop) {
    try {
      /** @type {QueryResponse<SessionParams>} */
      const [sessionRecords] = await datastore
        .createQuery("Session")
        .filter(new PropertyFilter("shop", "=", shop))
        .run();

      const sessions = sessionRecords.map((record) => {
        // Remove the Datastore key, so that it doesn't get leaked
        const sanitizedRecord = record;
        delete sanitizedRecord[datastore.KEY];

        return new Session(sanitizedRecord);
      });

      return sessions;
    } catch (error) {
      captureException(error, shop);
      return [];
    }
  }
}

/**
 * Function to get all unique shop names from Datastore
 * returns {Promise<string[]>} Array of unique shop names
 */
export async function getAllShopInfo() {
  const shopNamesSet = new Set();
  let query = datastore.createQuery("Shop").limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = _.get(shop, [datastore.KEY]);
      if (shopKey) shopNamesSet.add({ key: _.get(shopKey, "name"), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
}
