/* eslint-disable no-console */
import Mixpanel from "mixpanel";

import * as Sentry from "@sentry/node";

import getSecret from "./secrets.js";

// Flag to control if tracking is enabled
const trackEnabled = process.env.MIXPANEL_ENABLED === "true";

/**
 * @type {Mixpanel.Mixpanel}
 */
let mixpanel;

const productOptionsMixpanelAPIKey = process.env.MIX_PANEL_API_KEY;

// Initialize Mixpanel only if tracking is enabled
if (trackEnabled) {
  // @ts-ignore
  mixpanel = Mixpanel.init(await getSecret(productOptionsMixpanelAPIKey));
}

/**
 * @param {string} eventName
 * @param {string} userId
 * @param {Omit<import('mixpanel').PropertyDict, 'distinct_id'>} [properties]
 *
 * @returns {Promise<Error | undefined>}
 */
export default async function trackEvent(eventName, userId, properties) {
  // Only track event if Mixpanel is enabled
  if (!trackEnabled) {
    console.log("Tracking is disabled");
    console.log(`${eventName} = ${properties && JSON.stringify({ ...properties })}`);
    return;
  }
  // eslint-disable-next-line consistent-return
  return new Promise((resolve) => {
    mixpanel.track(eventName, { distinct_id: userId, ...properties }, resolve);
  });
}

/* ----------------------------------- Helper functions ------------------------------------ */

/**
 * @param {string} userId
 * @param {Omit<import('mixpanel').PropertyDict, 'distinct_id'>} [properties]
 *
 * @returns {Promise<Error | undefined>}
 */
export async function setProfileProperties(userId, properties) {
  // Only set profile properties if Mixpanel is enabled
  if (!trackEnabled) {
    console.log("Tracking is disabled");
    return;
  }

  // eslint-disable-next-line consistent-return
  return new Promise((resolve) => {
    mixpanel.people.set(userId, { distinct_id: userId, ...properties }, resolve);
  });
}

/**
 * @param {Error | unknown} exception
 * @param {string} userId
 * @param {any} [extraData]
 */
export function captureException(exception, userId, extraData) {
  Sentry.captureException(exception, { user: { id: userId }, extra: extraData });

  console.error("Error for", userId);
  console.trace(exception);

  if (extraData) {
    console.error(extraData);
  }
}

/**
 * @param {string} message
 * @param {string} userId
 * @param {any} [extraData]
 */
export function captureMessage(message, userId, extraData) {
  Sentry.captureException(message, { user: { id: userId }, extra: extraData });

  console.error("Error for", userId);
  console.trace(message);

  if (extraData) {
    console.error(extraData);
  }
}

/**
 * @param {import('./shopify.js').UserErrors | undefined} userErrors
 * @param {string} shop
 */
export function captureUserErrors(userErrors, shop) {
  if (!userErrors?.length) {
    return;
  }

  userErrors.slice(1).forEach((error) => {
    console.error(`Error on field ${error.field}:`, error.message);
    Sentry.addBreadcrumb({ message: error.message, data: { field: error.field } });
  });

  captureMessage(userErrors[0].message, shop, { field: userErrors[0].field });
}
