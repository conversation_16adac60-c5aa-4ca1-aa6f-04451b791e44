import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

export const googleCloudProjectId = process.env.GOOGLE_CLOUD_PROJECT_ID;

const secretManagerClient = new SecretManagerServiceClient({ projectId: googleCloudProjectId });

/**
 * @param {'productOptionsSentryDSN' | 'productOptionsMixpanelAPIKey'} name
 */
export default async function getSecret(name) {
  /** @type {undefined | string} */
  let secretResult;

  try {
    const [accessResponse] = await secretManagerClient.accessSecretVersion({
      name: `projects/${googleCloudProjectId}/secrets/${name}/versions/latest`,
    });
    secretResult = accessResponse.payload?.data?.toString();
  } catch {
    // Can't get secret
  }

  if (!secretResult) {
    // eslint-disable-next-line no-console
    console.error(`Secret named "${name}" could not be accessed.`);
    process.exit(1);
  }

  return secretResult;
}
