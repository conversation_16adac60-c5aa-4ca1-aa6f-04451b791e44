// eslint-disable-next-line import/no-extraneous-dependencies
import { apiResponseCodes } from 'easyflow-enums';

/**
 * Middleware to add response methods to the response object
 * @returns {Function} Middleware function
 */
const responseMiddleware = () => (_, res, next) => {
  /**
   * Send a success response
   * @param {Object} params - The response parameters
   * @param {string} params.message - The success message
   * @param {Object} params.data - Additional data to include in the response
   * @returns {Object} The response object
   */
  res.success = ({ message, ...data }) => {
    res.status(apiResponseCodes.SUCCESS).json({ ...data, message });
    return res;
  };

  /**
   * Send a failed response
   * @param {Object} params - The response parameters
   * @param {string} [params.message=''] - The error message
   * @param {Array} [params.errors=undefined] - The errors
   * @returns {Object} The response object
   */
  res.failed = ({ message = '', errors = undefined }) => {
    res.status(apiResponseCodes.FAILED).json({ errors, message });
    return res;
  };

  /**
   * Send a failed validation response
   * @param {Object} params - The response parameters
   * @param {string} [params.message=''] - The validation error message
   * @param {Array} [params.errors=[]] - The validation errors
   * @returns {Object} The response object
   */
  res.failedValidation = ({ message = '', errors = [] }) => {
    res
      .status(apiResponseCodes.VALIDATION_ERROR)
      .json({ errors, message: message || apiResponseCodes.VALIDATION_ERROR });
    return res;
  };

  /**
   * Send a liquid response
   * @param {string} data - The liquid data to send
   * @returns {Object} The response object
   */
  res.liquid = (data) => {
    res.set('Content-Type', 'application/liquid').send(data);
    return res;
  };

  // Call the next middleware
  next();
};

const applyResponseMiddleware = (app) => {
  app.use(responseMiddleware());
};

export default { applyResponseMiddleware };
