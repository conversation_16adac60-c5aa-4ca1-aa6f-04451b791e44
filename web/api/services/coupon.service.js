// eslint-disable-next-line import/no-extraneous-dependencies
import { couponStatus } from "easyflow-enums";
import lodash from "lodash";
import prisma from "../../prisma/prisma.client.js";

const { get } = lodash;

/**
 * Get coupon details by unique coupon code
 * @param code
 * @param reject
 * @returns {Promise<*|null>}
 */
const getCouponDetail = async (code) => {
  const coupon = await prisma.coupon.findFirst({
    where: {
      code: {
        equals: code,
        mode: "insensitive",
      },
      status: couponStatus.ACTIVE,
    },
  });

  return coupon || null;
};

/**
 * Updates the redeem count of a coupon.
 * @param {string} couponCode - The unique coupon code.
 * @param {number} incrementBy - The number to increment the redeem count by.
 * @throws Will throw an error if the coupon does not exist or the update fails.
 */
const updateCouponRedeemCount = async (couponCode, incrementBy = 1) => {
  try {
    // Update the redeem count
    const updatedCoupon = await prisma.coupon.update({
      where: { code: couponCode },
      data: {
        redeem_count: {
          increment: incrementBy,
        },
      },
    });

    return updatedCoupon;
  } catch (error) {
    console.error("Error updating coupon redeem count:", error);
    return null;
  }
};

/**
 *
 * @param {IShop} shop
 * @param {object | null} appSubscriptionId
 * @param {import("../../types/SubscriptionPlanCoupon.js").ISubscriptionPlanCoupon} couponDetails
 * @returns
 */
const saveCouponApplied = async (shop, couponDetails, appSubscriptionId = null) => {
  try {
    const couponApplied = await prisma.coupons_applied.create({
      data: {
        shop_domain: get(shop, "domain"),
        app_subscription_id: appSubscriptionId,
        name: get(couponDetails, "name"),
        code: get(couponDetails, "code"),
        discount_type: get(couponDetails, "discount_type"),
        amount: get(couponDetails, "amount"),
      },
    });
    return couponApplied;
  } catch (err) {
    console.log("err", err);
  }
};

export { getCouponDetail, saveCouponApplied, updateCouponRedeemCount };
