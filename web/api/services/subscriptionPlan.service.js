// eslint-disable-next-line import/no-extraneous-dependencies
import { planStatus } from "easyflow-enums";
import prisma from "../../prisma/prisma.client.js";
import { subscriptionPlanSerializer } from "../serializers/subscription.serializer.js";

/**
 * Fetch subscription plans based on given conditions.
 *
 * @param { string | undefined} planSlug - The conditions to filter the subscription plans.
 */
const getActiveSubscriptionPlans = async (planSlug) => {
  const [plans, activePlan] = await Promise.all([
    getPlansByCondition({ status: planStatus.ACTIVE }, { price: "asc" }, { coupon: true }),
    // @ts-ignore
    // eslint-disable-next-line no-nested-ternary
    planSlug ? (planSlug === "old" ? formateOldPlanData() : getSubscriptionPlanByConditions({ slug: planSlug })) : null,
  ]);

  return subscriptionPlanSerializer(plans, activePlan);
};

/**
 * Fetch subscription plans based on given conditions.
 *
 * @param {Object} conditions - The conditions to filter the subscription plans.
 * @param {Object} [order] - The order in which to sort the subscription plans.
 * @param {Object} [includes] - The related models to include in the query.
 */
const getPlansByCondition = async (conditions, order = undefined, includes = undefined) => {
  const plans = await prisma.subscription_plans.findMany({
    where: conditions,
    orderBy: order,
    include: includes,
  });
  return plans;
};

/**
 * Get subscription plan by id
 * @param {bigint} id - The ID of the subscription plan.
 * @param {[]} [attributes] - The attributes to select.
 * @returns {Promise<any>}
 */
const getSubscriptionPlanById = async (id, attributes = undefined) => {
  const plan = await prisma.subscription_plans.findUnique({
    where: { id },
    select: attributes ? Object.fromEntries(attributes.map((attr) => [attr, true])) : undefined,
  });
  return plan || null;
};

/**
 * Get subscription plan by conditions
 * @param {Object} conditions - The conditions to filter the subscription plans.
 * @param {Array<string>} [includes] - The related models to include in the query.
 * @returns {Promise<*|null>}
 */
const getSubscriptionPlanByConditions = async (conditions, includes = undefined) => {
  const plan = await prisma.subscription_plans.findFirst({
    where: conditions,
    include: includes?.reduce((acc, include) => {
      // @ts-ignore
      acc[include] = true;
      return acc;
    }, {}),
  });
  return plan || null;
};

const formateOldPlanData = async () => ({
  name: "",
  slug: "",
});

export { getActiveSubscriptionPlans, getSubscriptionPlanByConditions };
