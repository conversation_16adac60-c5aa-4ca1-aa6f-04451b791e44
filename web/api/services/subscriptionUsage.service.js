import prisma from "../../prisma/prisma.client.js";
import { preparePlanDescription } from "../helpers/global.js";

/**
 *
 * @param {IShop} shopData
 * @returns
 */
const serializeUsageFromShop = (shopData, { meta = null, occurrence = 1 } = {}) => {
  const lineId = shopData.appSubscriptionData?.lineItems.find(
    // eslint-disable-next-line no-underscore-dangle
    (li) => li.plan.pricingDetails?.terms || li.plan.pricingDetails?.__typename === "AppUsagePricing"
  ).id;

  return {
    shop_domain: shopData.domain,
    plan_slug: shopData.planSlug,
    subscription_id: shopData.appSubscriptionId,
    subscription_line_id: lineId,
    description: preparePlanDescription(
      shopData.planData?.interval,
      shopData.planData?.finalPrice,
      shopData.planData?.meta?.duration || 18
    ),
    price: shopData.planData?.finalPrice,
    meta,
    occurrence,
  };
};

const getUsageByConditions = async (conditions) => {
  const usage = await prisma.subscription_usage.findFirst({
    where: conditions,
  });
  return usage || null;
};

const updateUsage = async (id, data) => {
  const updatedUsage = await prisma.subscription_usage.updateManyAndReturn({
    where: { id },
    data,
  });

  if (updatedUsage.length > 0) {
    return updatedUsage[0];
  }
  const usages = await prisma.subscription_usage.findFirst({ where: { id } });
  return usages;
};

const createUsage = async (data) => {
  const usage = await prisma.subscription_usage.create({
    data,
  });
  return usage;
};

const updateOrCreateUsage = async (shopData) => {
  const prevUsage = await getUsageByConditions({
    shop_domain: shopData.domain,
    plan_slug: shopData.planSlug,
    occurrence: 1,
    // meta: { [Op.not]: null },
  });

  const data = serializeUsageFromShop(shopData);

  if (prevUsage) {
    const usages = await updateUsage(prevUsage.id, data);
    return usages;
  }
  const usage = createUsage(data);
  return usage;
};

export { updateOrCreateUsage, updateUsage };
