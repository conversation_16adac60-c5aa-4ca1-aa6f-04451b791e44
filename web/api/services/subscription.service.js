import { appSubscriptionStatus, subscriptionIntervals } from "easyflow-enums";
import lodash from "lodash";
import trackEvent from "../../utilities/analytics.js";
import { getShopProperties, setShopProperties } from "../../utilities/datastore.js";
import {
  appSubscriptionCreate,
  cancelSubscription,
  createUsageCharge,
  getActiveSubscriptions,
  loadSession,
} from "../../utilities/shopify.js";
import { checkIfIsFreeSubscription, updateShopCouponMeta } from "../helpers/subscription.helper.js";
import { serializeShopPlanData, serializeSinglePlan } from "../serializers/subscription.serializer.js";
import { getCouponDetail, saveCouponApplied, updateCouponRedeemCount } from "./coupon.service.js";
import { getSubscriptionPlanByConditions } from "./subscriptionPlan.service.js";
import { createTransaction, getTransTypeNarration } from "./subscriptionTransaction.service.js";
import { updateOrCreateUsage, updateUsage } from "./subscriptionUsage.service.js";

const { get, isEmpty, isNull } = lodash;

/**
 *
 * @param {IShop} shop
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} plan
 * @param {import('../../types/SubscriptionPlanCoupon.js').ISubscriptionPlanCoupon} couponDetails
 * @returns
 */
const getSerializeCheckoutPageData = async (shop, plan, couponDetails) => {
  const currentPlan = get(shop, "planSlug", null);
  const activePlan = currentPlan
    ? await getSubscriptionPlanByConditions({
        slug: get(shop, "planSlug"),
      })
    : null;
  const updatedPlan = { ...plan };
  if (couponDetails) {
    updatedPlan.coupon = couponDetails;
  }
  const {
    coupon,
    is_hundred_percent_coupon: isHundredPercentCoupon,
    ...planData
  } = serializeSinglePlan(updatedPlan, activePlan);
  const { narration } = await getTransTypeNarration(shop, updatedPlan);
  return {
    name: planData.name,
    slug: planData.slug,
    status: planData.status,
    type: planData.type,
    interval: planData.interval,
    sub_total: planData.sub_total,
    narration,
    price: planData.price || 0,
    duration: planData?.meta?.duration || 1,
    discount: planData.discount || 0,
    final_price: planData.final_price || 0,
    coupon_code: coupon?.code || null,
    coupon_desc: coupon?.name || null,
    hundred_percent_coupon_applied: isHundredPercentCoupon,
    is_upgradable: planData.is_upgradable,
    is_monthly: planData.is_monthly,
    is_free: planData.is_free,
    interval_text: planData.interval_text,
    package_info: planData.package_info,
    trial_days: planData.trial_days,
    currency: planData.currency,
    discount_type: planData.discount_type,
    test: planData.test,
    meta: planData.meta,
  };
};

/**
 *
 * @param {string | null | undefined } activePlanSlug
 * @param {string} planSlug
 * @param {string} couponCode
 * @returns
 */
const validateCouponCode = async (activePlanSlug, planSlug, couponCode) => {
  const couponErrorMessage = "Invalid coupon code.";
  const planData = await getSubscriptionPlanByConditions({ slug: planSlug });
  const activePlan = activePlanSlug
    ? await getSubscriptionPlanByConditions({
        slug: activePlanSlug,
      })
    : null;

  const couponDetail = await getCouponDetail(couponCode);

  if (isEmpty(planData) || isEmpty(couponDetail)) {
    throw new Error(couponErrorMessage);
  }

  if (!isEmpty(couponDetail.plans) && !couponDetail.plans.includes(planData.slug)) {
    throw new Error(couponErrorMessage);
  }

  if (!isNull(couponDetail.max_limit) && couponDetail.max_limit <= couponDetail.redeem_count) {
    throw new Error(couponErrorMessage);
  }

  const startDate = +new Date(couponDetail.start_date).setHours(0, 0, 0);
  const endDate = +new Date(couponDetail.end_date).setHours(23, 59, 59);
  const now = +new Date();

  if (!isNull(couponDetail.start_date) && now < startDate) {
    throw new Error(couponErrorMessage);
  }

  if (!isNull(couponDetail.end_date) && now > endDate) {
    throw new Error(couponErrorMessage);
  }

  planData.coupon = couponDetail;

  const {
    coupon,
    is_hundred_percent_coupon: isHundredPercentCoupon,
    is_discount_applicable: isDiscountApplicable,
    ...serializedData
  } = serializeSinglePlan(planData, activePlan);

  if (!isDiscountApplicable) throw new Error("Discount not applicable!");

  return {
    plan: serializedData,
    coupon,
    is_hundred_percent_coupon: isHundredPercentCoupon,
  };
};

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {IShop | undefined} currentShopData
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} subscriptionPlan
 */
const handleFreeSubscription = async (session, currentShopData, subscriptionPlan) => {
  const shopActiveSubscription = await getActiveSubscriptions(session);

  let newShopData = {
    ...currentShopData,
  };

  if (!isEmpty(shopActiveSubscription)) {
    await cancelSubscription(session, shopActiveSubscription[0].id);
  }
  if (newShopData && !isEmpty(subscriptionPlan.coupon)) {
    saveCouponApplied(newShopData, subscriptionPlan.coupon, null);
    // @ts-ignore
    newShopData = updateShopCouponMeta(newShopData, get(subscriptionPlan, "coupon.code", ""));
  }
  const updateShopPlanData = serializeShopPlanData(newShopData, subscriptionPlan);

  const finalShopData = { ...newShopData, ...updateShopPlanData };

  // @ts-ignore
  await setShopProperties(updateShopPlanData, newShopData, session?.shop, true);
  // @ts-ignore
  createTransaction(finalShopData, subscriptionPlan, updateShopPlanData);
};

/**
 *
 * @param {import('@shopify/shopify-api').Session} session
 * @param {string} planSlug
 * @param {null | string} couponCode
 */
const subscriptionCreate = async (session, planSlug, couponCode = null) => {
  let appSubscription;
  let confirmationUrl;
  let coupon;

  // eslint-disable-next-line prefer-const
  let [currentShop, plan] = await Promise.all([
    getShopProperties(session.shop),
    getSubscriptionPlanByConditions({ slug: planSlug }),
  ]);

  if (!isEmpty(couponCode)) {
    const validatedCouponData = await validateCouponCode(
      get(currentShop, "planSlug", null),
      planSlug,
      couponCode || ""
    );
    coupon = validatedCouponData.coupon;
    plan = validatedCouponData.plan;
  } else {
    plan = serializeSinglePlan(plan);
  }

  const isFreeSubscription = checkIfIsFreeSubscription(plan);
  if (isFreeSubscription) {
    // @ts-ignore
    await handleFreeSubscription(session, currentShop, plan);
    confirmationUrl = `shopify://admin/apps/${get(currentShop, "appHandle", "")}/`;
  } else {
    // @ts-ignore
    const newSubscription = await appSubscriptionCreate(session, currentShop, plan, coupon);
    appSubscription = newSubscription.appSubscription;
    confirmationUrl = newSubscription.confirmationUrl;

    if (!isEmpty(coupon) && !isEmpty(currentShop)) {
      const newShopData = updateShopCouponMeta(currentShop, get(coupon, "code", ""));
      // @ts-ignore
      await setShopProperties(newShopData, currentShop, session?.shop, true);
      // @ts-ignore
      saveCouponApplied(currentShop, coupon, appSubscription ? get(appSubscription, "id", null) : null);
    }
  }

  return {
    appSubscription,
    confirmationUrl,
    isFreeSubscription,
  };
};

/**
 *
 * @param {IShop} shop
 * @param {string} planSlug
 */
const getPlanDataForTransaction = async (shop, planSlug) => {
  const planInfo = await getSubscriptionPlanByConditions({ slug: planSlug });
  if (planInfo) {
    const couponCode = get(shop, "meta.coupon_code", null);
    const coupon = couponCode ? await getCouponDetail(couponCode) : null;

    const planData = await getSerializeCheckoutPageData(shop, planInfo, coupon);
    return planData;
  }
  return null;
};

/**
 *
 * @param {string} shopDomain
 * @param {IShop} shopData
 * @param {*} appSubscription
 * @returns
 */
const subscriptionUpdate = async (shopDomain, shopData, appSubscription) => {
  // @ts-ignore
  const { name, status } = appSubscription;
  const session = await loadSession(shopDomain, "cancel shop subscription");
  if (!session) {
    return;
  }

  trackEvent(`Updating subscription to: ${name} --- ${status}`, shopDomain);

  if (status === appSubscriptionStatus.DECLINED) {
    trackEvent(`Subscription declined ${name} --- ${status}`, shopDomain);
    return;
  }

  const subscriptionPlan = await getPlanDataForTransaction(shopData, name);
  if (subscriptionPlan && status === appSubscriptionStatus.ACTIVE) {
    const shopActiveSubscription = await getActiveSubscriptions(session);
    // @ts-ignore
    const updateShopPlanData = serializeShopPlanData(shopData, subscriptionPlan, shopActiveSubscription[0]);
    const finalShopData = { ...shopData, ...updateShopPlanData };
    const isCouponApplied = !!get(finalShopData, "meta.coupon_code", null);
    await Promise.all([
      // @ts-ignore
      setShopProperties(updateShopPlanData, shopData, shopDomain, true),
      // @ts-ignore
      createTransaction(finalShopData, subscriptionPlan, appSubscription),
      isCouponApplied && updateCouponRedeemCount(get(finalShopData, "meta.coupon_code", "")),
    ]);

    if (subscriptionPlan.interval === subscriptionIntervals.USAGE) {
      const usage = await updateOrCreateUsage({ ...shopData, ...updateShopPlanData });

      if (usage.meta === null) {
        const res = await createUsageCharge(session, usage);
        await updateUsage(usage.id, { meta: { ...res } });
      }
    }
  }

  //
  //
};

export {
  getPlanDataForTransaction,
  getSerializeCheckoutPageData,
  handleFreeSubscription,
  subscriptionCreate,
  subscriptionUpdate,
  validateCouponCode,
};
