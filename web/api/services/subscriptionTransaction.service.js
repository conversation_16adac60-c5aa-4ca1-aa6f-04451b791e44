import lodash from "lodash";
// eslint-disable-next-line import/no-extraneous-dependencies
import { subscriptionTypes, transactionTypes } from "easyflow-enums";
import prisma from "../../prisma/prisma.client.js";

const { isEmpty, get } = lodash;

/**
 *
 * @param {IShop} shop
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} subscriptionPlan
 */
const getTransTypeNarration = async (shop, subscriptionPlan) => {
  const { id, slug, name, price } = subscriptionPlan;
  // Get the last transaction for the shop
  const lastTrans = await getLastTransaction(shop.domain || "");

  // If there is no last transaction, then it's a new subscription
  if (isEmpty(lastTrans)) {
    return {
      narration: `Subscribe to "${name}" plan.`,
      transType: transactionTypes.NEW,
    };
  }

  // If the current plan slug and new plan slug are the same, and the price is the same, then it's a renewal
  if (shop.planSlug && shop.planSlug === slug && lastTrans.price === price) {
    return {
      narration: `Plan "${name}" renewed.`,
      transType: transactionTypes.RENEWAL,
    };
  }

  // If the new plan price is greater than the last transaction price, then it's an upgrade
  if (price > lastTrans.price) {
    return {
      narration: `Plan upgrade from "${lastTrans.name}" to "${name}".`,
      transType: lastTrans.type === subscriptionTypes.FREE ? transactionTypes.NEW : transactionTypes.UPGRADE,
    };
  }

  // If the new plan price is less than the last transaction price, then it's a downgrade
  if (price < lastTrans.price) {
    return {
      narration: `Plan downgrade from "${lastTrans.name}" to "${name}".`,
      transType: transactionTypes.DOWNGRADE,
    };
  }

  // Default case: renewal of the subscription
  return { narration: `Subscribe to "${name}" plan.`, transType: transactionTypes.RENEWAL };
};

/**
 * Get Shops last transaction
 * @param {string} shopDomain
 * @returns {Promise<*|null>}
 */
const getLastTransaction = async (shopDomain) => {
  const lastTrans = await prisma.subscription_transactions.findFirst({
    where: { shop_domain: shopDomain },
    orderBy: {
      created_at: "desc",
    },
  });
  return lastTrans || null;
};

/**
 *
 * @param {IShop} shop
 * @param {*} subscriptionData
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} subscriptionPlan
 * @returns
 */
const createTransaction = async (shop, subscriptionPlan, subscriptionData) => {
  try {
    const { narration, transType } = await getTransTypeNarration(shop, subscriptionPlan);
    const transactionData = {
      shop_domain: get(shop, "domain"),
      plan_slug: get(subscriptionPlan, "slug"),
      name: get(subscriptionPlan, "name"),
      slug: get(subscriptionPlan, "slug"),
      status: get(subscriptionPlan, "status"),
      type: get(subscriptionPlan, "type"),
      interval: get(subscriptionPlan, "interval"),
      transaction_type: transType,
      transaction_narration: narration,
      plan_data: subscriptionPlan,
      requested_data: subscriptionData ?? [],
      meta: get(subscriptionPlan, "meta", null),
      subscription_expire_date: get(shop, "planExpireDate"),
      is_paid:
        get(subscriptionPlan, "type") === subscriptionTypes.FREE ||
        (subscriptionData && get(subscriptionData, "status") === "ACTIVE"),
      trial_days: get(subscriptionPlan, "trial_days"),
      currency: get(subscriptionPlan, "currency"),
      test: get(subscriptionPlan, "test"),
      discount_type: get(subscriptionPlan, "discount_type"),
      price: parseFloat(get(subscriptionPlan, "price", 0).toFixed(2)),
      discount: parseFloat(get(subscriptionPlan, "discount", 0).toFixed(2)),
      final_price: parseFloat(get(subscriptionPlan, "final_price", 0).toFixed(2)),
      coupon_code: get(subscriptionPlan, "coupon.code", null),
    };
    const trans = await prisma.subscription_transactions.create({ data: transactionData });
    return trans;
  } catch (err) {
    console.log(err);
  }
};

export { createTransaction, getTransTypeNarration };
