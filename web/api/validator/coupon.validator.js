// eslint-disable-next-line import/no-extraneous-dependencies
import { z } from "zod";
import { serializeValidationErrors } from "../helpers/global.js";

// Define the schema
const couponSchema = z.object({
  slug: z.string({ required_error: "Slug is required" }).trim(),
  coupon: z.string({ required_error: "Please input coupon code" }).trim().nullable().optional(), // Allow coupon to be null or undefined,
});

/**
 * @typedef {Object} ValidationResult
 * @property {boolean} success
 * @property {string} message
 * @property {object} errors
 */

/**
 *
 * @type {ValidationResult}
 */
let data = {
  success: true,
  message: "",
  errors: {},
};

/**
 *
 * @param {*} reqData
 * @returns
 */
const validateSubscriptionCoupon = async (reqData) => {
  const result = couponSchema.safeParse(reqData);
  if (!result.success) {
    const formatted = serializeValidationErrors(result.error.format());
    data = {
      ...data,
      success: false,
      message: "Validation error",
      errors: formatted,
    };
  }
  return data;
};

export { validateSubscriptionCoupon };
