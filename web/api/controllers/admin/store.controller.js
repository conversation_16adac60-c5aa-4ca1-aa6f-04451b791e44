import { subscriptionIntervals } from "easyflow-enums";
import lodash from "lodash";
import { captureException } from "../../../utilities/analytics.js";
import { getShopProperties, setShopProperties } from "../../../utilities/datastore.js";
import { cancelSubscription, getActiveSubscriptions, loadSession } from "../../../utilities/shopify.js";
import { serializeShopSubscriptionPlanData } from "../../helpers/subscription.helper.js";
import { getSubscriptionPlanByConditions } from "../../services/subscriptionPlan.service.js";

const { isEmpty } = lodash;

/**
 * Cancel active subscription for the shop (Admin API)
 * @param {import('express').Request} req - The HTTP request object.
 * @param {import('express').Response} res - The HTTP response object.
 */
const cancelShopSubscription = async (req, res) => {
  try {
    const { shop: shopDomain } = req.body || {};

    // Validate required parameters
    if (!shopDomain || typeof shopDomain !== 'string') {
      return res.status(400).send({
        error: "Bad Request",
        message: "Shop domain is required and must be a string",
      });
    }

    // Load session for the shop
    const session = await loadSession(shopDomain, "cancel shop subscription");
    if (!session) {
      return res.status(404).send({
        error: "Session not found",
        message: `No active session found for shop: ${shopDomain}`,
      });
    }

    const [shopProperties, shopActiveSubscription] = await Promise.all([
      getShopProperties(session?.shop),
      getActiveSubscriptions(session),
    ]);

    if (isEmpty(shopActiveSubscription)) {
      return res.status(400).send({
        error: "No active subscription found to cancel",
        message: "No active subscription found",
        shop: shopDomain,
      });
    }

    // Cancel the subscription
    const cancelledSubscription = await cancelSubscription(session, shopActiveSubscription[0].id);

    // Get free plan to set as default
    const freePlan = await getSubscriptionPlanByConditions({ slug: `free-${subscriptionIntervals.LIFETIME}` });
    const freePlanData = freePlan ? serializeShopSubscriptionPlanData(freePlan) : {};

    // Calculate free plan expiry (5 years from now for lifetime plan)
    const freePlanExpiry = new Date();
    freePlanExpiry.setFullYear(freePlanExpiry.getFullYear() + 5);

    // Create free plan features from package_info
    /** @type {Record<string, any>} */
    const freePlanFeatures = {};
    if (freePlan?.package_info && Array.isArray(freePlan.package_info)) {
      freePlan.package_info.forEach(/** @param {any} feature */ (feature) => {
        if (feature && feature.name && typeof feature.name === 'string') {
          freePlanFeatures[feature.name] = feature.value;
        }
      });
    }

    // Update shop properties with complete free plan data to match the expected structure
    // Create the updated properties object with proper typing
    const updatedProperties = {
      isSubscriptionActive: false,
      planData: freePlanData,
      planSlug: `free-${subscriptionIntervals.LIFETIME}`,
      planStatus: null, // Set to null as shown in the example
      meta: {
        ...(shopProperties?.meta || {}),
        coupon_code: null
      }
    };

    // Add additional properties using object spread to avoid type conflicts
    Object.assign(updatedProperties, {
      planExpireDate: freePlanExpiry.toISOString(),
      planFeatures: freePlanFeatures,
      appSubscriptionId: null,
      appSubscriptionData: null,
      subscribedAt: null
    });

    await setShopProperties(
      updatedProperties,
      shopProperties,
      session?.shop,
      false
    );

    return res.send({
      success: true,
      message: "Subscription cancelled successfully",
      shop: shopDomain,
      cancelled_subscription: cancelledSubscription,
      subscription_id: shopActiveSubscription[0].id,
    });
  } catch (error) {
    const shopDomain = req.body?.shop || 'unknown';
    captureException(error, shopDomain);
    return res
      .status(error instanceof Error ? 400 : 500)
      .send({
        error: error instanceof Error ? error.message : "Internal server error",
        shop: shopDomain,
        success: false,
      });
  }
};

export { cancelShopSubscription };
