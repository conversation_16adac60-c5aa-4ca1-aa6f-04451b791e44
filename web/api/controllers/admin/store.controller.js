import lodash from "lodash";
import { captureException } from "../../../utilities/analytics.js";
import { getShopProperties, setShopProperties } from "../../../utilities/datastore.js";
import { cancelSubscription, getActiveSubscriptions, loadSession } from "../../../utilities/shopify.js";

const { isEmpty } = lodash;

/**
 * Cancel active subscription for the shop (Admin API)
 * @param {import('express').Request} req
 * @param {import('express').Response} res
 */
const cancelShopSubscription = async (req, res) => {
  try {
    const { shop: shopDomain } = req.body || {};

    // Validate required parameters
    if (!shopDomain || typeof shopDomain !== 'string') {
      return res.status(400).send({
        error: "Bad Request",
        message: "Shop domain is required and must be a string",
      });
    }

    // Load session for the shop
    const session = await loadSession(shopDomain, "cancel shop subscription");
    if (!session) {
      return res.status(404).send({
        error: "Session not found",
        message: `No active session found for shop: ${shopDomain}`,
      });
    }

    const [shopProperties, shopActiveSubscription] = await Promise.all([
      getShopProperties(session?.shop),
      getActiveSubscriptions(session),
    ]);

    if (isEmpty(shopActiveSubscription)) {
      return res.status(400).send({
        error: "No active subscription found to cancel",
        message: "No active subscription found",
        shop: shopDomain,
      });
    }

    // Cancel the subscription
    const cancelledSubscription = await cancelSubscription(session, shopActiveSubscription[0].id);

    // Update shop properties to reflect cancellation
    await setShopProperties({ isSubscriptionActive: false }, shopProperties, session?.shop, false);

    return res.send({
      success: true,
      message: "Subscription cancelled successfully",
      shop: shopDomain,
      cancelled_subscription: cancelledSubscription,
      subscription_id: shopActiveSubscription[0].id,
    });
  } catch (error) {
    const shopDomain = req.body?.shop || 'unknown';
    captureException(error, shopDomain);
    return res
      .status(error instanceof Error ? 400 : 500)
      .send({
        error: error instanceof Error ? error.message : "Internal server error",
        shop: shopDomain,
        success: false,
      });
  }
};

export { cancelShopSubscription };
