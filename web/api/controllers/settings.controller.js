import lodash from "lodash";
// eslint-disable-next-line import/no-extraneous-dependencies
import { apiResponseCodes } from "easyflow-enums";
import { captureException } from "../../utilities/analytics.js";
import { getShopProperties } from "../../utilities/datastore.js";
import { getCouponDetail } from "../services/coupon.service.js";
import {
  getSerializeCheckoutPageData,
  subscriptionCreate,
  validateCouponCode,
} from "../services/subscription.service.js";
import { getActiveSubscriptionPlans, getSubscriptionPlanByConditions } from "../services/subscriptionPlan.service.js";
import { validateSubscriptionCoupon } from "../validator/coupon.validator.js";

const { get } = lodash;

/**
 * Retrieves the active subscription plans for the shop.
 *
 * @param {import('express').Request} req - The HTTP request object.
 * @param {import('express').Response} res - The HTTP response object.
 */
const getSubscriptions = async (req, res) => {
  const { session } = res.locals.shopify;
  try {
    const { shop } = session;
    const shopProperties = await getShopProperties(shop);

    const previousPlanSlug = get(shopProperties, "planSlug", "old");

    const plans = await getActiveSubscriptionPlans(previousPlanSlug);

    return res.send({ plan_data: plans, is_old_subscription: previousPlanSlug === "old" });
  } catch (error) {
    captureException(error, session.shop);
    return res
      .status(error instanceof Error ? 400 : 500)
      .send({ error: error instanceof Error ? error.message : "Internal server error" });
  }
};

/**
 * Retrieves the active subscription plans for the shop.
 *
 * @param {import('express').Request} req - The HTTP request object.
 * @param {import('express').Response} res - The HTTP response object.
 */
const getCheckoutPageData = async (req, res) => {
  const { session } = res.locals.shopify;
  const { slug } = req.params;

  try {
    const { coupon: couponCode } = req.query;

    const [shop, plan, couponDetails] = await Promise.all([
      getShopProperties(session.shop),
      getSubscriptionPlanByConditions({ slug }),
      couponCode ? getCouponDetail(couponCode) : null,
    ]);

    // @ts-ignore
    const planData = await getSerializeCheckoutPageData(shop, plan, couponDetails);

    return res.send({ plan_data: planData });
  } catch (error) {
    captureException(error, session.shop);
    return res
      .status(error instanceof Error ? 400 : 500)
      .send({ error: error instanceof Error ? error.message : "Internal server error" });
  }
};

/**
 * Retrieves the active subscription plans for the shop.
 * @param {import('express').Request} req - The HTTP request object.
 * @param {import('express').Response} res - The HTTP response object.
 */
const validateCoupon = async (req, res) => {
  const { session } = res.locals.shopify;

  try {
    const { success, message, errors } = await validateSubscriptionCoupon(req.body);
    if (!success) {
      return res
        .status(apiResponseCodes.VALIDATION_ERROR)
        .json({ errors, message: message || apiResponseCodes.VALIDATION_ERROR });
    }

    const { slug, coupon: couponCode } = req.body;

    const currentShop = await getShopProperties(session.shop);

    const { plan, coupon } = await validateCouponCode(get(currentShop, "planSlug", null), slug, couponCode);

    // @ts-ignore
    const planData = await getSerializeCheckoutPageData(currentShop, plan, coupon);
    return res.send({
      plan_data: planData,
      message: "Coupon applied",
    });
  } catch (error) {
    return res.status(error instanceof Error ? 400 : 500).send({
      message: error instanceof Error ? error.message : "Internal server error",
      errors: { coupon: [error instanceof Error ? error.message : ""] },
    });
  }
};

/**
 * Retrieves the active subscription plans for the shop.
 *
 * @param {import('express').Request} req - The HTTP request object.
 * @param {import('express').Response} res - The HTTP response object.
 */
const subscribeToPlan = async (req, res) => {
  const { session } = res.locals.shopify;

  try {
    const { success, message, errors } = await validateSubscriptionCoupon(req.body);
    if (!success) {
      return res
        .status(apiResponseCodes.VALIDATION_ERROR)
        .json({ errors, message: message || apiResponseCodes.VALIDATION_ERROR });
    }

    const { slug: planSlug, coupon: couponCode } = req.body;

    const { appSubscription, confirmationUrl, isFreeSubscription } = await subscriptionCreate(
      session,
      planSlug,
      couponCode
    );

    return res.send({
      app_subscription: appSubscription,
      confirmation_url: confirmationUrl,
      is_free_subscription: isFreeSubscription,
      message: "Subscription successful",
    });
  } catch (error) {
    captureException(error, session.shop);
    return res
      .status(error instanceof Error ? 400 : 500)
      .send({ error: error instanceof Error ? error.message : "Internal server error" });
  }
};

export { getCheckoutPageData, getSubscriptions, subscribeToPlan, validateCoupon };
