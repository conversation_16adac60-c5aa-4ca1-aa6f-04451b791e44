// eslint-disable-next-line import/no-extraneous-dependencies
import {
  appSubscriptionIntervals,
  discountTypes,
  planIntervals,
  planTypes,
  subscriptionIntervals,
} from "easyflow-enums";
import lodash from "lodash";
import { serializeCouponData } from "../serializers/coupon.serializer.js";

const { isEmpty, get, pick } = lodash;

/**
 *
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} plan
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan | null} activePlan
 * @returns
 */
export const calculateCouponDiscount = (plan, activePlan = null) => {
  const price = parseFloat(plan.price.toFixed(2));
  const duration = plan.meta?.duration || 1;
  const discount = parseFloat(plan.discount.toFixed(2));

  const totalPrice = parseFloat((price * duration).toFixed(2));
  const finalPrice = parseFloat((totalPrice - discount).toFixed(2));

  const data = {
    price,
    duration,
    final_price: finalPrice,
    sub_total: totalPrice,
    discount,
    is_hundred_percent_coupon: plan?.coupon?.discount_type === discountTypes.PERCENT && plan?.coupon?.amount === 100,
    is_discount_applicable: false,
    coupon: plan?.coupon ? serializeCouponData(plan?.coupon) : null,
    interval: plan.interval,
  };

  if (!plan.coupon) {
    return data;
  }

  if (!calculatePlanIsUpgradable(plan, activePlan) && plan.id !== activePlan?.id && price === 0) {
    return {
      ...data,
      discount: 0.0,
      final_price: totalPrice,
    };
  }

  const couponAmount = parseFloat(get(plan, "coupon.amount", 0).toFixed(2));
  if (plan?.coupon?.discount_type === discountTypes.AMOUNT) {
    return {
      ...data,
      discount: couponAmount,
      final_price: totalPrice >= couponAmount ? parseFloat((totalPrice - couponAmount).toFixed(2)) : 0,
      is_discount_applicable: true,
    };
  }

  const discountAmount = parseFloat((couponAmount >= 100 ? totalPrice : (totalPrice * couponAmount) / 100).toFixed(2));
  return {
    ...data,
    discount: discountAmount,
    final_price: couponAmount <= 100 ? parseFloat((totalPrice - discountAmount).toFixed(2)) : 0,
    is_discount_applicable: true,
  };
};

/**
 *
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} plan
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan | null} activePlan
 *
 */
export const calculatePlanIsUpgradable = (plan, activePlan) => {
  if (isEmpty(activePlan)) {
    return true;
  }
  if (plan.slug === activePlan.slug || plan.price === 0) {
    return false;
  }

  return plan.price >= activePlan.price;
};

/**
 *
 * @param {string} type
 * @param {string} interval
 * @param {*} duration
 * @returns
 */
export const calculatePlanValidity = (type, interval, duration = 1) => {
  const d = new Date();
  const year = d.getFullYear();
  const month = d.getMonth();
  const day = d.getDate();

  switch (true) {
    case type === planTypes.FREE && interval === planIntervals.LIFETIME:
      return new Date(year + 5, month, day).toISOString();

    case type === planTypes.PRO && interval === planIntervals.YEARLY:
      return new Date(year + 1, month, day).toISOString();

    case type === planTypes.PRO && interval === planIntervals.USAGE:
      return new Date(year, month, day + duration * 30).toISOString();

    default:
      return new Date(year, month, day + 30).toISOString();
  }
};

/**
 * @param {{ name: string, value: any, display_name: string }[]} planPackageInfo
 * @param {{ name: string, value: any, display_name: string }[]} currentShopFeatures
 */
export const generatePlanFeaturesFromPackageInfo = (currentShopFeatures, planPackageInfo) => {
  const newFeatures = planPackageInfo.reduce((acc, item) => {
    // @ts-ignore
    acc[item.name] = item.value;
    return acc;
  }, {});

  return {
    ...currentShopFeatures,
    ...newFeatures,
  };
};

/**
 *
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @returns
 */
export const serializeShopSubscriptionPlanData = (plan) => ({
  ...pick(plan, ["id", "name", "slug", "type", "interval", "price", "discount", "meta"]),
  price: parseFloat(plan.price.toFixed(2)),
  discount: parseFloat(plan.discount.toFixed(2)),
  finalPrice: parseFloat(plan.final_price.toFixed(2)),
  duration: plan?.meta?.duration || 1,
  cappedAmount: plan?.meta?.capped_amount || null,
  trialDays: plan?.trial_days,
});

/**
 *
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @returns
 */
export const checkIfIsFreeSubscription = (plan) =>
  plan.type === planTypes.FREE || plan.final_price === 0 || plan.final_price === plan.discount;

/**
 *
 * @param {string} url
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @returns
 */
export const generateSubscriptionReturnUrl = (url, plan) => {
  const newUrl = new URL(`${url}`);
  newUrl.searchParams.append("plan", plan.slug);
  return newUrl;
};

/**
 *
 * @param {string} interval
 * @returns
 */
const generateShopifySubscriptionPlanInterval = (interval) => {
  switch (interval) {
    case planIntervals.YEARLY:
      return appSubscriptionIntervals.ANNUAL;
    default:
      return appSubscriptionIntervals.EVERY_30_DAYS;
  }
};

/**
 *
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @returns
 */
const calculateCappedAmount = (plan) => parseFloat(get(plan.meta, "capped_amount.amount", 0)).toFixed(2);

/**
 *
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @returns
 */
const generateTerms = (plan) => {
  let terms = `You will be charged ${plan.final_price}`;
  if (plan.interval === planIntervals.USAGE) {
    terms += ` for "${plan.name}" plan for ${plan.meta?.duration} months`;
  }

  return terms;
};

/**
 *
 * @param {import("../../types/SubscriptionPlanCoupon.js").ISubscriptionPlanCoupon | null | undefined} coupon
 * @returns
 */
export const calculateDiscountAfterCoupon = (coupon = null) => {
  let data = null;
  if (!isEmpty(coupon)) {
    const couponAmount = get(coupon, "amount", 0);
    data = {
      value: {
        amount: get(coupon, "discount_type") === discountTypes.AMOUNT ? couponAmount : null,
        percentage: get(coupon, "discount_type") === discountTypes.PERCENT ? couponAmount / 100 : null,
      },
    };
    const discountLimit = get(coupon, "discount_limit_duration", null);
    if (discountLimit) {
      data = { ...data, durationLimitInIntervals: discountLimit };
    }
  }
  return data;
};

/**
 *
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} plan
 * @param {import("../../types/SubscriptionPlanCoupon.js").ISubscriptionPlanCoupon | null | undefined} coupon
 * @returns
 */
export const generateSubscriptionLineItems = (plan, coupon) => {
  const lineItems = [];
  const updatedPlan = { ...plan };

  if (coupon && !updatedPlan.coupon) {
    updatedPlan.coupon = coupon;
  }

  if (updatedPlan.interval !== subscriptionIntervals.USAGE) {
    lineItems.push({
      plan: {
        appRecurringPricingDetails: {
          price: {
            amount: updatedPlan.price,
            currencyCode: updatedPlan.currency || "USD",
          },
          interval: generateShopifySubscriptionPlanInterval(updatedPlan.interval),
          discount: updatedPlan.coupon ? calculateDiscountAfterCoupon(updatedPlan.coupon) : null,
        },
      },
    });
  }
  if (updatedPlan.interval === subscriptionIntervals.USAGE) {
    lineItems.push({
      plan: {
        appUsagePricingDetails: {
          cappedAmount: {
            amount: calculateCappedAmount(plan),
            currencyCode: updatedPlan.currency || "USD",
          },
          terms: generateTerms(plan),
        },
      },
    });
  }
  return lineItems;
};

/**
 *
 * @param {IShop} shop
 * @param {string} couponCode
 */
export const updateShopCouponMeta = (shop, couponCode = "") => {
  if (shop) {
    let newMeta = shop.meta || {};
    newMeta = {
      ...newMeta,
      coupon_code: couponCode,
    };
    return {
      ...shop,
      meta: newMeta,
    };
  }
  return null;
};
