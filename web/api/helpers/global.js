/* eslint-disable import/no-relative-packages */
// eslint-disable-next-line import/no-extraneous-dependencies
import { planIntervals } from "easyflow-enums";
import lodash from "lodash";
import { customizationPageSkeleton } from "../../frontend/static.skeleton/customization.js";
import { homepageSkeletonDiv } from "../../frontend/static.skeleton/home.js";
import { pricingSkeletonPage } from "../../frontend/static.skeleton/pricing.js";
import { settingsPageSkeleton } from "../../frontend/static.skeleton/settings.js";

const { omit } = lodash;

/**
 *
 * @param {string} interval
 * @returns
 */
const prepareIntervalText = (interval) => {
  switch (interval) {
    case planIntervals.YEARLY:
      return "year";
    default:
      return "month";
  }
};

/**
 *
 * @param {string} interval
 * @param {number} totalPrice
 * @param {number} months
 * @returns
 */
const preparePlanDescription = (interval, totalPrice, months = 1) => {
  switch (interval) {
    case planIntervals.MONTHLY:
      return "Billed Monthly";
    case planIntervals.YEARLY:
      return "Billed Yearly";
    case planIntervals.USAGE:
      return `Billed $${totalPrice.toFixed(2)} for ${months} months`;
    default:
      return null;
  }
};

/**
 *
 * @param {URLSearchParams} searchParams
 */
const getQueryFromUrlSearchParam = (searchParams) => {
  const queries = Object.fromEntries(searchParams.entries());

  return omit(queries, ["embedded", "hmac", "host", "locale", "session", "shop", "timestamp", "id_token"]);
};

/**
 *
 * @param {object} conditions
 */
export const getSanitizedConditions = (conditions) => {
  // Replace undefined values with null
  const sanitizedConditions = Object.fromEntries(
    Object.entries(conditions).map(([key, value]) => [key, value === undefined ? null : value])
  );
  return sanitizedConditions;
};

const serializeValidationErrors = (errorObj) => {
  if (typeof errorObj !== "object" || errorObj === null) {
    return errorObj;
  }

  if (Array.isArray(errorObj)) {
    return errorObj.map(serializeValidationErrors);
  }

  return Object.entries(errorObj).reduce((acc, [key, value]) => {
    if (key === "_errors") {
      return acc;
    }

    if (typeof value === "object" && value !== null && "_errors" in value) {
      acc[key] = value._errors;
    } else {
      acc[key] = serializeValidationErrors(value);
    }
    return acc;
  }, {});
};

/**
 * Safely minifies HTML content by removing unnecessary whitespace while preserving structure
 * @param {string} html
 * @returns {string}
 */
const minifyHtml = (html) =>
  html
    .replace(/\s{2,}/g, " ") // Replace multiple spaces with single space
    .replace(/>\s+</g, "><") // Remove whitespace between tags
    .replace(/\s+>/g, ">") // Remove whitespace before closing tags
    .replace(/<\s+/g, "<") // Remove whitespace after opening tags
    .trim(); // Remove leading/trailing whitespace
/**
 *
 * @param {string} indexContent
 * @param {*} params
 * @returns
 */
const getPageStaticSkeletonContent = (indexContent, params) => {
  let pageContent = indexContent.toString(); // Start with the base content
  if (Object.keys(params).length > 0) {
    if (params["0"] === "") {
      pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", homepageSkeletonDiv);
    } else if (params["0"] === "customization") {
      pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", customizationPageSkeleton);
    } else if (params["0"] === "pricing") {
      pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", pricingSkeletonPage);
    } else if (params["0"] === "settings") {
      pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", settingsPageSkeleton);
    } else {
      pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", homepageSkeletonDiv);
    }
  } else {
    pageContent = pageContent.replace("%STATIC_LOADER_DIV_PLACEHOLDER%", homepageSkeletonDiv);
  }
  return minifyHtml(pageContent.toString());
};

export {
  getPageStaticSkeletonContent,
  getQueryFromUrlSearchParam,
  prepareIntervalText,
  preparePlanDescription,
  serializeValidationErrors,
};
