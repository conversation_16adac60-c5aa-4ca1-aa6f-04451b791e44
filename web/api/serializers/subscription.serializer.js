// eslint-disable-next-line import/no-extraneous-dependencies
import { planIntervals, planTypes, subscriptionIntervals } from "easyflow-enums";
// eslint-disable-next-line import/no-cycle
import lodash from "lodash";
import { prepareIntervalText, preparePlanDescription } from "../helpers/global.js";
import {
  calculateCouponDiscount,
  calculatePlanIsUpgradable,
  calculatePlanValidity,
  checkIfIsFreeSubscription,
  generatePlanFeaturesFromPackageInfo,
  serializeShopSubscriptionPlanData,
} from "../helpers/subscription.helper.js";

const { get } = lodash;

/**
 *
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan[]} plans
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} activePlan
 *
 */
export const subscriptionPlanSerializer = (plans, activePlan) =>
  plans.map((plan) => serializeSinglePlan(plan, activePlan, true));

/**
 *
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan} plan
 * @param {import('../../types/subscriptionPlan.js').ISubscriptionPlan | null} activePlan
 * @param {boolean} withFeatures
 *
 */
export const serializeSinglePlan = (plan, activePlan = null, withFeatures = false) => {
  const couponDiscount = calculateCouponDiscount(plan, activePlan);
  return {
    ...plan,
    ...couponDiscount,
    is_subscribed: plan.id === activePlan?.id,
    is_upgradable: calculatePlanIsUpgradable(plan, activePlan),
    is_free: plan.type === planTypes.FREE,
    is_monthly: plan.interval === subscriptionIntervals.MONTHLY,
    is_yearly: plan.interval === subscriptionIntervals.YEARLY,
    interval_text: prepareIntervalText(plan.interval),
    description: preparePlanDescription(plan.interval, couponDiscount.final_price, plan.meta?.duration),
  };
};

/**
 *
 * @param {IShop | undefined} shop
 * @param {import("../../types/subscriptionPlan.js").ISubscriptionPlan} subscriptionPlan
 * @returns
 */
export const serializeShopPlanData = (shop, subscriptionPlan, activeSubscription = null) => {
  const subscriptionId = get(activeSubscription, "id", null);
  const subscriptionStatus = get(activeSubscription, "status", null);
  const subscriptionActivatedAt = activeSubscription ? new Date(activeSubscription?.createdAt).toISOString() : null;

  let planData = {
    planSlug: get(subscriptionPlan, "slug"),
    planExpireDate: calculatePlanValidity(subscriptionPlan.type, subscriptionPlan.interval),
    planData: serializeShopSubscriptionPlanData(subscriptionPlan),
    planFeatures: generatePlanFeaturesFromPackageInfo(shop?.planFeatures, subscriptionPlan?.package_info),
    appSubscriptionId: subscriptionId,
    appSubscriptionData: get(subscriptionPlan, "slug") !== `free-${planIntervals.LIFETIME}` ? activeSubscription : null,
    planStatus: subscriptionStatus,
    subscribedAt: subscriptionActivatedAt,
  };
  if (get(subscriptionPlan, "type") === planTypes.FREE) {
    planData = {
      ...planData,
      // @ts-ignore
      isSubscriptionActive: false,
    };
  }
  if (get(subscriptionPlan, "type") !== planTypes.FREE && checkIfIsFreeSubscription(subscriptionPlan)) {
    planData = {
      ...planData,
      // @ts-ignore
      isSubscriptionActive: !!get(subscriptionPlan, "status", false),
    };
  }
  return planData;
};
