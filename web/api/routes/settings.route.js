import express from "express";
import {
  getCheckoutPageData,
  getSubscriptions,
  subscribeToPlan,
  validateCoupon,
} from "../controllers/settings.controller.js";

const router = express.Router();

// Define a route to get all subscriptions
router.get("/subscription", getSubscriptions);
router.get("/checkout/:slug", getCheckoutPageData);
router.post("/validate-coupon", validateCoupon);
router.post("/subscribe-to-plan", subscribeToPlan);

export default router;
