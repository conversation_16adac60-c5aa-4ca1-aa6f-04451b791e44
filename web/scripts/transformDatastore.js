import datastore from '../utilities/datastore.js';

/** @typedef {import('@google-cloud/datastore/build/src/query.js').RunQueryInfo} RunQueryInfo */

/** @type {QueryResponse<ISettings>} */
const [settings] = await datastore.createQuery('Settings').run();

/** @type {QueryResponse<{ isActive: boolean }>} */
const [subscriptions] = await datastore.createQuery('Subscription').run();

const shopNames = [
  ...new Set([
    ...settings.map((result) => result[datastore.KEY]?.name),
    ...subscriptions.map((result) => result[datastore.KEY]?.name),
  ]),
];

const entities = shopNames.flatMap((shop) =>
  shop
    ? {
        key: datastore.key(['Shop', shop]),
        data: {
          settings: settings.find((result) => result[datastore.KEY]?.name === shop),
          isSubscriptionActive: subscriptions.find((result) => result[datastore.KEY]?.name === shop)
            ?.isActive,
        },
      }
    : [],
);

await datastore.upsert(entities);
