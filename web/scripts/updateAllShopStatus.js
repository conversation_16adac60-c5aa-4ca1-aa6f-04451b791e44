import lodash from 'lodash';
// eslint-disable-next-line import/no-extraneous-dependencies
import { shopStatus } from 'easyflow-enums';
import datastore, { setShopProperties } from '../utilities/datastore.js';
import { getAppHandle, loadSession } from '../utilities/shopify.js';

const { get } = lodash;

const updateAllShopStatus = async () => {
  const existingAllShops = await getShopNames();
  console.log('allShopsCount', existingAllShops.length);
  const { activeCount, closedCount, uninstalledCount, inActiveCount } =
    await updateShopsStatus(existingAllShops);

  console.log('activeCount', activeCount);
  console.log('closedCount', closedCount);
  console.log('uninstalledCount', uninstalledCount);
  console.log('inActiveCount', inActiveCount);
};

const updateShopsStatus = async (/** @type {any} */ existingAllShops) => {
  let activeCount = 0;
  let closedCount = 0;
  let inActiveCount = 0;
  let uninstalledCount = 0;

  for (let i = 0; i < existingAllShops.length; i++) {
    const currentShop = existingAllShops[i];

    console.log(`Processing shop ${i + 1}/${existingAllShops.length}: ${currentShop?.key}`);

    // eslint-disable-next-line no-await-in-loop
    const session = await loadSession(currentShop?.key, 'load shop session');
    if (!session) {
      console.log(`Session not found for shop: ${currentShop?.key}`);
      // @ts-ignore
      // eslint-disable-next-line no-await-in-loop
      await setShopProperties(
        { status: shopStatus.INACTIVE },
        currentShop?.info,
        currentShop?.key,
        true,
      );
      inActiveCount++;
    } else {
      try {
        // eslint-disable-next-line no-await-in-loop
        const shopAppHandle = await getAppHandle(session);
        // eslint-disable-next-line no-await-in-loop
        await setShopProperties(
          // @ts-ignore
          { status: shopStatus.ACTIVE, appHandle: shopAppHandle },
          currentShop?.info,
          currentShop?.key,
          true,
        );
        activeCount++;
        console.log(i + 1, `Shop -- ${currentShop?.key} -- ACTIVE.`);
      } catch (err) {
        // @ts-ignore
        const storeStatus = shopStatus.getShopStatusByResponseCode(err?.response?.code);
        // @ts-ignore
        // eslint-disable-next-line no-await-in-loop
        await setShopProperties({ status: storeStatus }, currentShop?.info, currentShop?.key, true);
        if (storeStatus === shopStatus.CLOSED) {
          closedCount++;
          console.log(i + 1, `Shop -- ${currentShop?.key} -- CLOSED.`);
        }
        if (storeStatus === shopStatus.UNINSTALLED) {
          uninstalledCount++;
          console.log(i + 1, `Shop -- ${currentShop?.key} -- UNINSTALLED.`);
        }
      }
    }

    // Add a delay after every 50 iterations
    if ((i + 1) % 10 === 0) {
      console.log('Pausing for 2 seconds...');
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
    }
  }

  console.log('Processing complete.');
  return { activeCount, closedCount, uninstalledCount, inActiveCount };
};

/**
 * Function to get all unique shop names from Datastore
 * @returns {Promise<string[]>} Array of unique shop names
 */
const getShopNames = async () => {
  const shopNamesSet = new Set();
  let query = datastore.createQuery('Shop').limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.KEY]);
      if (shopKey) shopNamesSet.add({ key: get(shopKey, 'name'), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
};

updateAllShopStatus();
