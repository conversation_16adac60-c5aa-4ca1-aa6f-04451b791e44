import _ from "lodash";
import { getShopProperties, setShopProperties } from "../utilities/datastore.js";
import { cancelSubscription, getActiveSubscriptions, loadSession } from "../utilities/shopify.js";

const shopDomain = "";

const cancelShopSubscriptionsByShopDomain = async () => {
  const session = await loadSession(shopDomain, "cancel shop subscription");
  if (!session) {
    console.log("no session found");
    return;
  }

  const [shopProperties, shopActiveSubscription] = await Promise.all([
    getShopProperties(session?.shop),
    getActiveSubscriptions(session),
  ]);

  if (!_.isEmpty(shopActiveSubscription)) {
    await cancelSubscription(session, shopActiveSubscription[0].id);
  }

  setShopProperties({ isSubscriptionActive: false }, shopProperties, session?.shop, false);

  // no need to update shop properties (isSubscriptionActive and isOn18DollarPlan) because when subscription cancelled webhook hits , it will delete them.
};

cancelShopSubscriptionsByShopDomain();
