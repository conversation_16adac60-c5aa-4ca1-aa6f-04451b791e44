import { PropertyFilter } from '@google-cloud/datastore';

import datastore from '../utilities/datastore.js';

const transaction = datastore.transaction();
await transaction.run();

/** @type {QueryResponse<IShop>} */
const [shops] = await transaction
  .createQuery('Shop')
  // Uncomment the following line to only set the property for a specific shop
  .filter(new PropertyFilter('__key__', '=', datastore.key(['Shop', 'c0f33c-2.myshopify.com'])))
  .run();

const updates = shops.map((shop) => {
  const { [datastore.KEY]: key, ...updatedShop } = shop;
  return { key, data: { ...updatedShop, isOldSubscription: true } };
});

transaction.update(updates);

await transaction.commit();

console.log(updates.length, "shops' property was successfully set");
