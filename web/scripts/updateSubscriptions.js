import { PropertyFilter } from '@google-cloud/datastore';

import datastore from '../utilities/datastore.js';

const transaction = datastore.transaction();
await transaction.run();

/** @type {QueryResponse<IShop>} */
const [subscriptions] = await transaction
  .createQuery('Shop')
  .filter(new PropertyFilter('isSubscriptionActive', '=', true))
  .run();

const updates = (
  await Promise.all(
    subscriptions.map(async (shopProperties) => {
      const newShop = {
        key: shopProperties[datastore.KEY],
        data: { ...shopProperties, isOnOldSubscriptionPlan: true },
      };

      delete newShop.data[datastore.KEY];

      return newShop;
    }),
  )
).filter((subscriptionId) => subscriptionId);

transaction.update(updates);

await transaction.commit();

console.log(updates.length, 'subscriptions were updated.');
