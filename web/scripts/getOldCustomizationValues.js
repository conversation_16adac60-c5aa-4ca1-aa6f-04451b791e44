import datastore from "../utilities/datastore.js";

/** @type {QueryResponse<StyleOptions>} */
const [customizationEntities] = await datastore.createQuery("Styles").run();

customizationEntities.forEach((entity) => {
  const { cssCustomizations } = entity;

  if (cssCustomizations && Object.keys(cssCustomizations).length) {
    const customizationsString = JSON.stringify(cssCustomizations);

    if (customizationsString.includes("border-width") || customizationsString.includes("0 0 0")) {
      console.log(`${entity[datastore.KEY]?.parent?.name}:`, cssCustomizations);
    }
  }
});
