import { loadSession, sendQuery } from "../utilities/shopify.js";

const shop = "cremationcanada-com.myshopify.com";

const session = await loadSession(shop, "getting product metafields");
if (!session) {
  process.exit(1);
}

const metafields = await sendQuery(
  `{
    product(id: "gid://shopify/Product/364160687") {
      metafields(first: 10) {
        nodes {
          id,
          namespace,
          key,
          value,
          reference { ... on Metaobject { type, fields { key, type, value } } }
          references(first: 10) {
            nodes { ... on Metaobject { type, handle, fields { key, type, value } }  }
          }
        }
      }
    }
  }`,
  session
);

console.log(JSON.stringify(metafields, null, 2));

// const metaobject = await sendQuery(
//   `{
//     metaobject(id: "gid://shopify/Metaobject/19577078073") {
//       id
//       type
//       displayName
//       fields { key, type, value }
//     }
//   }`,
//   session,
// );

// console.log(JSON.stringify(metaobject, null, 2));
