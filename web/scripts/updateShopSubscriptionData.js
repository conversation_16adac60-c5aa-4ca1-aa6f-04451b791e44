import { subscriptionUpdate } from "../api/services/subscription.service.js";
import { getShopProperties, setShopProperties } from "../utilities/datastore.js";
import { loadSession } from "../utilities/shopify.js";

const shop = "";

const updateShopSubscriptionData = async () => {
  const session = await loadSession(shop, "updating shop subscription data");
  if (!session) {
    process.exit(1);
  }

  const shopProperties = await getShopProperties(session.shop);

  const newShopProperties = {
    ...shopProperties,
    isSubscriptionActive: true,
  };
  await setShopProperties(newShopProperties, shopProperties, session.shop, true);

  const appSubscription = {
    status: "ACTIVE",
    name: "pro-monthly",
  };

  await subscriptionUpdate(shop, newShopProperties, appSubscription);
};

updateShopSubscriptionData();
