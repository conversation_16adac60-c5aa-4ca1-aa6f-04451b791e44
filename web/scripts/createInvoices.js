import createInvoice from '../utilities/invoice.js';
import shopify, { loadSession } from '../utilities/shopify.js';

const shopOrderIds = {
  shop: [5751263166805],
};

/** @type {{[shop: string]: 'create' | 'paid' | 'fulfilled'}} */
const shopTriggers = {
  shop: 'fulfilled',
};

Promise.all(
  Object.entries(shopOrderIds).map(async ([shop, orderIds]) => {
    const session = await loadSession(shop, 'creating invoices');
    if (!session) {
      return undefined;
    }

    return Promise.all(
      orderIds.map(async (orderId) => {
        /** @type {Order | undefined} */
        const order = await shopify.api.rest.Order.find({ session, id: orderId });

        if (!order) {
          console.error('Order', orderId, 'was not found for', shop);
          return;
        }

        const trigger = shopTriggers[shop];
        if (!trigger) {
          console.error('Could not find shop trigger for', shop);
        }

        createInvoice(order, shop, trigger);
      }),
    );
  }),
);
