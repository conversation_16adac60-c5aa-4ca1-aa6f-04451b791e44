import fs from 'fs';

import { Datastore } from '@google-cloud/datastore';

const szamlazzHUDatastore = new Datastore({
  namespace: 'szamlazz-hu',
  projectId: 'szamlazz-hu-app',
});

const billingoDatastore = new Datastore({ namespace: 'billingo', projectId: 'billingo-app' });

writeShopDataToCSV(szamlazzHUDatastore, 'szamlazzHU');
writeShopDataToCSV(billingoDatastore, 'billingo');

/**
 * @param {import('@google-cloud/datastore').Datastore} datastore
 * @param {string} datastoreName
 */
async function writeShopDataToCSV(datastore, datastoreName) {
  /** @type {QueryResponse<IShop>} */
  const [shops] = await datastore.createQuery('Shop').run();

  const keys = [
    ...new Set(shops.flatMap((shop) => /** @type {(keyof IShop)[]} */ (Object.keys(shop)))),
  ]
    .filter((key) => !['activeSubscriptionId', 'settings', 'lastError'].includes(key))
    .sort();

  const data = [
    ['id', ...keys].join(','),
    ...shops.map((shopProperties) =>
      [
        shopProperties[datastore.KEY]?.name,
        ...keys.map((key) => {
          const value = shopProperties[key];

          if (value instanceof Date) {
            const formatter = new Intl.DateTimeFormat([], {
              timeZone: 'Europe/Budapest',
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
            });

            return formatter.format(value).replace(',', '');
          }

          return value instanceof Date ? value?.toISOString() : value;
        }),
      ].join(','),
    ),
  ].join('\n');

  fs.writeFileSync(new URL(`../../${datastoreName}Shops.csv`, import.meta.url), data);
}
