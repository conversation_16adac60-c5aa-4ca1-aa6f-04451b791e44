/**
 * <PERSON><PERSON><PERSON> to check datastore connections
 *
 * This script verifies connections to:
 * 1. Google Cloud Datastore
 * 2. PostgreSQL database (via Prisma) - if DATABASE_URL is configured
 *
 * Run with: node web/scripts/checkDatastoreConnections.js
 * Or with environment variables: env-cmd -f .env.development node web/scripts/checkDatastoreConnections.js
 */

import { captureException } from "../utilities/analytics.js";
import datastore from "../utilities/datastore.js";

// Check if DATABASE_URL is defined before importing prisma
const isDatabaseConfigured = !!process.env.DATABASE_URL;
// Define prisma with a type annotation to avoid TypeScript errors
/** @type {any} */
let prisma;
if (isDatabaseConfigured) {
  try {
    prisma = await import("../prisma/prisma.client.js").then((module) => module.default);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.warn("Failed to import Prisma client:", errorMessage);
  }
}

/**
 * Check Google Cloud Datastore connection
 * @returns {Promise<{success: boolean, message: string, data?: any}>}
 */
async function checkDatastoreConnection() {
  try {
    console.log("Checking Google Cloud Datastore connection...");

    // Try to run a simple query to check connection
    const query = datastore.createQuery("Shop").limit(1);
    const [shops] = await query.run();

    if (shops && shops.length > 0) {
      return {
        success: true,
        message: "Successfully connected to Google Cloud Datastore",
        data: {
          sampleShop: shops[0],
          projectId: datastore.projectId,
          namespace: datastore.namespace,
        },
      };
    }
    return {
      success: true,
      message: "Connected to Google Cloud Datastore, but no Shop entities found",
      data: {
        projectId: datastore.projectId,
        namespace: datastore.namespace,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error connecting to Google Cloud Datastore:", errorMessage);
    return {
      success: false,
      message: `Failed to connect to Google Cloud Datastore: ${errorMessage}`,
      data: {
        error: errorMessage,
      },
    };
  }
}

/**
 * Check PostgreSQL database connection via Prisma
 * @returns {Promise<{success: boolean, message: string, data?: any}>}
 */
async function checkPrismaConnection() {
  // Check if DATABASE_URL is configured
  if (!isDatabaseConfigured) {
    console.log("Skipping PostgreSQL database check - DATABASE_URL is not configured");
    return {
      success: false,
      message: "DATABASE_URL environment variable is not set. PostgreSQL connection check skipped.",
      data: {
        databaseUrl: "Not configured",
      },
    };
  }

  // Check if Prisma client was successfully imported
  if (!prisma) {
    console.log("Skipping PostgreSQL database check - Prisma client could not be loaded");
    return {
      success: false,
      message: "Prisma client could not be loaded. PostgreSQL connection check skipped.",
      data: {
        databaseUrl: process.env.DATABASE_URL ? process.env.DATABASE_URL.replace(/:[^:]*@/, ":****@") : "Not available",
      },
    };
  }

  try {
    console.log("Checking PostgreSQL database connection via Prisma...");

    // Try to query the database to check connection
    // First check if we can connect
    await prisma.$connect();

    // Then try to get some data
    // Adjust this query based on your actual database schema
    let subscriptionPlansCount;
    try {
      subscriptionPlansCount = await prisma.subscription_plans.count();
    } catch (countError) {
      // If the specific table doesn't exist, try a more generic query
      console.log("Could not count subscription_plans, trying a generic query instead");
      await prisma.$queryRaw`SELECT 1`;
      subscriptionPlansCount = "Table not found, but database connection is working";
    }

    return {
      success: true,
      message: "Successfully connected to PostgreSQL database via Prisma",
      data: {
        subscriptionPlansCount,
        databaseUrl: process.env.DATABASE_URL?.replace(/:[^:]*@/, ":****@"), // Hide password in connection string
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Error connecting to PostgreSQL database via Prisma:", errorMessage);
    return {
      success: false,
      message: `Failed to connect to PostgreSQL database via Prisma: ${errorMessage}`,
      data: {
        error: errorMessage,
      },
    };
  } finally {
    // Always disconnect from Prisma if it exists
    if (prisma && typeof prisma.$disconnect === "function") {
      await prisma.$disconnect();
    }
  }
}

/**
 * Main function to check all datastore connections
 */
async function checkAllConnections() {
  console.log("Starting datastore connection checks...");
  console.log("----------------------------------------");

  try {
    // Check Google Cloud Datastore
    const datastoreResult = await checkDatastoreConnection();
    console.log("\nGoogle Cloud Datastore:");
    console.log(`Status: ${datastoreResult.success ? "Connected ✅" : "Failed ❌"}`);
    console.log(`Message: ${datastoreResult.message}`);
    if (datastoreResult.data) {
      console.log("Details:");
      console.log(JSON.stringify(datastoreResult.data, null, 2));
    }

    console.log("\n----------------------------------------\n");

    // Check PostgreSQL via Prisma
    const prismaResult = await checkPrismaConnection();
    console.log("PostgreSQL Database (Prisma):");
    console.log(`Status: ${prismaResult.success ? "Connected ✅" : "Failed ❌"}`);
    console.log(`Message: ${prismaResult.message}`);
    if (prismaResult.data) {
      console.log("Details:");
      console.log(JSON.stringify(prismaResult.data, null, 2));
    }

    console.log("\n----------------------------------------");
    console.log("Connection check summary:");
    console.log(`- Google Cloud Datastore: ${datastoreResult.success ? "Connected ✅" : "Failed ❌"}`);
    console.log(`- PostgreSQL Database: ${prismaResult.success ? "Connected ✅" : "Failed ❌"}`);
    console.log("----------------------------------------");

    // Determine overall success - for PostgreSQL, we only consider it a failure if DATABASE_URL is set but connection failed
    const postgresSuccess = !isDatabaseConfigured || prismaResult.success;

    // Exit with appropriate code - only require Datastore to be successful if DATABASE_URL isn't set
    if (datastoreResult.success && postgresSuccess) {
      if (isDatabaseConfigured && !prismaResult.success) {
        console.log("Google Cloud Datastore connection is working, but PostgreSQL connection failed.");
        console.log("Since DATABASE_URL is set, this is considered a partial success.");
      } else {
        console.log("All configured datastore connections are working properly! 🎉");
      }
      process.exit(0);
    } else {
      console.error("One or more required datastore connections failed! ⚠️");
      process.exit(1);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Unexpected error during connection checks:", errorMessage);
    try {
      // Only try to capture exception if it's a proper Error object
      if (error instanceof Error) {
        captureException(error, "checkDatastoreConnections");
      }
    } catch (captureError) {
      console.error(
        "Failed to capture exception:",
        captureError instanceof Error ? captureError.message : String(captureError)
      );
    }
    process.exit(1);
  }
}

// Run the checks
checkAllConnections().catch((error) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  console.error("Fatal error:", errorMessage);
  process.exit(1);
});
