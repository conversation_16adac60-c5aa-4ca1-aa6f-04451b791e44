import _ from 'lodash';

import { HttpResponseError, Session } from '@shopify/shopify-api';

import datastore from '../utilities/datastore.js';
import { loadSession, sendQuery } from '../utilities/shopify.js';
import webhooks from '../webhooks.js';

const chunkSize = 30;

// Register missing webhooks for a specific shop
registerMissingWebhooks('c0f33c-2.myshopify.com');

// Register missing webhooks for all shops
// /** @type {QueryResponse<SessionParams>} */
// const [sessions] = await datastore.createQuery('Session').run();

// const sessionChunks = _.chunk(
//   sessions.map((sessionParams) => new Session(sessionParams)),
//   chunkSize,
// );

// for (let i = 0; i < sessionChunks.length; i++) {
//   // eslint-disable-next-line no-await-in-loop
//   await Promise.all(
//     sessionChunks[i].map((session) => registerMissingWebhooks(session.shop, session)),
//   );

//   console.log(`${chunkSize * i + sessionChunks[i].length}/${sessions.length} registered`);
// }

/* -------------------------------------- Helper functions -------------------------------------- */

/**
 * Register missing webhooks.
 *
 * @param {string} shop
 * @param {Session} [defaultSession]
 */
async function registerMissingWebhooks(shop, defaultSession = undefined) {
  const session = defaultSession || (await loadSession(shop, 'updating or creating webhooks'));
  if (!session) {
    return;
  }

  const excludedTopics = ['CUSTOMERS_DATA_REQUEST', 'CUSTOMERS_REDACT', 'SHOP_REDACT'];
  const topics = Object.keys(webhooks).filter((topic) => !excludedTopics.includes(topic));

  // If topic is not registered, register it.
  try {
    /**
     * @type {{
     *   webhookSubscriptions: {
     *     nodes: {
     *       id: string;
     *       topic: string;
     *       endpoint: { callbackUrl?: string; }
     *     }[]
     *   }
     * } | undefined}
     */
    const webhookResult = await sendQuery(
      `{
        webhookSubscriptions(first: 10) {
          nodes {
            id
            topic
            endpoint { ... on WebhookHttpEndpoint { callbackUrl } }
          }
        }
      }`,
      session,
    );

    await Promise.all(
      topics.flatMap(async (topic) => {
        const webhook = webhooks[topic];

        if (
          !('callbackUrl' in webhook) ||
          webhookResult?.webhookSubscriptions.nodes.find((node) => node.topic === topic)
        ) {
          return [];
        }

        const webhookSubscription = {
          callbackUrl: new URL(webhook.callbackUrl, process.env.HOST).href,
          format: 'JSON',
        };

        return sendQuery(
          `mutation WebhookSubscriptionCreateMutation(
            $topic: WebhookSubscriptionTopic!
            $webhookSubscription: WebhookSubscriptionInput!
          ) {
            webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
              userErrors { field, message }
            }
          }`,
          session,
          { webhookSubscription, topic },
        );
      }),
    );
  } catch (error) {
    if (
      error instanceof HttpResponseError &&
      (error.response.body?.errors === 'Not Found' ||
        error.response.body?.errors === 'Unavailable Shop')
    ) {
      return;
    }

    console.error(`An error occurred while creating the webhook subscription for ${session.shop}`);
    console.trace(error);
  }
}
