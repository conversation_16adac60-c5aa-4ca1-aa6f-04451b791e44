from datetime import datetime, timezone

from google.cloud import datastore

read_time = datetime(2023, 8, 21, 11, 50, 0, 0, timezone.utc)

client = datastore.Client(project="szamlazz-hu-app", namespace="szamlazz-hu")

query = client.query(kind="Shop")

# run query with PITR read time
iterator = query.fetch(read_time=read_time)
query.add_filter("currentPeriodInvoiceCount", "=", 1)

for result in iterator:
    print(
        result.key.name,
        result["currentPeriodStartedAt"]
        if "currentPeriodStartedAt" in result
        else None,
    )
