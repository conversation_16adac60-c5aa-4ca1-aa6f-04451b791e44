import prisma from '../prisma/prisma.client.js';
import { getAllShopInfo } from '../utilities/datastore.js';
import { loadSession, sendQuery } from '../utilities/shopify.js';

const main = async () => {
  const existingAllShops = await getAllShopInfo();
  console.log('allShopsCount', existingAllShops.length);
  await updateShopsWebhooksTable(existingAllShops);
};

const updateShopsWebhooksTable = async (/** @type {any} */ existingAllShops) => {
  const successfulUpdates = [];
  const failedUpdates = [];

  for (let i = 0; i < existingAllShops.length; i++) {
    const currentShop = existingAllShops[i];

    try {
      console.log(`Processing shop ${i + 1}/${existingAllShops.length}: ${currentShop?.key}`);

      // eslint-disable-next-line no-await-in-loop
      const session = await loadSession(currentShop?.key, 'load shop session');
      if (!session) {
        failedUpdates.push(currentShop?.key);
        throw new Error(`Session not found for shop: ${currentShop?.key}`);
      }

      // eslint-disable-next-line no-await-in-loop
      const webhooksRegistered = await getWebhooksSubscriptionsForShop(session);
      console.log('webhooksRegistered', webhooksRegistered);

      const reformateData = reformatWebhookData(webhooksRegistered, currentShop?.key);

      console.log('check', reformateData);

      // eslint-disable-next-line no-await-in-loop
      await saveWebhooksData(reformateData);

      successfulUpdates.push(session.shop); // Add to successful updates
    } catch (error) {
      console.error(`Error updating shop: ${currentShop?.key}`, error);
      failedUpdates.push(currentShop?.key); // Add to failed updates
    }

    // Add a delay after every 50 iterations
    if ((i + 1) % 50 === 0) {
      console.log('Pausing for 2 seconds...');
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
    }
  }

  console.log('Processing complete.');
  return { successfulUpdates, failedUpdates };
};

const getWebhooksSubscriptionsForShop = async (session) => {
  try {
    /**
     * @type {{
     *   webhookSubscriptions: {
     *     nodes: {
     *       id: string;
     *       topic: string;
     *       endpoint: { callbackUrl?: string; }
     *     }[]
     *   }
     * } | undefined}
     */
    const webhookResult = await sendQuery(
      `{
          webhookSubscriptions(first:10) {
            nodes {
              id
              topic
              endpoint {
                ... on WebhookHttpEndpoint { callbackUrl }
              }
              apiVersion {
                displayName
              }
              createdAt
              updatedAt
            }
          }
        }`,
      session,
    );
    return webhookResult?.webhookSubscriptions.nodes;
  } catch (error) {
    console.log('webhook get error from shopify =', error.message);
  }
};

/**
 * @typedef {Object} WebhookNode
 * @property {string} id
 * @property {string} topic
 * @property {{ callbackUrl?: string }} endpoint
 * @property {{ displayName: string }} apiVersion
 * @property {string} createdAt
 * @property {string} updatedAt
 */

/**
 * @param {WebhookNode[]} nodes
 * @param {string} shopDomain
 */
const reformatWebhookData = (nodes, shopDomain) =>
  nodes.map((node) => ({
    shop_domain: shopDomain,
    wh_subs_id: node.id,
    topic: node.topic,
    delivery_method: 'http',
    response: null,
    created_at: new Date(node.createdAt),
    updated_at: new Date(node.updatedAt),
  }));

const saveWebhooksData = async (webhookData) => {
  try {
    webhookData.map(async (webhook) => {
      await prisma.shop_webhooks.create({
        data: {
          ...webhook,
        },
      });
    });

    // Replace console.log with a logging utility if available
    console.log('All data have been seeded successfully.');
  } catch (error) {
    console.error('Error saving data:', error.message);
  }
};

main();
