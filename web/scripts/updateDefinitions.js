import _ from "lodash";

import { HttpResponseError, Session } from "@shopify/shopify-api";

import datastore from "../utilities/datastore.js";
import {
  loadSession,
  metafieldNamespace,
  metaobjectType,
  optionsMetafieldKey,
  preferencesMetafieldKey,
  ranksMetafield<PERSON>ey,
  sendQuery,
} from "../utilities/shopify.js";

const chunkSize = 30;

/* ----------------------------------- Metaobject definitions ----------------------------------- */

// Get and update metaobject definitions for a specific shop
getAndUpdateDefinitions("winspace-japan-online.myshopify.com");

// Get and update metaobject definitions for every shop
// /** @type {QueryResponse<SessionParams>} */
// const [sessions] = await datastore.createQuery('Session').run();

// const sessionChunks = _.chunk(
//   sessions.map((sessionParams) => new Session(sessionParams)),
//   chunkSize,
// );

// for (let i = 0; i < sessionChunks.length; i++) {
//   // eslint-disable-next-line no-await-in-loop
//   await Promise.all(
//     sessionChunks[i].map((session) => getAndUpdateDefinitions(session.shop, session)),
//   );

//   console.log(`${chunkSize * i + sessionChunks[i].length}/${sessions.length} updated`);
// }

/* ----------------------------------- Metafield definitions ------------------------------------ */

// Create a metafield definition for a specific shop
// createMetafieldDefinition('product-options-development.myshopify.com');

// Create metafield definitions for every shop
// /** @type {QueryResponse<SessionParams>} */
// const [sessions] = await datastore.createQuery('Session').run();

// const sessionChunks = _.chunk(
//   sessions.map((sessionParams) => new Session(sessionParams)),
//   chunkSize,
// );

// for (let i = 0; i < sessionChunks.length; i++) {
//   // eslint-disable-next-line no-await-in-loop
//   await Promise.all(
//     sessionChunks[i].map((session) => createMetafieldDefinition(session.shop, session)),
//   );

//   console.log(`${chunkSize * i + sessionChunks[i].length}/${sessions.length} updated`);
// }

/* -------------------------------------- Helper functions -------------------------------------- */

/**
 * Get and update all Metaobject definitions.
 *
 * @param {string} shop
 * @param {Session} [defaultSession]
 */
async function getAndUpdateDefinitions(shop, defaultSession = undefined) {
  const session = defaultSession || (await loadSession(shop, "updating metaobject definitions"));
  if (!session) {
    return;
  }

  // const metafieldDefinations = await getDefinition(session, "gid://shopify/MetafieldDefinition/28349038848");

  // console.log("metafieldDefinations", metafieldDefinations);

  const metaobjectDefinitionIds = await getMetaobjectDefinitions(session);

  const metaobjectDefinitionId = metaobjectDefinitionIds[0].id;
  // console.log(metaobjectDefinitionIds);
  console.log("id", metaobjectDefinitionIds[0]);

  // console.log("metaobjectDefinitionIds", metaobjectDefinitionIds[0].fieldDefinitions);

  // if (metaobjectDefinitionIds.length > 0) {
  //   const id = metaobjectDefinitionIds[0];
  //   await createDefination(session, id);
  // }

  // console.log("metaobjectDefinitionIds", metaobjectDefinitionIds);

  // await Promise.all(metaobjectDefinitionIds.map((id) => updateMetaobjectDefinitions(id, session)));

  if (metaobjectDefinitionId) {
    // await updateMetaobjectDefinitions(metaobjectDefinitionId, session);
  }
}

/**
 * Get all Metaobject definitions.
 *
 * @param {import('@shopify/shopify-api').Session} session
 */
async function getMetaobjectDefinitions(session) {
  try {
    /** @type {{ metaobjectDefinitions: { nodes: { id: string, name: string }[] } } | undefined} */
    const result = await sendQuery(
      `query { metaobjectDefinitions(first: 100) { nodes { id, name , 
        metaobjects(first: 100) {
          nodes {
            id
            type
            handle
            displayName
            fields {
              key
              value
            }
          }
        },
        fieldDefinitions {
          name, key, 
          validations {
            name,
            value,
            type
          }
        }
        } } }`,
      session
    );

    if (!result?.metaobjectDefinitions.nodes.length) {
      console.error(`Could not get metaobject definitions for ${session.shop}`);
      return [];
    }

    return result.metaobjectDefinitions.nodes.flatMap(({ id, name, metaobjects, fieldDefinitions }) =>
      name === "Option" ? { id: id, name: name, metaobjects: metaobjects, fieldDefinitions } : []
    );
  } catch (error) {
    if (
      error instanceof HttpResponseError &&
      (error.response.body?.errors === "Not Found" || error.response.body?.errors === "Unavailable Shop")
    ) {
      return [];
    }

    console.error(`An error occurred while getting the metaobject definitions for ${session.shop}`);
    console.trace(error);

    return [];
  }
}

const getMetafieldDefinitions = async (session) => {
  try {
    const query = `query {
      metafieldDefinitions(first: 250, ownerType: PRODUCT) {
        edges {
          node {
            id
            name
          }
        }
      }
    }`;

    const result = await sendQuery(query, session);

    if (!result?.metafieldDefinitions?.edges?.length) {
      console.error(`Could not get metafield definitions for ${session.shop}`);
      return [];
    }

    return result.metafieldDefinitions.edges.map((edge) =>
      edge.node.name === "EasyFlow Product options" ? edge.node : {}
    );
  } catch (error) {
    console.log("object", error.response);

    if (
      error instanceof Error &&
      error.response &&
      error.response.body?.errors?.message &&
      ["GraphQL Client: Not Found", "GraphQL Client: Unavailable Shop", "GraphQL Client: Payment Required"].includes(
        error.response.body.errors.message
      )
    ) {
      return;
    }

    console.error(`An error occurred while fetching metafield definitions for ${session.shop}`);
    console.trace(error);
  }
};

const createDefination = async (session, metaobjectDefinitionId) => {
  const optionsMetafieldDefinition = {
    name: "EasyFlow Product options",
    namespace: metafieldNamespace,
    key: optionsMetafieldKey,
    description: "List of options available for the product",
    type: "list.metaobject_reference",
    ownerType: "PRODUCT",
    validations: [{ name: "metaobject_definition_id", value: metaobjectDefinitionId }],
  };

  try {
    await sendQuery(
      `mutation MetafieldDefinitionCreateMutation($definition: MetafieldDefinitionInput!) {
        metafieldDefinitionCreate(definition: $definition) {
          userErrors { field, message }
        }
      }`,
      session,
      { definition: optionsMetafieldDefinition }
    );
  } catch (error) {
    if (
      error instanceof Error &&
      "response" in error &&
      // @ts-ignore
      (error.response.body?.errors.message === "GraphQL Client: Not Found" ||
        // @ts-ignore
        error.response.body?.errors.message === "GraphQL Client: Unavailable Shop" ||
        // @ts-ignore
        error.response.body?.errors.message === "GraphQL Client: Payment Required")
    ) {
      return;
    }

    console.error(`An error occurred while creating the metafield definition for ${session.shop}`);
    console.trace(error);
  }
};

const getDefinition = async (session, id) => {
  try {
    const query = `query getMetaFieldDefinition($metafieldDefinitionId: ID!) {
      metafieldDefinition(id: $metafieldDefinitionId) {
        id
        metafieldsCount
        name
        namespace
        key
        description
        validations {
          name
          value
          type
        }
      }
    }`;

    const variables = { metafieldDefinitionId: id };

    const result = await sendQuery(query, session, variables);

    return result?.metafieldDefinition || null;
  } catch (error) {
    if (
      error instanceof Error &&
      error.response &&
      error.response.body?.errors?.message &&
      ["GraphQL Client: Not Found", "GraphQL Client: Unavailable Shop", "GraphQL Client: Payment Required"].includes(
        error.response.body.errors.message
      )
    ) {
      return null;
    }

    console.error(`An error occurred while fetching the metafield definition for ${session.shop}`);
    console.trace(error);
  }
};

/**
 * Update a Metaobject definition.
 *
 * @param {string} definitionId
 * @param {import('@shopify/shopify-api').Session} session
 */
async function updateMetaobjectDefinitions(definitionId, session) {
  const definitionUpdate = {
    fieldDefinitions: [
      // {
      //   create: {
      //     key: "placeholderText",
      //     name: "Placeholder text",
      //     type: "single_line_text_field",
      //   },
      // },
      // {
      //   create: {
      //     key: 'minimumSelectionCount',
      //     name: 'Minimum number of selected values',
      //     type: 'number_integer',
      //   },
      // },
      // {
      //   create: {
      //     key: "isShowSelectedValues",
      //     name: "Show the selected values next to the option title",
      //     type: "boolean",
      //   },
      // },
      // {
      //   create: {
      //     key: 'minimumValue',
      //     name: 'The lower limit for the option value',
      //     type: 'number_integer',
      //   },
      // },
      // {
      //   create: {
      //     key: 'maximumValue',
      //     name: 'The upper limit for the option value',
      //     type: 'number_integer',
      //   },
      // },
      // {
      //   create: {
      //     key: "allowedFileTypes",
      //     name: "Allowed file types",
      //     type: "list.single_line_text_field",
      //   },
      // },
    ],
  };

  try {
    const result = await sendQuery(
      `mutation MetaobjectDefinitionUpdateMutation($definition: MetaobjectDefinitionUpdateInput!) {
        metaobjectDefinitionUpdate(id: "${definitionId}", definition: $definition) {
          metaobjectDefinition {
            id
            name
            displayNameKey
            fieldDefinitions {
              name
              key
              type {
                name
              }
            }
          }
          userErrors { field, message }
        }
      }`,
      session,
      { definition: definitionUpdate }
    );
    console.log("result", result.metaobjectDefinitionUpdate.metaobjectDefinition.fieldDefinitions);
  } catch (error) {
    console.error(`An error occurred while updating metaobject definitions for ${session.shop}`);
    console.trace(error);
  }
}

/**
 * Create a new metafield definition.
 *
 * @param {string} shop
 * @param {import('@shopify/shopify-api').Session} [defaultSession]
 */
async function createMetafieldDefinition(shop, defaultSession) {
  const session = defaultSession || (await loadSession(shop, "creating metafield definition"));
  if (!session) {
    return;
  }

  // Get metaobject definition IDs for the 'Options' metafield definition
  // const metaobjectDefinitionIds = await getMetaobjectDefinitions(session);

  // Get metaobject definition ID for the 'Option' metaobject type

  const definition = {
    name: "EasyFlow Product Option ranks",
    namespace: metafieldNamespace,
    key: ranksMetafieldKey,
    description: "Rank of option/sets to be displayed on the product page",
    type: "json",
    ownerType: "PRODUCT",
  };

  try {
    await sendQuery(
      `mutation MetafieldDefinitionCreateMutation($definition: MetafieldDefinitionInput!) {
        metafieldDefinitionCreate(definition: $definition) {
          userErrors { field, message }
        }
      }`,
      session,
      { definition }
    );
  } catch (error) {
    if (
      error instanceof Error &&
      "response" in error &&
      // @ts-ignore
      (error.response.body?.errors.message === "GraphQL Client: Not Found" ||
        // @ts-ignore
        error.response.body?.errors.message === "GraphQL Client: Unavailable Shop" ||
        // @ts-ignore
        error.response.body?.errors.message === "GraphQL Client: Payment Required")
    ) {
      return;
    }

    console.error(`An error occurred while creating the metafield definition for ${session.shop}`);
    console.trace(error);
  }
}
