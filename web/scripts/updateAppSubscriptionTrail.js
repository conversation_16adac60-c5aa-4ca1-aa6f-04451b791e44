import _ from "lodash";
import { getShopProperties, setShopProperties } from "../utilities/datastore.js";
import { extendsAppSubscriptionTrail, getActiveSubscriptions, loadSession } from "../utilities/shopify.js";

const shopDomain = "";

const updateAppSubscriptionsByShopDomain = async () => {
  const session = await loadSession(shopDomain, "cancel shop subscription");
  if (!session) {
    console.log("no session found");
    return;
  }

  const [shopProperties, shopActiveSubscription] = await Promise.all([
    getShopProperties(session?.shop),
    getActiveSubscriptions(session),
  ]);

  console.log("shopActiveSubscription", shopActiveSubscription);

  if (!_.isEmpty(shopActiveSubscription)) {
    try {
      const updatedSubscriptionData = await extendsAppSubscriptionTrail(session, shopActiveSubscription[0].id, 14);
      console.log("updatedSubscriptionData", updatedSubscriptionData);
      await setShopProperties({ appSubscriptionData: updatedSubscriptionData }, shopProperties, session?.shop, false);
    } catch (error) {
      console.log("trail extension failed", error);
    }
  }
};

updateAppSubscriptionsByShopDomain();
