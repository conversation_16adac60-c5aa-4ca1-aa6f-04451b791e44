import { loadSession, sendQuery } from "../utilities/shopify.js";

const shop = ".myshopify.com";

const testScript = async () => {
  const session = await loadSession(shop, "getting product");
  if (!session) {
    return;
  }
  try {
    const cursor = undefined;
    const paginationType = undefined;
    const titleQuery = undefined;

    /**
     * @type {{
     *   products: {
     *    edges: {
     *     cursor: string,
     *     node: {
     *       id: string,
     *       handle: string,
     *       title: string,
     *       featuredImage?: { url: string },
     *       variants: {
     *         edges: {
     *           node: {
     *             id: string,
     *             displayName: string,
     *             price: string,
     *             image?: { url: string }
     *           }
     *         }[]
     *       }
     *     }
     *    }[],
     *    pageInfo: IPageInfo
     *   }
     * } | undefined}
     */
    const productsResult = await sendQuery(
      `{
           products(
             ${paginationType === "before" ? "last" : "first"}: 5,
             sortKey: TITLE,
             ${cursor ? `${paginationType}: "${cursor}"` : ""},
             ${titleQuery ? `query: "title:${titleQuery}"` : ""}
           ) {
             edges {
               cursor,
               node {
                 id,
                 handle
                 title,
                 featuredImage { url } 
                 variants(first: 50) { edges { node { id, displayName, price, image { url } } } }
               }
             }
             pageInfo { hasNextPage, hasPreviousPage }
           }
         }`,
      session
    );
  } catch (error) {
    console.error(error);
  }
};

testScript();
