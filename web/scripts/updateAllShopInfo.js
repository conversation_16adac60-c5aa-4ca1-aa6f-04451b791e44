import lodash from 'lodash';
import datastore, { setShopProperties } from '../utilities/datastore.js';
import { getShopDetailsFromShopify, loadSession } from '../utilities/shopify.js';

const { get } = lodash;

const updateAllShopInfos = async () => {
  const existingAllShops = await getShopNames();
  const { successfulUpdates, failedUpdates } = await updateShopsInfo(existingAllShops);

  console.log('successfulUpdates', successfulUpdates);
  console.log('failedUpdates', failedUpdates);
};

// const updateShopsInfo = async (/** @type {any} */ existingAllShops) => {
//   // @ts-ignore
//   const successfulUpdates = [];
//   // @ts-ignore
//   const failedUpdates = [];

//   // Map to generate promises for each shop update process
//   // @ts-ignore
//   const updatePromises = existingAllShops.map(async (currentShop) => {
//     try {
//       const session = await loadSession(currentShop?.key, 'load shop session');
//       if (!session) {
//         throw new Error(`Session not found for shop: ${currentShop?.key}`);
//       }
//       const shopifyShop = await getShopDetailsFromShopify(session);
//       if (!shopifyShop) {
//         throw new Error(`Shop data not found for session: ${session.shop}`);
//       }
//       await setShopProperties(shopifyShop, currentShop?.info, session.shop, true);
//       successfulUpdates.push(session.shop); // Add to successful updates
//     } catch (error) {
//       console.error(`Error updating shop: ${currentShop?.key}`, error);
//       failedUpdates.push(currentShop?.key); // Add to failed updates
//     }
//   });

//   // Wait for all promises to resolve
//   await Promise.all(updatePromises);

//   // Return the results
//   // @ts-ignore
//   return { successfulUpdates, failedUpdates };
// };

const updateShopsInfo = async (/** @type {any} */ existingAllShops) => {
  const successfulUpdates = [];
  const failedUpdates = [];

  for (let i = 0; i < existingAllShops.length; i++) {
    const currentShop = existingAllShops[i];

    try {
      console.log(`Processing shop ${i + 1}/${existingAllShops.length}: ${currentShop?.key}`);

      // eslint-disable-next-line no-await-in-loop
      const session = await loadSession(currentShop?.key, 'load shop session');
      if (!session) {
        throw new Error(`Session not found for shop: ${currentShop?.key}`);
      }
      // eslint-disable-next-line no-await-in-loop
      const shopifyShop = await getShopDetailsFromShopify(session);
      if (!shopifyShop) {
        throw new Error(`Shop data not found for session: ${session.shop}`);
      }
      // eslint-disable-next-line no-await-in-loop
      await setShopProperties(shopifyShop, currentShop?.info, session.shop, true);
      console.log(
        `Successfully updated shop: ${currentShop?.key} and data: ${JSON.stringify({ ...shopifyShop, ...currentShop?.info })}`,
      );
      successfulUpdates.push(session.shop); // Add to successful updates
    } catch (error) {
      console.error(`Error updating shop: ${currentShop?.key}`, error);
      failedUpdates.push(currentShop?.key); // Add to failed updates
    }

    // Add a delay after every 50 iterations
    if ((i + 1) % 10 === 0) {
      console.log('Pausing for 2 seconds...');
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
    }
  }

  console.log('Processing complete.');
  return { successfulUpdates, failedUpdates };
};

/**
 * Function to get all unique shop names from Datastore
 * @returns {Promise<string[]>} Array of unique shop names
 */
const getShopNames = async () => {
  const shopNamesSet = new Set();
  let query = datastore.createQuery('Shop').limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.KEY]);
      if (shopKey) shopNamesSet.add({ key: get(shopKey, 'name'), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
};

updateAllShopInfos();
