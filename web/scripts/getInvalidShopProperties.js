import datastore from '../utilities/datastore.js';

/** @type {QueryResponse<IShop>} */
const [shops] = await datastore.createQuery('Shop').run();

const overrides = shops.flatMap((shopProperties) =>
  shopProperties.settings?.paymentMethodOverrides?.some((override) => !override.paymentMethodName)
    ? {
        shop: shopProperties[datastore.KEY]?.name,
        overrides: shopProperties.settings.paymentMethodOverrides,
      }
    : [],
);

const untrimmedValues = shops.filter(
  (shopProperties) =>
    Object.values(shopProperties).some(
      (value) => typeof value === 'string' && value.trim() !== value,
    ) ||
    Object.values(shopProperties.settings || {}).some(
      (value) => typeof value === 'string' && value.trim() !== value,
    ) ||
    Object.values(shopProperties.settings?.paymentMethodOverrides || {}).some(
      ({ paymentGatewayName, paymentMethodName }) =>
        paymentGatewayName.trim() !== paymentGatewayName ||
        paymentMethodName.trim() !== paymentMethodName,
    ),
);

console.log(JSON.stringify(overrides, null, 2));

console.log('\n\nUntrimmed:\n');
console.log(JSON.stringify(untrimmedValues, null, 2));
