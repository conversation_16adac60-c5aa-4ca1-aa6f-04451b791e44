import datastore from '../utilities/datastore.js';
import { loadSession, sendQuery } from '../utilities/shopify.js';

const transaction = datastore.transaction();
await transaction.run();

const [shops] = await transaction.createQuery('Shop').run();

const updates = (
  await Promise.all(
    shops.map(async (shopProperties) => {
      const shop = shopProperties[datastore.KEY]?.name || '';
      const session = await loadSession(shop, 'resetting subscriptions');

      if (!shop || !session) {
        return undefined;
      }

      try {
        /** @type {{ shop: { email: string } } | undefined} */
        const shopResult = await sendQuery('{ shop { email } }', session);
        const { email } = shopResult?.shop || {};

        return { key: datastore.key(['Shop', shop]), data: { ...shopProperties, email } };
      } catch (error) {
        console.error(error);
      }

      return undefined;
    }),
  )
).filter((subscriptionId) => subscriptionId);

transaction.update(updates);

await transaction.commit();
