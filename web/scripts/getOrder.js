import fs from 'fs';

import shopify from '../utilities/shopify.js';

const [, , shop, orderId] = process.argv;

if (!shop || !orderId) {
  console.error('Enter the shop and the order ID as command line parameters.');
  process.exit(1);
}

const sessionId = shopify.api.session.getOfflineId(shop);
const session = await shopify.config.sessionStorage.loadSession(sessionId);

if (!session) {
  console.error(`No session with ID ${sessionId} was found.`);
  process.exit(1);
}

const order = await shopify.api.rest.Order.find({
  session,
  id: orderId,
});

if (!order) {
  console.error(`No order with ID ${orderId} were found.`);
}

const outputPath = new URL(`../../${orderId}.json`, import.meta.url);

fs.writeFileSync(outputPath, JSON.stringify(order, null, 2));
console.log('Order written successfully to', outputPath.pathname);
