import _ from 'lodash';

import { HttpResponseError, Session } from '@shopify/shopify-api';

import datastore, { getShopProperties } from '../utilities/datastore.js';
import { loadSession, sendQuery, setWatermarkVisibility } from '../utilities/shopify.js';

const chunkSize = 30;

// Uncomment the following line to only update a specific shop.
// updateWatermarkMetafield('quick-start-192ed431.myshopify.com');

// Register missing webhooks for all shops
/** @type {QueryResponse<SessionParams>} */
const [sessions] = await datastore.createQuery('Session').run();

const sessionChunks = _.chunk(
  sessions.map((sessionParams) => new Session(sessionParams)),
  chunkSize,
);

for (let i = 0; i < sessionChunks.length; i++) {
  // eslint-disable-next-line no-await-in-loop
  await Promise.all(
    sessionChunks[i].map((session) => updateWatermarkMetafield(session.shop, session)),
  );

  console.log(`${chunkSize * i + sessionChunks[i].length}/${sessions.length} updated`);
}

/* -------------------------------------- Helper functions -------------------------------------- */

/**
 * Create a new metafield definition.
 *
 * @param {string} shop
 * @param {import('@shopify/shopify-api').Session} [defaultSession]
 */
async function updateWatermarkMetafield(shop, defaultSession) {
  const session = defaultSession || (await loadSession(shop, 'updating watermark'));
  if (!session) {
    return;
  }

  try {
    /**
     * @type {[
     *   DatastoreEntity<IShop> | undefined,
     *   { shop?: { plan: { partnerDevelopment: boolean } } } | undefined
     * ]}
     */
    const [shopProperties, shopResult] = await Promise.all([
      getShopProperties(shop),
      await sendQuery('{ shop { plan { partnerDevelopment } } }', session),
    ]);

    const { isSubscriptionActive, isGrandfathered } = shopProperties || {};

    await setWatermarkVisibility(
      session,
      !isSubscriptionActive && !shopResult?.shop?.plan.partnerDevelopment && !isGrandfathered,
    );
  } catch (error) {
    // TODO: take out error handling
    if (
      error instanceof HttpResponseError &&
      (error.response.body?.errors === 'Not Found' ||
        error.response.body?.errors === 'Unavailable Shop')
    ) {
      return;
    }

    console.error(`An error occurred while creating the webhook subscription for ${session.shop}`);
    console.trace(error);
  }
}
