import lodash from 'lodash';
// eslint-disable-next-line import/no-extraneous-dependencies
import { shopStatus } from 'easyflow-enums';
import { getAllShopInfo } from '../utilities/datastore.js';
import { createOrUpdateFluentCRMContact, FLUENT_CRM_TAGS } from '../utilities/fluentCrm.js';

const { get, isEmpty } = lodash;

const updateAllShopData = async () => {
  const existingAllShops = await getAllShopInfo();
  console.log('allShopsCount', existingAllShops.length);
  const { success, failed, noEmail } = await updateShopDataToFluentCRM(existingAllShops);
  console.log('success', success);
  console.log('failed', failed);
  console.log('noEmail', noEmail);
};

const updateShopDataToFluentCRM = async (/** @type {any} */ existingAllShops) => {
  let success = 0;
  let failed = 0;
  let noEmail = 0;
  for (let i = 0; i < existingAllShops.length; i++) {
    const currentShop = existingAllShops[i];

    console.log(`Processing shop ${i + 1}/${existingAllShops.length}: ${currentShop?.key}`);
    if (!isEmpty(currentShop?.info?.email)) {
      try {
        const fluentCrmTag = getTagFromShopStatus(get(currentShop?.info, 'status'));
        const fluentCrmOptionalData = getTagOptionalDataFromShopStatus(
          get(currentShop?.info, 'status'),
        );

        console.log('fluentCrmOptionalData', fluentCrmOptionalData);
        console.log('fluentCrmTag', fluentCrmTag);

        if (!isEmpty(fluentCrmOptionalData)) {
          createOrUpdateFluentCRMContact(
            { ...currentShop?.info },
            fluentCrmTag,
            fluentCrmOptionalData,
          );
          console.log('hasOptionalData');
        } else {
          createOrUpdateFluentCRMContact({ ...currentShop?.info }, fluentCrmTag);
          console.log('Without');
        }
        success++;
      } catch (err) {
        console.error(`Error in updating shop ${currentShop?.key} to FluentCRM`, err);
        failed++;
      }
    } else {
      console.error(`Shop ${currentShop?.key} does not have an email address`);
      noEmail++;
    }

    // Add a delay after every 50 iterations
    if ((i + 1) % 30 === 0) {
      console.log('Pausing for 20 seconds...');
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, 20000);
      });
    }
  }

  return { success, failed, noEmail };
};

/**
 * @param {string} status
 */
const getTagFromShopStatus = (status) => {
  switch (status) {
    case shopStatus.UNINSTALLED:
      return FLUENT_CRM_TAGS.UNINSTALLED;
    default:
      return FLUENT_CRM_TAGS.INSTALLED;
  }
};
/**
 * @param {string} status
 */
const getTagOptionalDataFromShopStatus = (status) => {
  switch (status) {
    case shopStatus.ACTIVE:
      return { status: 'ACTIVE' };
    case shopStatus.CLOSED:
      return { status: 'FROZEN' };
    default:
      return null;
  }
};

updateAllShopData();
