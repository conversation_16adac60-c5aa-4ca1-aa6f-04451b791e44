/* eslint-disable no-await-in-loop */
// @ts-nocheck
import lodash from "lodash";

import { HttpResponseError } from "@shopify/shopify-api";

import datastore from "../utilities/datastore.js";
import { loadSession, sendQuery } from "../utilities/shopify.js";
import webhooks from "../webhooks.js";

const { get, pick } = lodash;

// const chunkSize = 30;

// Unregistered unnecessary webhooks for a specific shop
const allShopsRegisterWebhooks = async () => {
  const successfulUpdates = [];
  const failedUpdates = [];
  const noChangeUpdates = [];

  const existingAllShops = await getShopNames();

  for (let i = 0; i < existingAllShops.length; i++) {
    const currentShop = existingAllShops[i];
    try {
      console.log(`Processing shop ${i + 1}/${existingAllShops.length}: ${currentShop?.key}`);
      // eslint-disable-next-line no-await-in-loop
      const result = await registerShopMissingWebhooks(currentShop?.key);

      if (get(result, "status") === "success") {
        successfulUpdates.push(pick(result, "shop", "webhooks"));
      } else if (get(result, "status") === "failed") {
        failedUpdates.push(pick(result, "shop", "webhooks"));
      } else if (get(result, "status") === "no_change") {
        noChangeUpdates.push(pick(result, "shop"));
      }
    } catch (error) {
      console.error(`Error updating shop: ${currentShop?.key}`, error);
      failedUpdates.push({
        shop: currentShop?.key,
        message: error.message,
      });
    }

    // Add a delay after every 50 iterations
    if ((i + 1) % 10 === 0) {
      console.log("Pausing for 2 seconds...");
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(resolve, 2000);
      });
    }
  }

  console.log("success_count", successfulUpdates.length);
  console.log("failed_count", failedUpdates.length);
  console.log("no_changed_count", noChangeUpdates.length);
};

/**
 * Register missing webhooks.
 *
 * @param {string} shop
 * @param {Session} [defaultSession]
 * @returns Object
 */
const registerShopMissingWebhooks = async (shop, defaultSession = undefined) => {
  const successfulWebhooks = [];
  const failedWebhooks = [];

  const session = defaultSession || (await loadSession(shop, "updating or creating webhooks"));
  if (!session) {
    return;
  }

  console.log("session", session);

  const excludedTopics = ["CUSTOMERS_DATA_REQUEST", "CUSTOMERS_REDACT", "SHOP_REDACT"];
  const topics = Object.keys(webhooks).filter((topic) => !excludedTopics.includes(topic));

  console.log("topics", topics);

  // If topic is not registered, register it.
  try {
    /**
     * @type {{
     *   webhookSubscriptions: {
     *     nodes: {
     *       id: string;
     *       topic: string;
     *       endpoint: { callbackUrl?: string; }
     *     }[]
     *   }
     * } | undefined}
     */
    const webhookResult = await sendQuery(
      `{
       webhookSubscriptions(first: 10) {
         nodes {
           id
           topic
           endpoint { ... on WebhookHttpEndpoint { callbackUrl } }
         }
       }
     }`,
      session
    );

    if (webhookResult) {
      const registeredWebhooks = webhookResult?.webhookSubscriptions.nodes;

      // Identify webhooks to be registered
      const webhooksToRegister = topics.filter(
        (topic) => !registeredWebhooks.some((webhook) => webhook.topic === topic)
      );

      if (webhooksToRegister.length === 0) {
        console.log(`No unnecessary webhooks found for shop ${shop}.`);

        // eslint-disable-next-line consistent-return
        return {
          shop: session.shop,
          webhooks: {},
          error: undefined,
          status: "no_change",
        };
      }

      console.log(`Webhooks to be Registered for shop ${shop}:`, webhooksToRegister);

      // eslint-disable-next-line no-restricted-syntax
      for (const webTopic of webhooksToRegister) {
        const webhook = webhooks[webTopic];
        try {
          // eslint-disable-next-line no-await-in-loop
          const webhookSubscription = {
            callbackUrl: new URL(webhook.callbackUrl, process.env.HOST).href,
            format: "JSON",
          };

          const response = await sendQuery(
            `mutation WebhookSubscriptionCreateMutation(
              $topic: WebhookSubscriptionTopic!
              $webhookSubscription: WebhookSubscriptionInput!
            ) {
              webhookSubscriptionCreate(topic: $topic, webhookSubscription: $webhookSubscription) {
                userErrors { field, message }
              }
            }`,
            session,
            { webhookSubscription, topic: webTopic }
          );

          if (response.userErrors && response.userErrors.length > 0) {
            console.error("Error creating webhook:", response.userErrors);
          } else {
            console.log("Webhook created successfully!");
          }
        } catch (error) {
          console.error(`Error registering webhook ${webhook.id}:`, error);
          failedWebhooks.push({
            id: webhook.id,
            topic: webhook.topic,
            errors: error.message,
          });
        }
      }
      // eslint-disable-next-line consistent-return
      return {
        shop: session.shop,
        webhooks: {
          success: successfulWebhooks,
          failed: failedWebhooks,
        },
        error: undefined,
        status: "success",
      };
    }
  } catch (error) {
    if (
      error instanceof HttpResponseError &&
      (error.response.body?.errors === "Not Found" || error.response.body?.errors === "Unavailable Shop")
    ) {
      // eslint-disable-next-line consistent-return
      return {
        shop: session.shop,
        webhooks: {
          success: successfulWebhooks,
          failed: failedWebhooks,
        },
        error: "Unavailable Shop",
        status: "failed",
      };
    }

    console.error(`An error occurred while delete the webhook for ${session.shop}`);
    // console.trace(error);
  }
};

/**
 * Function to get all unique shop names from Datastore
 * @returns {Promise<string[]>} Array of unique shop names
 */
const getShopNames = async () => {
  const shopNamesSet = new Set();
  let query = datastore.createQuery("Shop").limit(500);
  let moreResults = true;

  while (moreResults) {
    /** @type {QueryResponse<IShop>} */
    // eslint-disable-next-line no-await-in-loop
    const [shops, info] = await query.run();

    // Add shop names to the Set
    shops.forEach((shop) => {
      const shopKey = get(shop, [datastore.KEY]);
      if (shopKey) shopNamesSet.add({ key: get(shopKey, "name"), info: shop });
    });

    // Check if there are more results and set the cursor for the next query
    if (info.moreResults !== datastore.MORE_RESULTS_AFTER_LIMIT) {
      moreResults = false;
    } else if (info.endCursor) {
      query = query.start(info.endCursor);
    } else {
      moreResults = false;
    }
  }
  // Convert Set to Array if needed
  return Array.from(shopNamesSet);
};

allShopsRegisterWebhooks();
