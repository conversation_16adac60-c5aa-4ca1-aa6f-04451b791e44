import "./utilities/sentry.js";

import crypto from "crypto";
import path, { join } from "path";
import process from "process";

import bodyParser from "body-parser";
import express from "express";
import { readFile } from "fs/promises";
import serveStatic from "serve-static";

import { Datastore, PropertyFilter } from "@google-cloud/datastore";
import * as Sentry from "@sentry/node";

// eslint-disable-next-line import/no-extraneous-dependencies
import lodash from "lodash";
// eslint-disable-next-line import/no-extraneous-dependencies
import { shopStatus, subscriptionIntervals } from "easyflow-enums";
import settingsRoutes from "./api/routes/settings.route.js";
import { handleFreeSubscription } from "./api/services/subscription.service.js";
import { getSubscriptionPlanByConditions } from "./api/services/subscriptionPlan.service.js";
import { defaultStyleOptions } from "./defaults.js";
// eslint-disable-next-line import/no-relative-packages
import { getPageStaticSkeletonContent } from "./api/helpers/global.js";
import adminRoutes from "./api/routes/admin.routes.js";
import configs from "./configs/index.js";
import trackEvent, { captureException, setProfileProperties } from "./utilities/analytics.js";
import datastore, {
  getShopProperties,
  optionFromStored,
  optionSetFromStored,
  optionSetToStored,
  optionToStored,
  runUnlimitedInQuery,
  setShopProperties,
} from "./utilities/datastore.js";
import { createOrUpdateFluentCRMContact, FLUENT_CRM_CONFIGS, FLUENT_CRM_TAGS } from "./utilities/fluentCrm.js";
import {
  deleteValueArtifacts,
  updateAddonProducts,
  updateCollections,
  updateGlobalOptionsMetafield,
  updateMetafields,
  updateMetaobjects,
} from "./utilities/productOptions.js";
import shopify, {
  createFiles,
  createMetafieldDefinitions,
  createUploadUrls,
  getAppHandle,
  getShopDetails,
  getShopDetailsFromShopify,
  getShopId,
  getSubscriptionRedirect,
  isProduction,
  loadSession,
  metafieldNamespace,
  preferencesMetafieldKey,
  sendQuery,
  setWatermarkVisibility,
  stylesMetafieldKey,
} from "./utilities/shopify.js";
import webhookHandlers from "./webhooks.js";

const { get, isEmpty } = lodash;

/**
 * @template TResponse
 * @template [TRequest=any]
 * @template [TQuery=any]
 *
 * @typedef {import('express').RequestHandler<
 *   import('express-serve-static-core').RouteParameters<any>,
 *   TResponse | { error?: string },
 *   TRequest,
 *   TQuery
 * >} ExpressHandler
 */

const port = parseInt(process.env.BACKEND_PORT || process.env.PORT || "3000", 10);
const staticPath = path.join(process.cwd(), isProduction ? "frontend/dist" : "frontend/");

// Catch SIGTERM, so that we can wait for the asynchronous tasks to finish
process.once("SIGTERM", () => {
  // eslint-disable-next-line no-console
  console.log("Received SIGTERM, shutting down...");

  // eslint-disable-next-line no-console
  setInterval(() => console.log("1s elapsed"), 1000);
});

/* --------------------------------------- Webserver setup -------------------------------------- */

const app = express();

// Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());

app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  async (_, response, next) => {
    const { session } = response.locals.shopify;
    const [, , oldShopProperty, shopData, freePlan, shopAppHandle] = await Promise.all([
      createMetafieldDefinitions(session.shop),
      updateCollections(session),
      getShopProperties(session.shop),
      getShopDetailsFromShopify(session),
      getSubscriptionPlanByConditions({ slug: `free-${subscriptionIntervals.LIFETIME}` }),
      getAppHandle(session),
    ]);

    const updatedShopData = {
      ...shopData,
      createdAt: new Date().toISOString(),
      status: shopStatus.ACTIVE,
      appHandle: shopAppHandle,
    };

    // await setShopProperties(updatedShopData, oldShopProperty, session.shop, true);
    await handleFreeSubscription(session, { ...oldShopProperty, ...updatedShopData }, freePlan);

    (async () => {
      try {
        if (FLUENT_CRM_CONFIGS.isEnabled) {
          createOrUpdateFluentCRMContact({ ...oldShopProperty, ...updatedShopData }, FLUENT_CRM_TAGS.INSTALLED);
        }
        trackEvent("install app", session.shop, { email: get(shopData, "email") });
      } catch (error) {
        captureException(error, session.shop);
      }
    })();

    next();
  },
  shopify.redirectToShopifyOrAppRoot()
);

app.post(
  shopify.config.webhooks.path,
  express.text({ type: "*/*", limit: "1MB" }),
  shopify.processWebhooks({ webhookHandlers })
);

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js

app.use("/appProxy/*", bodyParser.json(), async (request, response, next) => {
  await shopify.api.utils.validateHmac(/** @type {import('@shopify/shopify-api').AuthQuery} */ (request.query), {
    signator: "appProxy",
  });

  const { shop } = request.query;

  if (!shop || typeof shop !== "string") {
    response.status(400).send({ error: "Bad Request" });
    return;
  }

  const session = await loadSession(shop, "creating upload url");

  if (!session) {
    response.status(401).send({ error: "Unauthorized" });
    return;
  }

  response.locals.shopify = { session };

  next();
});

app.post(
  "/appProxy/createUploadUrl",
  /** @type {ExpressHandler<CreateUploadUrlResponse, CreateUploadUrlRequest>} */ (
    async (request, response) => {
      const { file } = request.body;

      if (typeof file !== "object" || !file) {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { session } = response.locals.shopify;

      try {
        const fileUploadRequests = await createUploadUrls(
          [file],
          file.mimeType.startsWith("image/") ? "IMAGE" : "FILE",
          session
        );

        response.send({ data: fileUploadRequests?.[0] });
      } catch (error) {
        captureException(error, session.shop);

        response
          .status(error instanceof Error ? 400 : 500)
          .send({ error: error instanceof Error ? error.message : "Internal server error" });
      }
    }
  )
);

app.post(
  "/appProxy/createUploadFile",
  /** @type {ExpressHandler<CreateUploadFileResponse, CreateUploadFileRequest>} */ (
    async (request, response) => {
      const { file, contentType } = request.body;

      if (typeof file !== "object" || !file || typeof contentType !== "string") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { session } = response.locals.shopify;

      try {
        const files = await createFiles([file], contentType, session, true);
        response.send({ data: files?.[0] });
      } catch (error) {
        captureException(error, session.shop);

        response
          .status(error instanceof Error ? 400 : 500)
          .send({ error: error instanceof Error ? error.message : "Internal server error" });
      }
    }
  )
);

// Parse JSON for all routes
app.use(express.json({ limit: "1MB" }));

app.use("/admin-api", adminRoutes);

// API handlers
app.use("/api/*", shopify.validateAuthenticatedSession());

app.get(
  "/api/appBlock/enabled",
  /** @type {ExpressHandler<boolean>} */ (
    async (_, response) => {
      const { session } = response.locals.shopify;
      const { shop } = session;

      try {
        const activeThemeId = (await shopify.api.rest.Theme.all({ session, role: "main", fields: ["id"] })).data[0]?.id;

        if (!activeThemeId) {
          response.status(200).send(false);
          return;
        }

        const productTemplate = await shopify.api.rest.Asset.all({
          theme_id: activeThemeId,
          asset: { key: "templates/product.json" },
          fields: ["value"],
          session,
        });

        const productTemplateValue = productTemplate.data[0]?.value;

        const isAppBlockAdded =
          productTemplateValue &&
          Object.values(JSON.parse(productTemplateValue).sections).some(
            (section) =>
              section.blocks &&
              Object.values(section.blocks).some((block) =>
                [
                  get(configs, "themeAppExtensionAppBlockUrl", ""),
                  "shopify://apps/easyflow/blocks/app-block/83d04f43-e63e-4a8d-82b9-e4f0b41ef22f",
                  "shopify://apps/easyflow-product-options/blocks/app-block/83d04f43-e63e-4a8d-82b9-e4f0b41ef22f",
                  "shopify://apps/product-options-test/blocks/app-block/e266466e-457c-40e3-b808-b597d88681cc",
                ].includes(block.type)
              )
          );

        response.status(200).send(isAppBlockAdded);
      } catch (error) {
        captureException(error, shop);
        response.status(500).send();
      }
    }
  )
);

app.post(
  "/api/extension/enabled",
  /** @type {ExpressHandler<{}>} */ (
    async (_, response) => {
      const { shop } = response.locals.shopify.session;

      const shopProperties = await getShopProperties(shop);
      await setShopProperties({ isExtensionEnabled: true }, shopProperties, shop, true);

      response.send({});
    }
  )
);

app.get(
  "/api/options/get",
  /**
   * @type {ExpressHandler<
   *   OptionItems,
   *   null,
   *   { optionSetsCursor?: string, optionsCursor?: string }
   * >}
   */ (
    async (request, response) => {
      const { optionSetsCursor, optionsCursor } = request.query;

      const { session } = response.locals.shopify;
      const { shop } = session;

      const isInitialRequest = !optionSetsCursor && !optionsCursor;

      const optionSetsQuery = datastore
        .createQuery("Option set")
        .hasAncestor(datastore.key(["Shop", shop]))
        .order("createdAt", { descending: true });

      if (isInitialRequest) {
        optionSetsQuery.limit(13);
      } else if (optionSetsCursor) {
        optionSetsQuery.start(optionSetsCursor);
      }

      const optionsQuery = datastore
        .createQuery("Option")
        .hasAncestor(datastore.key(["Shop", shop]))
        .order("createdAt", { descending: true });

      if (isInitialRequest) {
        optionsQuery.limit(13);
      } else if (optionsCursor) {
        optionsQuery.start(optionsCursor);
      }

      const [[optionSets, optionSetsInfo], [options, optionsInfo]] = await Promise.all(
        /** @type {[QueryResponse<IProductOptionSet>, QueryResponse<IProductOption>]} */ ([
          ...(isInitialRequest || optionSetsCursor ? [optionSetsQuery.run()] : [[[], {}]]),
          ...(isInitialRequest || optionsCursor ? [optionsQuery.run()] : [[[], {}]]),
        ])
      );

      response.status(200).send({
        optionSets: (optionSets || []).flatMap((optionSet) => optionSetFromStored(optionSet, shop) || []),
        options: (options || []).flatMap((option) => optionFromStored(option, shop) || []),

        ...(optionSetsInfo && optionSetsInfo.moreResults !== Datastore.NO_MORE_RESULTS
          ? { optionSetsCursor: optionSetsInfo.endCursor }
          : {}),
        ...(optionsInfo && optionsInfo.moreResults !== Datastore.NO_MORE_RESULTS
          ? { optionsCursor: optionsInfo.endCursor }
          : {}),
      });
    }
  )
);

app.put(
  "/api/options/save",
  /** @type {ExpressHandler<OptionItems, OptionItems>} */ (
    async (request, response) => {
      if (typeof request.body !== "object") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { optionSets = [], options = [] } = request.body;
      const { session } = response.locals.shopify;
      const { shop } = session;

      if (!optionSets.length && !options.length) {
        response.send({ optionSets, options });
        return;
      }

      try {
        const existingOptionSetKeys = optionSets.flatMap((optionSet) =>
          optionSet.id ? datastore.key(["Shop", shop, "Option set", optionSet.id]) : []
        );

        const existingOptionKeys = options.flatMap((option) =>
          option.id ? datastore.key(["Shop", shop, "Option", option.id]) : []
        );

        const unsavedOptionCount = options.filter((option) => !option.id).length;

        // Request previous options, option sets, and allocate new option IDs
        const [storedOptionSets, storedOptions, optionOptionSets, [allocatedOptionIds]] = await Promise.all([
          existingOptionSetKeys.length
            ? /** @type {Promise<CollectedProjection<IProductOptionSet, 'productIds'>[]>} */ (
                runUnlimitedInQuery(
                  "Option set",
                  existingOptionSetKeys,
                  (query, chunk) => query.filter(new PropertyFilter("__key__", "IN", chunk)).select("productIds"),
                  "productIds"
                )
              )
            : [],
          existingOptionKeys.length
            ? /** @type {Promise<DatastoreEntity<IProductOption>[]>} */ (
                runUnlimitedInQuery(
                  "Option",
                  existingOptionKeys,
                  (query, chunk) => query.filter(new PropertyFilter("__key__", "IN", chunk)),
                  null
                )
              )
            : [],
          existingOptionKeys.length
            ? /** @type {Promise<CollectedProjection<IProductOptionSet, 'productIds'>[]>} */ (
                runUnlimitedInQuery(
                  "Option set",
                  existingOptionKeys.map((key) => key.id),
                  (query, chunk) =>
                    query
                      .hasAncestor(datastore.key(["Shop", shop]))
                      .filter(new PropertyFilter("optionIds", "IN", chunk))
                      .select("productIds"),
                  "productIds"
                )
              )
            : [],
          unsavedOptionCount
            ? datastore.allocateIds(datastore.key(["Shop", shop, "Option"]), unsavedOptionCount)
            : [[]],
        ]);

        // Update addon products' of changed option values
        const optionsWithAddonProducts = await updateAddonProducts(options, storedOptions, session);

        // Update metaobjects using the `metaobjectId`s fetched above
        const optionsWithMetaobjectId = await updateMetaobjects(
          optionsWithAddonProducts,
          storedOptions,
          allocatedOptionIds,
          session
        );

        // Calculate changed products using the `productIds` fetched above
        const changedProductIds = [
          ...new Set(
            [
              // Add changed options and option sets to update them on products
              ...optionSets,
              ...optionsWithMetaobjectId,
              ...optionOptionSets,
              // Add existing options and option sets to remove them from products
              ...storedOptions,
              ...storedOptionSets,
            ].flatMap((optionOrSet) => optionOrSet.productIds)
          ),
        ];

        const changedOptionsAndSets = { update: { optionSets, options: optionsWithMetaobjectId } };

        await Promise.all([
          // Update metafield for the options that are added automatically to the products
          updateGlobalOptionsMetafield(changedOptionsAndSets, session),
          // Update metafields of the changed products
          updateMetafields(changedProductIds, changedOptionsAndSets, session),
          // Delete addon products' of changed option values
        ]);

        // Store options and option sets
        const upsertedEntities = [
          ...optionSets.map((optionSet) => ({
            kind: /** @type {const} */ ("Option set"),
            data: optionSet,
          })),
          ...optionsWithMetaobjectId.map((option) => ({
            kind: /** @type {const} */ ("Option"),
            data: option,
          })),
        ].map((item) => {
          const itemData = item.kind === "Option set" ? optionSetToStored(item.data) : optionToStored(item.data);

          return {
            key: datastore.key(["Shop", shop, item.kind, ...(item.data.id ? [item.data.id] : [])]),
            data: itemData,
          };
        });

        const upsertResult = optionSets.length + options.length ? await datastore.upsert(upsertedEntities) : undefined;

        trackEvent("options saved", shop);

        // Return objects with the IDs generated by Datastore
        response.send({
          optionSets: upsertResult
            ? upsertedEntities.flatMap((item) =>
                item.key.kind === "Option set"
                  ? optionSetFromStored(
                      {
                        .../** @type {Stored<IProductOptionSet>} */ (item.data),
                        id: Number(item.key.id),
                      },
                      shop
                    ) || []
                  : []
              )
            : [],
          options: upsertResult
            ? upsertedEntities.flatMap((item) =>
                item.key.kind === "Option"
                  ? optionFromStored(
                      {
                        .../** @type {Stored<IProductOption>} */ (item.data),
                        id: Number(item.key.id),
                      },
                      shop
                    ) || []
                  : []
              )
            : [],
        });
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Failed to save options" });
      }
    }
  )
);

app.post(
  "/api/options/delete",
  /** @type {ExpressHandler<{}, OptionItemIds>} */ (
    async (request, response) => {
      if (typeof request.body !== "object" || !request.body) {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { optionSetIds = [], optionIds = [] } = request.body;
      const { session } = response.locals.shopify;
      const { shop } = session;

      try {
        const optionKeys = optionIds.map((id) => datastore.key(["Shop", shop, "Option", id]));
        const optionSetKeys = optionSetIds.map((id) => datastore.key(["Shop", shop, "Option set", id]));

        /**
         * @type {[
         *   DatastoreEntity<IProductOptionSet>[],
         *   CollectedProjection<IProductOptionSet, 'productIds'>[],
         *   DatastoreEntity<IProductOption>[]
         * ]}
         */
        const [containingOptionSets, optionSets, options] = await Promise.all([
          optionIds.length
            ? runUnlimitedInQuery(
                "Option set",
                optionIds,
                (query, chunk) =>
                  query.hasAncestor(datastore.key(["Shop", shop])).filter(new PropertyFilter("optionIds", "IN", chunk)),
                null
              )
            : [],
          optionSetKeys.length
            ? /**
               * @type {Promise<
               *   CollectedProjection<IProductOptionSet, 'productIds' | 'productSelectionMethod'>[]
               * >}
               */ (
                runUnlimitedInQuery(
                  "Option set",
                  optionSetKeys,
                  (query, chunk) => query.filter(new PropertyFilter("__key__", "IN", chunk)).select(["productIds"]),
                  "productIds"
                )
              )
            : [],
          optionKeys.length
            ? runUnlimitedInQuery(
                "Option",
                optionKeys,
                (query, chunk) => query.filter(new PropertyFilter("__key__", "IN", chunk)),
                null
              )
            : [],
        ]);

        // Delete addon products
        deleteValueArtifacts(options, session);

        await Promise.all([
          // Delete option metaobjects
          ...(options.length
            ? options.map((option) =>
                sendQuery(
                  `mutation MetaobjectDeleteMutation {
                    metaobjectDelete(id: "${option.metaobjectId}") {
                      userErrors { field, message }
                    }
                  }`,
                  session
                )
              )
            : []),
          // Update metafield for the options that are automatically added to the products
          updateGlobalOptionsMetafield({ delete: { optionSetIds, optionIds } }, session),
          // Update products' metafields
          optionSets?.length &&
            updateMetafields(
              optionSets.flatMap((optionSet) => optionSet.productIds),
              { delete: { optionSetIds, optionIds } },
              session
            ),
        ]);

        await Promise.all([
          // Delete options and option sets from Datastore
          datastore.delete([
            ...optionSetIds.map((optionSetId) => datastore.key(["Shop", shop, "Option set", optionSetId])),
            ...optionIds.map((optionId) => datastore.key(["Shop", shop, "Option", optionId])),
          ]),

          // Remove deleted options from option sets
          containingOptionSets?.length &&
            datastore.update(
              containingOptionSets?.flatMap((optionSet) =>
                !optionSetIds.includes(Number(optionSet[datastore.KEY]?.id))
                  ? {
                      key: optionSet[datastore.KEY],
                      data: {
                        ...optionSet,
                        optionIds: optionSet.optionIds.filter((id) => !optionIds.includes(id)),
                      },
                    }
                  : []
              )
            ),
        ]);

        trackEvent("options deleted", shop);

        response.send({});
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Failed to delete options" });
      }
    }
  )
);

app.get(
  "/api/styles/get",
  /** @type {ExpressHandler<StyleOptions> } */ (
    async (_, response) => {
      const { shop } = response.locals.shopify.session;

      /** @type {GetResponse<StyleOptions>} */
      const [styles] = await datastore.get(datastore.key(["Shop", shop, "Styles", "default"]));

      response.send({ ...defaultStyleOptions, ...styles });
    }
  )
);

app.post(
  "/api/styles/save",
  /** @type {ExpressHandler<{}, StyleOptions> } */ (
    async (request, response) => {
      if (typeof request.body !== "object" || !request.body) {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      // Remove all unknown style options
      const sanitizedStyleOptions = /** @type {StyleOptions} */ (
        Object.fromEntries(
          Object.entries(request.body).filter(
            ([name, value]) => typeof value === typeof defaultStyleOptions[/** @type {keyof StyleOptions} */ (name)]
          )
        )
      );

      if (!Object.keys(sanitizedStyleOptions).length) {
        response.send({});
        return;
      }

      const { session } = response.locals.shopify;

      const { cssCustomizations, cssText, ...customizations } = sanitizedStyleOptions;

      try {
        const shopId = await getShopId(session);

        const cssCustomizationText = Object.entries(cssCustomizations)
          .flatMap(
            ([selector, cssProperties]) =>
              cssProperties
                ? selector
                    .split(",")
                    .map(
                      (selectorPart) =>
                        `.ef__product-option-root ${selectorPart.trim()}{${Object.entries(cssProperties)
                          .map(([propertyName, propertyValue]) => `${propertyName}:${propertyValue}`)
                          .join(";")}}`
                    )
                    .join("")
                : [],
            ""
          )
          .join("");

        const metafields = [
          ...(cssText || cssCustomizationText
            ? [
                {
                  namespace: metafieldNamespace,
                  key: stylesMetafieldKey,
                  type: "multi_line_text_field",
                  ownerId: shopId,
                  value: cssCustomizationText + cssText,
                },
              ]
            : []),
          ...(Object.keys(customizations).length > 1
            ? [
                {
                  namespace: metafieldNamespace,
                  key: preferencesMetafieldKey,
                  type: "json",
                  ownerId: shopId,
                  value: JSON.stringify(customizations),
                },
              ]
            : []),
        ];

        await sendQuery(
          `mutation MetafieldsSetMutation($metafields: [MetafieldsSetInput!]!) {
            metafieldsSet(metafields: $metafields) {
              userErrors { field, message }
            }
          }`,
          session,
          { metafields }
        );

        await datastore.save({
          key: datastore.key(["Shop", session.shop, "Styles", "default"]),
          data: sanitizedStyleOptions,
          excludeFromIndexes: ["cssText"],
        });
      } catch (error) {
        captureException(error, session.shop);
        response.status(500).send({ error: "Failed to save styles." });
      }

      response.send({});
    }
  )
);

app.get(
  "/api/products/get-handle",
  /**
   * @type {ExpressHandler<
   *   ProductsGetHandleResponse,
   *   null,
   *   { productId?: String, collectionId?: string }>
   * }
   */ (
    async (request, response) => {
      const { productId, collectionId } = request.query;

      const { session } = response.locals.shopify;
      const { shop } = session;

      if (typeof productId !== "string" && typeof collectionId !== "string") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      try {
        /** @type {{ products: { nodes: { handle: string }[] } }} */
        const result = productId
          ? await sendQuery(`{ products(first: 1, query: "id:${productId}") { nodes { handle } } }`, session)
          : (
              await sendQuery(
                `{
                  collection(id: "gid://shopify/Collection/${collectionId}") {
                    products(first: 1) { nodes { handle } }
                  }
                }`,
                session
              )
            )?.collection;

        response.send({ handle: result.products.nodes[0]?.handle || "" });
      } catch (error) {
        captureException(error, shop);
        response.send({ handle: "" });
      }
    }
  )
);

app.get(
  "/api/products/options",
  /** @type {ExpressHandler<ProductsOptionsGetResponse, never, { productIds: string[] }>} */ (
    async (request, response) => {
      const { productIds } = request.query;
      const { session } = response.locals.shopify;

      if (isEmpty(productIds)) {
        response.send([]);
        return;
      }

      /**
       * @type {{
       *   nodes: ({ id: string; options: { name: string, values: string[], }[] } | null)[]
       * } | undefined}
       */
      const productsResult = await sendQuery(
        `{
          nodes(ids: ${JSON.stringify(productIds)}) {
            ... on Product { id, options { name, values, position } }
          }
        }`,
        session
      );

      response.send(
        Object.fromEntries(
          productsResult?.nodes.flatMap((product) => (product?.options ? [[product.id, product.options]] : [])) || []
        )
      );
    }
  )
);

app.post(
  "/api/products/get",
  /**
   * @type {ExpressHandler<
   *   ProductsGetResponse,
   *   {selectedIds?: any},
   *   { searchTerm?: string, selectedIds?: string, cursor?: string, paginationType?: string }
   * >}
   */ (
    async (request, response) => {
      const { searchTerm, cursor, paginationType } = request.query;

      if (typeof searchTerm !== "string" && typeof searchTerm !== "undefined") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { session } = response.locals.shopify;
      const { shop } = session;

      const titleQuery =
        searchTerm
          ?.split(/\s+/)
          .map((term) => `*${term}*`)
          .join(" ") || "";

      const selectedIds = Array.isArray(request.body.selectedIds)
        ? request.body.selectedIds.flatMap((id) => (typeof id === "string" ? id : []))
        : [];

      const idsToExclude = selectedIds.flatMap((id) => id.replace?.("gid://shopify/Product/", ""));

      try {
        /**
         * @type {{
         *   products: {
         *    edges: {
         *      cursor: string,
         *      node: { id: string, title: string, featuredImage?: { url: string } }
         *    }[],
         *    pageInfo: IPageInfo,
         *   }
         * } | undefined}
         */
        const productsResult = await sendQuery(
          `{
            products(
              ${paginationType === "before" ? "last" : "first"}: 50,
              sortKey: TITLE,
              ${cursor ? `${paginationType}: "${cursor}",` : ""}
              ${titleQuery ? `query: "title:${titleQuery}",` : ""}
              ${idsToExclude.length && !titleQuery ? `query: "NOT id:${idsToExclude.join(" AND NOT id:")}"` : ""}
            ) {
              edges { cursor, node { id, title, featuredImage { url } } }
              pageInfo { hasNextPage, hasPreviousPage }
            }
          }`,
          session
        );

        const products =
          productsResult?.products.edges.map(({ cursor: productCursor, node: product }) => ({
            id: product.id,
            title: product.title,
            imageUrl: product.featuredImage?.url,
            cursor: productCursor,
          })) || [];

        const idsToQuery = selectedIds
          .filter((id) => !products.find((product) => product.id === id))
          .map((id) => id.replace("gid://shopify/Product/", ""));

        // Show selected products at the top of the list (first page only)
        if (idsToQuery.length && !productsResult?.products.pageInfo.hasPreviousPage && !titleQuery) {
          /**
           * @type {{
           *  products: {
           *    nodes: { id: string, title: string, featuredImage?: { url: string } }[]
           *  }
           * } | undefined}
           */
          const selectedProductsResult = await sendQuery(
            `{
              products(
                first: 50,
                sortKey: TITLE,
                query: "id:${idsToQuery.join(" OR id:")}"
              ) {
                nodes { id, title, featuredImage { url } }
              }
            }`,
            session
          );

          products.unshift(
            ...(selectedProductsResult?.products.nodes.map((product) => ({
              id: product.id,
              title: product.title,
              imageUrl: product.featuredImage?.url,
              cursor: "",
            })) || [])
          );
        }

        response.send({ products, pageInfo: productsResult?.products.pageInfo || {} });
      } catch (error) {
        captureException(error, shop);
        response.send({ products: [], pageInfo: {} });
      }
    }
  )
);

app.get(
  "/api/collections/get",
  /**
   * @type {ExpressHandler<
   *   CollectionsGetResponse,
   *   never,
   *   { searchTerm?: string, selectedIds?: string }
   * >}
   */ (
    async (request, response) => {
      const { searchTerm } = request.query;

      const { session } = response.locals.shopify;
      const { shop } = session;

      if (typeof searchTerm !== "string" && typeof searchTerm !== "undefined") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const titleQuery =
        searchTerm
          ?.split(/\s+/)
          .map((term) => `*${term}*`)
          .join(" ") || "";

      let idsToQuery = Array.isArray(request.query.selectedIds)
        ? request.query.selectedIds.flatMap((id) => (typeof id === "string" ? id : []))
        : [];

      idsToQuery = idsToQuery.map((id) => id.replace("gid://shopify/Collection/", ""));

      try {
        const [initialQueryResult, secondQueryResult] = await Promise.all([
          /** @type {{ collections: { nodes: { id: string, title: string }[] } } | undefined} */ (
            !titleQuery ? sendQuery(`{ collections(first: 50) { nodes { id, title } } }`, session) : undefined
          ),
          /** @type {{ collections: { nodes: { id: string, title: string }[] } } | undefined} */ (
            titleQuery || idsToQuery.length > 0
              ? sendQuery(
                  `{
                    collections(
                      first: 50,
                      sortKey: TITLE,
                      ${titleQuery ? `query: "title:${titleQuery}",` : ""}
                      ${idsToQuery.length ? `query: "id:${idsToQuery.join(" OR id:")}"` : ""}
                    ) {
                      nodes { id, title }
                    }
                  }`,
                  session
                )
              : undefined
          ),
        ]);

        let collections = secondQueryResult?.collections?.nodes || [];

        collections = [
          ...collections,
          ...(initialQueryResult?.collections?.nodes.filter(
            (initialCollection) => !collections.some((collection) => collection.id === initialCollection.id)
          ) || []),
        ];

        response.send(collections);
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Internal Server Error" });
      }
    }
  )
);

app.get(
  "/api/variants/get",
  /**
   * @type {ExpressHandler<
   *   VariantsGetResponse,
   *   null,
   *   { searchTerm?: string, cursor?: string, paginationType?: string }
   * >}
   */ (
    async (request, response) => {
      const { searchTerm, cursor, paginationType } = request.query;

      if (
        (searchTerm !== undefined && typeof searchTerm !== "string") ||
        (cursor !== undefined && typeof cursor !== "string") ||
        (paginationType !== undefined && typeof paginationType !== "string")
      ) {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      const { session } = response.locals.shopify;
      const { shop } = session;

      const titleQuery =
        searchTerm
          ?.split(/\s+/)
          .map((term) => `*${term}*`)
          .join(" ") || "";

      try {
        /**
         * @type {{
         *   products: {
         *    edges: {
         *     cursor: string,
         *     node: {
         *       id: string,
         *       handle: string,
         *       title: string,
         *       featuredImage?: { url: string },
         *       variants: {
         *         edges: {
         *           node: {
         *             id: string,
         *             displayName: string,
         *             price: string,
         *             image?: { url: string }
         *           }
         *         }[]
         *       }
         *     }
         *    }[],
         *    pageInfo: IPageInfo
         *   }
         * } | undefined}
         */
        const productsResult = await sendQuery(
          `{
            products(
              ${paginationType === "before" ? "last" : "first"}: 5,
              sortKey: TITLE,
              ${cursor ? `${paginationType}: "${cursor}"` : ""},
              ${titleQuery ? `query: "title:${titleQuery}"` : ""}
            ) {
              edges {
                cursor,
                node {
                  id,
                  handle
                  title,
                  featuredImage { url } 
                  variants(first: 50) { edges { node { id, displayName, price, image { url } } } }
                }
              }
              pageInfo { hasNextPage, hasPreviousPage }
            }
          }`,
          session
        );

        response.send({
          variants:
            productsResult?.products.edges.flatMap(({ cursor: productCursor, node: product }) =>
              product.variants.edges.map((edge) => ({
                productId: product.id,
                id: edge.node.id,
                handle: product.handle,
                displayName: edge.node.displayName,
                price: edge.node.price,
                imageUrl: edge.node.image?.url || product.featuredImage?.url,
                cursor: productCursor,
              }))
            ) || [],
          pageInfo: productsResult?.products.pageInfo || {},
        });
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Internal Server Error" });
      }
    }
  )
);

app.get(
  "/api/variants/:variantId", // Use a route parameter for the variant ID
  /** @type {ExpressHandler<ProductVariant>} */ (
    async (request, response) => {
      const { variantId } = request.params;

      if (!variantId) {
        response.status(400).send({ error: "No variant ID provided" });
        return;
      }

      const { session } = response.locals.shopify;
      const { shop } = session;

      try {
        /**
         * @type {{
         *   productVariant: {
         *     id: string,
         *     displayName: string,
         *     price: string,
         *     image?: { url: string },
         *     product: {
         *       id: string,
         *       handle: string,
         *       title: string,
         *       featuredImage?: { url: string }
         *     }
         *   }
         * } | undefined}
         */
        const variantResult = await sendQuery(
          `{
            productVariant(id: "${variantId}") {
              id
              displayName
              price
              image { url }
              product { id, handle, title, featuredImage { url } }
            }
          }`,
          session
        );

        // Check if the query returned a variant and send a response accordingly
        if (variantResult && variantResult.productVariant) {
          const { productVariant } = variantResult;

          response.send({
            productId: productVariant.product.id,
            id: productVariant.id,
            handle: productVariant.product.handle,
            displayName: productVariant.displayName,
            price: productVariant.price,
            imageUrl: productVariant.image?.url || productVariant.product.featuredImage?.url,
          });
        } else {
          response.status(404).send({ error: "Variant not found" });
        }
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Internal Server Error" });
      }
    }
  )
);

app.post(
  "/api/subscribe",
  /** @type {ExpressHandler<SubscribeResponse>} */ (
    async (_, response) => {
      const { session } = response.locals.shopify;
      const subscriptionRedirect = await getSubscriptionRedirect(session);

      if (subscriptionRedirect) {
        response.send({ subscribeUrl: subscriptionRedirect });
      } else {
        response.status(403).send({ error: "Billing is not enabled" });
      }
    }
  )
);

app.get(
  "/api/shop/details",
  /** @type {ExpressHandler<IShopDetails>} */ (
    async (request, response) => {
      const { chargeId } = request.query;

      const { session } = response.locals.shopify;
      const { shop } = session;

      try {
        const shopDetails = await getShopDetails(session, chargeId);
        response.send(shopDetails);
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Internal Server Error" });
      }
    }
  )
);

app.post(
  "/api/store/update",
  /** @type {ExpressHandler<{}, { isSubscriptionActive?: string }, { isSubscriptionActive?: string }>} */ (
    async (request, response) => {
      const { isSubscriptionActive } = request.body;

      const { session } = response.locals.shopify;
      const { shop } = session;

      if (isSubscriptionActive === "false") {
        const oldShopProperties = await getShopProperties(shop);

        setShopProperties({ isSubscriptionActive: false }, oldShopProperties, shop, false);

        response.send({ status: "success" });
        return;
      }

      try {
        /**
         * @type {[
         *   DatastoreEntity<IShop> | undefined,
         *   { shop?: { email: string, plan: { partnerDevelopment: boolean } } } | undefined
         * ]}
         */
        const [shopProperties, shopResult] = await Promise.all([
          getShopProperties(shop),
          sendQuery("{ shop { email, plan { partnerDevelopment } } }", session),
        ]);

        setProfileProperties(shop, {
          "is dev": !!shopResult?.shop?.plan.partnerDevelopment,
          is_paid: !!shopProperties?.isSubscriptionActive,
          email: shopResult?.shop?.email,
        });

        await setWatermarkVisibility(
          session,
          !shopProperties?.isSubscriptionActive &&
            !shopResult?.shop?.plan.partnerDevelopment &&
            !shopProperties?.isGrandfathered
        );

        response.send({ status: "success" });
      } catch (error) {
        captureException(error, shop);
        response.status(500).send({ error: "Internal Server Error" });
      }
    }
  )
);

app.get(
  "/api/store/crisp-session",
  /** @type {ExpressHandler<CrispSessionResponse>} */ (
    async (_, response) => {
      const { shop } = response.locals.shopify.session;

      const shopProperties = await getShopProperties(shop);
      const shouldSaveSession = !!shopProperties?.crispSessionId;
      const crispSessionId = shopProperties?.crispSessionId || crypto.randomUUID();

      if (!shouldSaveSession) {
        await setShopProperties({ crispSessionId }, shopProperties, shop, true);
      }

      response.send({
        crispSessionId,
        user: { email: get(shopProperties, "email", "") },
        session: {
          shopEmail: get(shopProperties, "email", ""),
          shopName: get(shopProperties, "name", ""),
          domain: get(shopProperties, "domain", ""),
          url: get(shopProperties, "url", ""),
          isSubscriptionActive: get(shopProperties, "isSubscriptionActive", false),
          plan: get(shopProperties, "plan", ""),
          shopifyPlanName: get(shopProperties, "shopifyPlan.displayName", ""),
          isDevelopmentStore: get(shopProperties, "shopifyPlan.partnerDevelopment", false),
        },
      });
    }
  )
);

app.post(
  "/api/imageUpload/createUrls",
  /** @type {ExpressHandler<ImageUploadCreateUrlsResponse, ImageUploadCreateUrlsRequest>} */ (
    async (request, response) => {
      const { images } = request.body;

      if (!images.length) {
        response.send({ data: [] });
        return;
      }

      const { session } = response.locals.shopify;

      try {
        const imageUploadRequests = await createUploadUrls(images, "IMAGE", session);
        response.send({ data: imageUploadRequests });
      } catch (error) {
        captureException(error, session.shop);

        response
          .status(error instanceof Error ? 400 : 500)
          .send({ error: error instanceof Error ? error.message : "Internal server error" });
      }
    }
  )
);

app.post(
  "/api/imageUpload/createFiles",
  /** @type {ExpressHandler<ImageUploadCreateFilesResponse, ImageUploadCreateFilesRequest>} */ (
    async (request, response) => {
      const { images } = request.body;

      if (!images.length) {
        response.send({ data: [] });
        return;
      }

      const { session } = response.locals.shopify;

      try {
        const imageFiles = await createFiles(images, "IMAGE", session);
        response.send({ data: imageFiles });
      } catch (error) {
        captureException(error, session.shop);

        response
          .status(error instanceof Error ? 400 : 500)
          .send({ error: error instanceof Error ? error.message : "Internal server error" });
      }
    }
  )
);

app.use("/api", settingsRoutes);

app.post(
  "/api/track-event",
  /** @type {ExpressHandler<{}>} */ (
    (request, response) => {
      const { session } = response.locals.shopify;
      const { eventName, properties } = request.body;

      if (typeof eventName !== "string") {
        response.status(400).send({ error: "Bad Request" });
        return;
      }

      trackEvent(eventName, session.shop, properties);

      response.send({});
    }
  )
);

app.use(shopify.cspHeaders());

app.use(serveStatic(staticPath, { index: false }));

// Main app page
const [indexHtml, developmentScript] = await Promise.all([
  await readFile(join(staticPath, "index.html")),
  ...(!isProduction ? [await readFile(join(staticPath, "development.js"))] : []),
]);

const indexContent = indexHtml
  .toString()
  .replace(
    "<!-- %DEVELOPMENT_SCRIPT% -->",
    developmentScript ? `<script type="module">${developmentScript.toString()}</script>` : ""
  )
  .replace(/%SHOPIFY_API_KEY%/g, process.env.SHOPIFY_API_KEY || "");

app.use(
  "/*",
  // redirectToHomeIfNoShopQuery,
  // shopify.ensureInstalledOnShop(),
  async (request, response, next) => {
    const url = new URL(`${request.protocol}://${request.get("host")}${request.originalUrl}`);
    const chargeId = new URLSearchParams(url.search).get("charge_id");

    const [shopDetails, nextValue] = await Promise.all([
      request.query.shop
        ? getShopDetails(await loadSession(String(request.query.shop), "getting shop details"), chargeId || undefined)
        : undefined,
      new Promise((resolve) => {
        shopify.ensureInstalledOnShop()(request, response, resolve);
      }),
    ]);

    request.shopDetails = shopDetails;

    return next(nextValue);
  },
  /** @type {ExpressHandler<string>} */ (
    async ({ query, shopDetails, params }, response) => {
      const { shop } = query;

      // /** @type {ISettings['language'] | undefined}} */
      // let language;

      // /** @type {boolean}} */
      // let isIntegrationActive = false;

      // if (typeof shop === 'string') {
      //   const shopProperties = await getShopProperties(shop);

      //   language = shopProperties?.settings?.language;
      //   isIntegrationActive = getIntegrationActive(shopProperties?.settings);
      // }

      // language = language || defaultSettings.language;

      if (typeof shop === "string") {
        trackEvent("load main page", shop);
      }

      let pageContent = getPageStaticSkeletonContent(indexContent, params);

      // Replace %SHOP_DETAILS% after handling %STATIC_LOADER_DIV_PLACEHOLDER%
      pageContent = pageContent.replace("%SHOP_DETAILS%", JSON.stringify(shopDetails || {}));

      response.status(200).set("Content-Type", "text/html").send(pageContent);
    }
  )
);

Sentry.setupExpressErrorHandler(app);

app.listen(port);
