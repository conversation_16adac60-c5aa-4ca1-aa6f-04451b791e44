/** @type {ISettings} */
export const defaultSettings = {
  language: 'hu',
  apiKey: '',
  trigger: 'paid',
  fulfillmentDateOffset: 0,
  quantityUnit: 'db',
  invoiceLanguage: 'hu',
  invoiceType: 'electronic',
  isSendEmail: true,
  dueDateOffset: 0,
  vatRate: 'EUKT',
};

/** @type {Partial<ISettings>} */
export const defaultOptionalSettings = {
  isDisableForPOS: false,
  isDisableReversingInvoice: false,
  invoicePrefix: '',
  comment: '',
  isShowOrderNumber: false,
  isShowSKU: false,
  isUseCustomerLanguage: false,
  bankAccountNumber: '',
  isPreventSameOrderNumberInvoices: false,
  paymentMethodOverrides: [],
  vatRateEntitlement: 'SECOND_HAND',
  companyInvoiceType: 'disabled',
};

export const defaultPaymentMethodName = 'bankcard';

export const defaultOptionType = 'Checkbox';

export const defaultConditionRelation = 'in';
export const defaultActionType = 'hide';
export const defaultRule = { conditions: [{}], actions: [{}] };

export const defaultProductSelectionMethod = 'manual';
export const defaultAutomaticProductSelectionMethod = 'collection';

export const defaultFreePlanMaxOptionSets = 5;

export const defaultStyleOptions = {
  cssCustomizations: {},
  cssText: '',

  appEmbedPlacementSelector: '',
  cartLineSelector: '',
  quantityAndRemoveButtonSelector: '',
  regularPriceItemSelector: '',
  compareAtPriceItemSelector: '',
  isGetPricesOnStorefront: false,

  // Text customizations
  dropdownPlaceholder: 'Choose %option-title%',
  fileUploadButtonText: 'Choose file',
  dragAndDropText: 'or drop files to upload',
  uploadLoadingText: 'Uploading...',
  fileTypeErrorText: 'Only %file-types% files allowed.',
  fileSizeErrorText: 'File needs to be smaller than %max-file-size%.',
  fileUploadErrorText: 'Failed to upload file: %reason%',
  requiredOptionErrorText: '"%option-title%" is required.',
  minimumSelectionErrorText: '"%option-title%" requires at least %min-selection-limit% selections.',
  maximumSelectionErrorText: '"%option-title%" allows only %max-selection-limit% selections.',
  mimumCharacterErrorText: '"%option-title%" requires at least %min-char-limit% characters.',

  isMatchAddonQuantity: false,
  isDisableOrderIfAddonOutOfStock: false,
  isBuyNowIntegrationDisabled: false,
  isEnableRemoveAddonFromCart: false,
  isEnableOpenAddonProductPage: false,
  isChangeBaseProductPriceDisabled: false,
  isShowTotalPrice: true,
  storefrontFooterText: 'Total add-ons:',
};
