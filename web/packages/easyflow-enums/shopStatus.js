// shopStatus.js
export const ACTIVE = 'ACTIVE';
export const INACTIVE = 'INACTIVE';
export const UNINSTALLED = 'UNINSTALLED';
export const CLOSED = 'CLOSED';
export const DELETED = 'DELETED';

export const getShopStatusByResponseCode = (/** @type {number} */ code) => {
  switch (code) {
    case 401:
      return UNINSTALLED;
    case 402:
      return CLOSED;
    default:
      return UNINSTALLED;
  }
};
