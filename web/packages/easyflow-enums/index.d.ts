declare module "easyflow-enums" {
  export const shopStatus: {
    ACTIVE: string;
    INACTIVE: string;
    UNINSTALLED: string;
    CLOSED: string;
    DELETED: string;
    getShopStatusByResponseCode: (code: number) => string;
  };
  export const subscriptionTypes: {
    FREE: string;
    PRO: string;
  };
  export const planTypes: {
    FREE: string;
    PRO: string;
    BLACK_FRIDAY_22?: string;
  };
  export const planStatus: {
    ACTIVE: boolean;
    INACTIVE: boolean;
  };
  export const subscriptionIntervals: {
    MONTHLY: string;
    YEARLY: string;
    LIFETIME: string;
    VISIONARY: string;
    USAGE: string;
  };
  export const appSubscriptionIntervals: {
    ANNUAL: string;
    EVERY_30_DAYS: string;
  };
  export const appSubscriptionReplacement: {
    APPLY_IMMEDIATELY: string;
    APPLY_ON_NEXT_BILLING_CYCLE: string;
    STANDARD: string;
  };
  export const appSubscriptionStatus: {
    ACCEPTED: string;
    ACTIVE: string;
    CANCELLED: string;
    DECLINED: string;
    EXPIRED: string;
    FROZEN: string;
    PENDING: string;
  };
  export const planIntervals: {
    MONTHLY: string;
    YEARLY: string;
    LIFETIME: string;
    VISIONARY: string;
    USAGE: string;
  };
  export const discountTypes: {
    AMOUNT: string;
    PERCENT: string;
  };
  export const couponStatus: {
    ACTIVE: boolean;
    INACTIVE: boolean;
  };
  export const transactionTypes: {
    NEW: string;
    RENEWAL: string;
    UPGRADE: string;
    DOWNGRADE: string;
    FAILED: string;
    CANCELED: string;
  };
  export const apiResponseCodes: {
    SUCCESS: number;
    FAILED: number;
    AUTH_FAILED: number;
    NOT_FOUND: number;
    VALIDATION_ERROR: number;
    SERVER_ERROR: number;
  };
}
