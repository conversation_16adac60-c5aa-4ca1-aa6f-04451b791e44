{"private": true, "name": "product-options-web", "version": "1.0.0", "license": "UNLICENSED", "type": "module", "scripts": {"debug": "env-cmd -f ../.env.development node --inspect-brk main.js", "dev": "env-cmd -f ../.env nodemon main.js --ignore ./frontend", "serve": "env-cmd -f ../.env pm2 start main.js --no-daemon --max-memory-restart 500M", "staging-serve": "env-cmd -f ../.env.staging pm2 start main.js --no-daemon --max-memory-restart 300M", "db:migrate": "env-cmd -f ../.env.development -- pnpm prisma migrate dev", "prisma:generate": "env-cmd -f ../.env.development -- pnpm prisma generate", "prisma-db:pull": "env-cmd -f ../.env.development -- pnpm prisma db pull", "postinstall": "env-cmd -f ../.env -- pnpm prisma generate"}, "engines": {"node": "^20.0.0"}, "dependencies": {"@google-cloud/datastore": "^9.2.1", "@google-cloud/secret-manager": "^5.6.0", "@prisma/client": "^6.2.1", "@sentry/integrations": "^7.114.0", "@sentry/node": "^8.8.0", "@shopify/shopify-api": "^11.8.1", "@shopify/shopify-app-express": "^5.0.0", "async": "^3.2.5", "body-parser": "^1.20.2", "express": "^4.19.2", "lodash": "^4.17.21", "mixpanel": "^0.18.0", "pm2": "^5.4.0", "prisma": "^6.2.1", "serve-static": "^1.15.0", "zod": "^3.24.1"}, "devDependencies": {"@types/async": "^3.2.24", "@types/express": "^4.17.21", "@types/serve-static": "^1.15.7", "nodemon": "^3.1.3"}}