/**
 * Generated from examples at https://shopify.dev/docs/api/admin-rest/2024-04/resources/webhook
 * and https://shopify.dev/docs/api/admin-rest/2024-04/resources/product
 * and from an example webhook payload
 * using https://app.quicktype.io/
 */

export interface Product {
  admin_graphql_api_id?: string;
  body_html:             string;
  created_at:            null | string;
  handle:                string;
  id:                    number;
  product_type:          string;
  published_at:          null | string;
  template_suffix:       null | string;
  title:                 string;
  updated_at:            string;
  vendor:                string;
  status:                string;
  published_scope:       string;
  tags:                  string;
  variants:              Variant[];
  options:               Option[] | Option;
  images:                Image[];
  image?:                Image | null;
  variant_gids?:         VariantGid[];
}

export interface Image {
  id:                    number;
  alt?:                  null;
  position:              number;
  product_id:            number;
  created_at:            string;
  updated_at:            string;
  admin_graphql_api_id?: string;
  width:                 number;
  height:                number;
  src:                   string;
  variant_ids:           Array<VariantIDClass | number>;
}

export interface VariantIDClass {
}

export interface Option {
  id:         number;
  product_id: number;
  name:       string;
  position:   number;
  values:     string[];
}

export interface VariantGid {
  admin_graphql_api_id: string;
  updated_at:           string;
}

export interface Variant {
  admin_graphql_api_id?:   string;
  barcode:                 null | string;
  compare_at_price:        null | string;
  created_at:              string;
  fulfillment_service:     string;
  id:                      number;
  inventory_management:    null | string;
  inventory_policy:        string;
  position:                number;
  price:                   number | string;
  product_id:              number;
  sku:                     string;
  taxable:                 boolean;
  title:                   string;
  updated_at:              string;
  option1:                 string;
  option2?:                null;
  option3?:                null;
  grams:                   number;
  image_id?:               number | null;
  weight:                  number;
  weight_unit:             string;
  inventory_item_id:       number | null;
  inventory_quantity:      number;
  old_inventory_quantity?: number;
  requires_shipping:       boolean;
  presentment_prices?:     PresentmentPrice[];
}

export interface PresentmentPrice {
  price:            PriceClass;
  compare_at_price: null;
}

export interface PriceClass {
  amount:        string;
  currency_code: string;
}

