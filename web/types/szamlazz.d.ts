declare module 'szamlazz.js' {
  class Currency<
    TValue extends string = string,
    TRoundPriceExp extends number = number,
    TComment extends string = string,
  > {
    value: TValue;
    roundPriceExp: TRoundPriceExp;
    comment: TComment;

    constructor(value: string, roundPriceExp: number, comment: string);
    toString(): string;
  }

  const Currencies: {
    Ft: Currency<'Ft', 0, 'Hungarian Forint'>;
    HUF: Currency<'HUF', 0, 'Hungarian Forint'>;
    EUR: Currency<'EUR', 2, 'Euro'>;
    CHF: Currency<'CHF', 2, 'Swiss Franc'>;
    USD: Currency<'USD', 2, 'US Dollar'>;
    AUD: Currency<'AUD', 2, 'Australian Dollar'>;
    AED: Currency<'AED', 2, '<PERSON>irati <PERSON>'>;
    BGN: Currency<'BGN', 2, 'Bulgarian Lev'>;
    CAD: Currency<'CAD', 2, 'Canadian Dollar'>;
    CNY: Currency<'CNY', 2, 'Chinese Yuan Renminbi'>;
    CZK: Currency<'CZK', 2, 'Czech Koruna'>;
    DKK: Currency<'DKK', 2, 'Danish Krone'>;
    EEK: Currency<'EEK', 2, 'Estonian Kroon'>;
    GBP: Currency<'GBP', 2, 'British Pound'>;
    HRK: Currency<'HRK', 2, 'Croatian Kuna'>;
    ISK: Currency<'ISK', 2, 'Icelandic Krona'>;
    JPY: Currency<'JPY', 2, 'Japanese Yen'>;
    LTL: Currency<'LTL', 2, 'Lithuanian Litas'>;
    LVL: Currency<'LVL', 2, 'Latvian Lats'>;
    NOK: Currency<'NOK', 2, 'Norwegian Krone'>;
    NZD: Currency<'NZD', 2, 'New Zealand Dollar'>;
    PLN: Currency<'PLN', 2, 'Polish Zloty'>;
    RON: Currency<'RON', 2, 'Romanian New Leu'>;
    RUB: Currency<'RUB', 2, 'Russian Ruble'>;
    SEK: Currency<'SEK', 2, 'Swedish Krona'>;
    SKK: Currency<'SKK', 2, 'Slovak Koruna'>;
    UAH: Currency<'UAH', 2, 'Ukrainian Hryvnia'>;
  };

  class Language<TValue extends string = string, TName extends string = string> {
    value: TValue;
    name: TName;

    constructor(value: string, name: string);
    toString(): string;
  }

  const Languages: {
    Hungarian: Language<'hu', 'Hungarian'>;
    English: Language<'en', 'English'>;
    German: Language<'de', 'German'>;
    Italian: Language<'it', 'Italian'>;
    Romanian: Language<'ro', 'Romanian'>;
    Slovak: Language<'sk', 'Slovak'>;
  };

  class PaymentMethod<TValue extends string = string, TComment extends string = string> {
    value: TValue;
    comment: TComment;

    constructor(value: string, comment: string);
    toString(): string;
  }

  const PaymentMethods: {
    Cash: PaymentMethod<'Készpénz', 'cash'>;
    BankTransfer: PaymentMethod<'Átutalás', 'bank transfer'>;
    CreditCard: PaymentMethod<'Bankkártya', 'credit card'>;
    PayPal: PaymentMethod<'PayPal', 'PayPal'>;
  };

  class TaxSubject<TValue extends number = number, TComment extends string = string> {
    value: TValue;
    comment: TComment;

    constructor(value: number, comment: string);
    toString(): string;
  }

  const TaxSubjects: {
    NonEUCompany: TaxSubject<7, 'Company outside EU'>;
    EUCompany: TaxSubject<6, 'Company within EU'>;
    HungarianTaxID: TaxSubject<1, 'Has Hungarian VAT ID'>;
    Unknown: TaxSubject<0, 'Unknown VAT status'>;
    NoTaxID: TaxSubject<-1, 'Has no Hungarian VAT ID'>;
  };

  class Seller {
    constructor(options?: {
      bank?: { name?: string; accountNumber?: string };
      email?: {
        replyToAddress?: string;
        subject?: string;
        message?: string;
      };
      issuerName?: string;
    });
  }

  class Buyer {
    constructor(options: {
      name: string;
      country?: string;
      zip: string;
      city: string;
      address: string;
      email?: string;
      sendEmail?: boolean;
      taxSubject: TaxSubject;
      taxNumber?: string;
      taxNumberEU?: string;
      postAddress?: {
        name?: string;
        country?: string;
        zip?: string;
        city?: string;
        address?: string;
      };
      identifier?: number;
      issuerName?: string;
      phone?: string;
      comment?: string;
    });
  }

  type HandledVatType = 'TAM' | 'AAM' | 'EU' | 'EUK' | 'MAA' | 'ÁKK' | 'TEHK' | 'HO' | 'KBAET';
  type UnhandledVatType =
    | 'F.AFA'
    | 'K.AFA'
    | 'TAHK'
    | 'EUT'
    | 'EUKT'
    | 'EUE'
    | 'EUFADE'
    | 'EUFAD37'
    | 'ATK'
    | 'NAM'
    | 'EAM'
    | 'KBAUK';

  type Vat =
    | { vat: number | HandledVatType; netUnitPrice: number }
    | { vat: number | HandledVatType; grossUnitPrice: number }
    | {
        vat: UnhandledVatType;
        netUnitPrice?: number;
        netValue?: number;
        vatValue?: number;
        grossValue?: number;
      };

  type ItemOptions = {
    label: string;
    quantity: number;
    unit: string;
    comment?: string;
  } & Vat;

  class Item {
    constructor(options: ItemOptions);
  }

  class Invoice {
    constructor(options: {
      issueDate: Date;
      fulfillmentDate: Date;
      dueDate: Date;
      paymentMethod: PaymentMethod;
      currency: Currency;
      language: Language;
      exchangeRate?: number;
      exchangeBank?: string;
      seller: Seller;
      buyer: Buyer;
      items: Item[];
      orderNumber?: string;
      proforma?: boolean;
      invoiceIdPrefix?: string;
      paid?: boolean;
      comment?: string;
      logoImage?: string;
      prepaymentInvoice?: boolean;
    });

    // Private members (remove when accessing them is not necessary anymore)
    _generateXML(indentLevel?: number): string;
  }

  interface IInvoiceReturn {
    invoiceId: string;
    netTotal: string;
    grossTotal: string;
    pdf?: Buffer;
  }

  type Authentication = XOR<
    { user: string; password: string; authToken: void },
    { authToken: string }
  >;

  type ClientOptions = {
    eInvoice?: boolean;
    requestInvoiceDownload?: boolean;
    downloadedInvoiceCount?: number;
    responseVersion?: number;
    timeout?: number;
  } & Authentication;

  class Client {
    constructor(options: ClientOptions);

    reverseInvoice(options: {
      invoiceId: string;
      eInvoice: boolean;
      requestInvoiceDownload: boolean;
    }): Promise<IInvoiceReturn>;

    issueInvoice(invoice: Invoice): Promise<IInvoiceReturn>;

    // Private members (remove when accessing them is not necessary anymore)
    _options: ClientOptions;

    _getXmlHeader(tag: string, dir: string): string;
    _getAuthFields(): [keyof Authentication, Authentication[keyof Authentication]][];

    _sendRequest(
      fileFieldName: string,
      data: string,
      isBinaryDownload: boolean,
    ): Promise<import('axios').AxiosResponse<string>>;
  }
}
