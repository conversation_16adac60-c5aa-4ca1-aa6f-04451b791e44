interface IPageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

type OptionItems = (
  | { optionSets: IProductOptionSet[]; options?: IProductOption[] }
  | { optionSets?: IProductOptionSet[]; options: IProductOption[] }
) & { optionSetsCursor?: string; optionsCursor?: string };

type OptionItemIds =
  | { optionSetIds: number[]; optionIds?: number[] }
  | { optionSetIds?: number[]; optionIds: number[] };

// /api/products/get-handle
type ProductsGetHandleResponse = { handle: string };

// /api/products/options
type ProductsOptionsGetResponse = Record<
  number,
  Pick<import('./product').Option, 'name' | 'values'>[]
>;

// /api/products/get
type ProductsGetResponse = { products: Product[]; pageInfo: Partial<IPageInfo> };

// /api/collections/get
type CollectionsGetResponse = { id: string; title: string }[];

// /api/variants/get
type VariantsGetResponse = { variants: ProductVariant[]; pageInfo: Partial<IPageInfo> };

// /api/subscribe
type SubscribeResponse = { subscribeUrl: string };

// /api/store/crisp-session
type CrispSessionResponse = {
  crispSessionId: string;
  user?: { email?: string };
  session?: {
    shopEmail?: string;
    shopName?: string;
    domain?: string;
    url?: string;
    isSubscriptionActive?: boolean;
    plan?: string;
    shopifyPlanName?: string;
    isDevelopmentStore?: boolean;
  };
};

/* File upload */

// /appProxy/createUploadUrl
type FileData = { filename: string; mimeType: string };
type CreateUploadUrlRequest = { file: FileData };

type CreateUploadUrlResponse = { data?: Omit<FileUploadRequest, 'url'> & { url: string } };

// /appProxy/createUploadFile
type FileUploadData = { filename: string; resourceUrl: string; altText?: string };
type CreateUploadFileRequest = { file: FileUploadData; contentType: 'FILE' | 'IMAGE' };

type CreateUploadFileResponse = {
  data?: IProductOption['values'][number]['image'];
  error?: string;
};

// /api/imageUpload/createUrls
type ImageUploadCreateUrlsRequest = { images: FileData[] };

type ImageUploadCreateUrlsResponse = {
  data?: (Omit<FileUploadRequest, 'url'> & { url: string })[];
};

// /api/imageUpload/createFiles
type ImageUploadCreateFilesRequest = { images: FileUploadData[] };

type ImageUploadCreateFilesResponse = { data?: IProductOption['values'][number]['image'][] };

// General types

type Product = Pick<import('./product').Product, 'title'> & {
  id: string;
  imageUrl?: string;
  cursor?: string;
};

type ProductVariant = {
  id: string;
  handle: string;
  productId: string;
  displayName: string;
  price: string;
  imageUrl?: string;
  cursor?: string;
};

type Variant = Pick<import('./product').Variant, 'id' | 'title'> & { imageUrl?: string };
type PageInfo = { hasNextPage: boolean; hasPreviousPage: boolean };

type FileUploadRequest = {
  resourceUrl?: string;
  url?: string;
  parameters: {
    name: string;
    value: string;
  }[];
};
