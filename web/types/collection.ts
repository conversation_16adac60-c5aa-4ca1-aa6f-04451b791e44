/**
 * Generated from examples at https://shopify.dev/docs/api/admin-rest/2024-04/resources/webhook,
 * https://shopify.dev/docs/api/admin-rest/2024-04/resources/collection,
 * https://shopify.dev/docs/api/admin-rest/2024-04/resources/customcollection,
 * and https://shopify.dev/docs/api/admin-rest/2024-04/resources/smartcollection
 * using https://app.quicktype.io/
 */

export interface Collection {
  id:                    number;
  handle:                string;
  title:                 string;
  updated_at:            string;
  body_html:             null | string;
  published_at:          string;
  sort_order:            null | string;
  template_suffix:       null | string;
  published_scope:       string;
  admin_graphql_api_id?: string;
  image?:                Image;
  products_count?:       number;
  collection_type?:      string;
  published?:            boolean;
  rules?:                Rule[];
  disjunctive?:          boolean;
}

export interface Image {
  src:         string;
  alt:         string;
  width?:      number;
  height?:     number;
  created_at?: string;
}

export interface Rule {
  column:    string;
  relation:  string;
  condition: string;
}
