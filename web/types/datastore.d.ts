type DatastoreKeySymbol = typeof import("@google-cloud/datastore/build/src/entity").entity.KEY_SYMBOL;
type DatastoreKey = import("@google-cloud/datastore").Key;

// Entity
type WithDatastoreKey<T> = T & { [key in DatastoreKeySymbol]?: DatastoreKey };

type DatastoreEntity<T> = WithDatastoreKey<Stored<T>>;
type ProjectedDatastoreEntity<T, TFields extends keyof Stored<T>> = WithDatastoreKey<
  Pick<Projected<Stored<T>>, TFields>
>;

// Get and Query
type GetResponse<T> = T extends (infer U)[] ? [DatastoreEntity<U>[]] : [DatastoreEntity<T> | undefined];

type QueryResponse<T> = [DatastoreEntity<T>[], import("@google-cloud/datastore/build/src/query.js").RunQueryInfo];

type ProjectionQueryResponse<TData, TFields extends keyof Stored<TData>> = [
  ProjectedDatastoreEntity<TData, TFields>[],
  import("@google-cloud/datastore/build/src/query.js").RunQueryInfo,
];

type CollectedProjection<T, TFields extends keyof Stored<T>> = WithDatastoreKey<
  Pick<{ [TField in keyof Stored<T>]: ProjectedField<Stored<T>[TField]> }, TFields>
>;

// Projected and Stored types
type ProjectedField<T> = T extends Date ? number : T;
type Projected<T> = {
  [K in keyof T]: T extends (infer TElement)[] ? TElement : ProjectedField<T[K]>;
};

type Stored<T> = T extends IProductOptionSet
  ? StoredProductOptionSet
  : T extends IProductOption
    ? StoredProductOption
    : T;

type StoredProductOptionSet = Omit<IProductOptionSet, "id" | "createdAt"> & { createdAt: Date };
type StoredProductOption = Omit<IProductOption, "id" | "createdAt"> & {
  createdAt: Date;
  metaobjectId: string;
};

type ErrorType =
  | (typeof import("../utilities/error.js").criticalErrorTypes)[number]
  | "invalid-address"
  | "unsupported-currency"
  | "shop-mismatch"
  | "unsupported-vat"
  | "duplicate-order-number"
  | "system-maintenance";

interface IShop {
  globalId?: string;
  settings?: ISettings;

  /**
   * `true` if the user is on the paid plan, `false` if it's a free plan,
   * `undefined` if the user haven't chosen a plan yet.
   */
  isSubscriptionActive?: boolean;
  isOn18DollarPlan?: boolean;

  /** Identifies shops that are not charged a billing fee. */
  isGrandfathered?: boolean;
  isAddonsFree?: boolean;

  isExtensionEnabled?: boolean;

  crispSessionId?: string;
  lastError?: {
    action: "generate-invoice" | "reverse-invoice";
    type: ErrorType;
    displayValue?: unknown;
    orderId?: number;
    orderName?: string;
    isCritical?: boolean;
  };
  status?: string;

  // new added fields
  domain?: string;
  planSlug?: string;
  email?: string;
  meta?: object;
  appSubscriptionData?: object;
  appSubscriptionId?: string;
  planData?: object;
}

interface PaymentMethodOverride {
  paymentGatewayName: string;
  paymentMethodName: string;
  isPaid?: boolean;
}

interface ISettings {
  language: "hu" | "en";
  apiKey: string;
  isDisableForPOS?: boolean;
  isDisableReversingInvoice?: boolean;
  trigger: string;
  invoicePrefix?: string;
  fulfillmentDateOffset: number;
  quantityUnit: string;
  comment?: string;
  isShowOrderNumber?: boolean;
  isShowSKU?: boolean;
  invoiceLanguage: string;
  isUseCustomerLanguage?: boolean;
  invoiceType: string;
  isSendEmail: boolean;
  dueDateOffset: number;
  bankAccountNumber?: string;
  isPreventSameOrderNumberInvoices?: boolean;
  vatRate: string;
  vatRateEntitlement?: string;
  paymentMethodOverrides?: PaymentMethodOverride[];
  companyInvoiceType?: "disabled" | "address2" | "custom";
}

interface IInvoice {
  id: string;
  shop: string;
}
