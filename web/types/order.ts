/**
 * Generated from examples at https://shopify.dev/docs/api/admin-rest/2024-04/resources/webhook
 * and https://shopify.dev/docs/api/admin-rest/2024-04/resources/order
 * using https://app.quicktype.io/
 */

export interface Order {
  id:                                  number;
  note:                                null | string;
  email:                               string;
  taxes_included:                      boolean;
  currency:                            string;
  invoice_sent_at?:                    null;
  created_at:                          string;
  updated_at:                          string;
  tax_exempt?:                         boolean;
  completed_at?:                       null;
  name:                                string;
  status?:                             string;
  line_items:                          OrderLineItem[];
  shipping_address:                    IngAddress | null;
  billing_address:                     IngAddress | null;
  invoice_url?:                        string;
  applied_discount?:                   AppliedDiscount;
  order_id?:                           null;
  shipping_line?:                      PurpleShippingLine;
  tax_lines:                           OrderTaxLine[];
  tags:                                string;
  note_attributes:                     NoteAttribute[];
  total_price:                         string;
  subtotal_price:                      string;
  total_tax:                           string;
  payment_terms:                       PaymentTerms | null;
  admin_graphql_api_id?:               string;
  customer:                            Customer | null;
  app_id?:                             number | null;
  browser_ip?:                         null | string;
  buyer_accepts_marketing?:            boolean;
  cancel_reason?:                      null | string;
  cancelled_at?:                       null;
  cart_token?:                         null | string;
  checkout_token?:                     null | string;
  client_details?:                     ClientDetails | null;
  closed_at?:                          null | string;
  company?:                            Company;
  confirmation_number?:                null | string;
  current_total_additional_fees_set?:  Set | null;
  current_total_discounts?:            string;
  current_total_discounts_set?:        CurrentTotalDiscountsSet;
  current_total_duties_set?:           CurrentTotalDutiesSet | null;
  current_total_price?:                string;
  current_total_price_set?:            CurrentTotalPriceSet;
  current_subtotal_price?:             string;
  current_subtotal_price_set?:         CurrentSubtotalPriceSet;
  current_total_tax?:                  string;
  current_total_tax_set?:              CurrentTotalTaxSet;
  customer_locale?:                    null | string;
  discount_applications?:              DiscountApplication[];
  discount_codes?:                     DiscountCode[];
  estimated_taxes?:                    boolean;
  financial_status?:                   string;
  fulfillments?:                       Fulfillment[];
  fulfillment_status?:                 null | string;
  gateway?:                            string;
  landing_site?:                       null | string;
  location_id?:                        number | null;
  merchant_of_record_app_id?:          number | null;
  number?:                             number;
  order_number?:                       number;
  original_total_additional_fees_set?: Set | null;
  original_total_duties_set?:          OriginalTotalDutiesSet | null;
  payment_gateway_names?:              string[];
  phone?:                              null | string;
  po_number?:                          null | string;
  presentment_currency?:               string;
  processed_at?:                       string;
  referring_site?:                     null | string;
  refunds?:                            Refund[];
  shipping_lines?:                     ShippingLineElement[];
  source_name?:                        string;
  source_identifier?:                  null | string;
  source_url?:                         null | string;
  subtotal_price_set?:                 Set;
  test?:                               boolean;
  token?:                              string;
  total_discounts?:                    string;
  total_discounts_set?:                Set;
  total_line_items_price?:             string;
  total_line_items_price_set?:         Set;
  total_outstanding?:                  string;
  total_price_set?:                    Set;
  total_shipping_price_set?:           Set;
  total_tax_set?:                      Set;
  total_tip_received?:                 string;
  total_weight?:                       number;
  user_id?:                            number | null;
  order_status_url?:                   OrderStatusURLClass | string;
  checkout_id?:                        number | null;
  confirmed?:                          boolean;
  contact_email?:                      null | string;
  device_id?:                          null;
  duties_included?:                    boolean;
  landing_site_ref?:                   null | string;
  reference?:                          null | string;
}

export interface AppliedDiscount {
  description: string;
  value:       string;
  title:       string;
  amount:      string;
  value_type:  string;
}

export interface IngAddress {
  first_name:    string;
  address1:      string;
  phone:         string;
  city:          string;
  zip:           string;
  province:      string;
  country:       string;
  last_name:     string;
  address2:      null | string;
  company:       null | string;
  latitude:      number | null | string;
  longitude:     number | null | string;
  name:          string;
  country_code:  string;
  province_code: string;
}

export interface ClientDetails {
  accept_language: null | string;
  browser_height:  number | null;
  browser_ip:      string;
  browser_width:   number | null;
  session_hash:    null | string;
  user_agent:      null | string;
}

export interface Company {
  id:          number;
  location_id: number;
}

export interface CurrentSubtotalPriceSet {
  current_subtotal_price_set?: Set;
  shop_money?:                 Money;
  presentment_money?:          Money;
}

export interface Set {
  shop_money:        Money;
  presentment_money: Money;
}

export interface Money {
  amount:        string;
  currency_code: string;
}

export interface CurrentTotalDiscountsSet {
  current_total_discounts_set?: Set;
  shop_money?:                  Money;
  presentment_money?:           Money;
}

export interface CurrentTotalDutiesSet {
  current_total_duties_set: Set;
}

export interface CurrentTotalPriceSet {
  current_total_price_set?: Set;
  shop_money?:              Money;
  presentment_money?:       Money;
}

export interface CurrentTotalTaxSet {
  current_total_tax_set?: Set;
  shop_money?:            Money;
  presentment_money?:     Money;
}

export interface Customer {
  id:                       number;
  email:                    string;
  created_at:               null | string;
  updated_at:               null | string;
  first_name:               string;
  last_name:                string;
  orders_count?:            number;
  state:                    string;
  total_spent?:             string;
  last_order_id?:           null;
  note:                     null;
  verified_email:           boolean;
  multipass_identifier:     null;
  tax_exempt:               boolean;
  tags:                     string;
  last_order_name?:         null;
  currency:                 string;
  phone:                    null | string;
  tax_exemptions:           any[] | Addresses;
  email_marketing_consent?: EmailMarketingConsent;
  sms_marketing_consent?:   SMSMarketingConsent | null;
  admin_graphql_api_id:     string;
  default_address:          DefaultAddress;
  accepts_marketing?:       boolean;
  addresses?:               Addresses;
}

export interface Addresses {
}

export interface DefaultAddress {
  id?:            number | null;
  customer_id?:   number;
  first_name?:    null;
  last_name?:     null;
  company?:       null;
  address1?:      string;
  address2?:      null | string;
  city?:          string;
  province?:      string;
  country?:       string;
  zip?:           string;
  phone?:         string;
  name?:          string;
  province_code?: string;
  country_code?:  string;
  country_name?:  string;
  default?:       boolean;
}

export interface EmailMarketingConsent {
  state:              string;
  opt_in_level:       null;
  consent_updated_at: null | string;
}

export interface SMSMarketingConsent {
  state:                  string;
  opt_in_level:           string;
  consent_updated_at:     string;
  consent_collected_from: string;
}

export interface DiscountApplication {
  type:              string;
  title?:            string;
  description?:      string;
  value:             string;
  value_type:        string;
  allocation_method: string;
  target_selection:  string;
  target_type:       string;
  code?:             string;
}

export interface DiscountCode {
  code:   string;
  amount: string;
  type:   string;
}

export interface Fulfillment {
  created_at:            string;
  id:                    number;
  order_id:              number;
  status:                string;
  tracking_company:      string;
  tracking_number:       string;
  updated_at:            string;
  admin_graphql_api_id?: string;
  location_id?:          number;
  name?:                 string;
  origin_address?:       Addresses;
  receipt?:              Receipt;
  service?:              string;
  shipment_status?:      null;
  tracking_numbers?:     string[];
  tracking_url?:         string;
  tracking_urls?:        string[];
  line_items?:           FulfillmentLineItem[];
}

export interface FulfillmentLineItem {
  id:                           number;
  admin_graphql_api_id:         string;
  attributed_staffs:            any[];
  current_quantity:             number;
  fulfillable_quantity:         number;
  fulfillment_service:          string;
  fulfillment_status:           null;
  gift_card:                    boolean;
  grams:                        number;
  name:                         string;
  price:                        string;
  price_set:                    Set;
  product_exists:               boolean;
  product_id:                   number;
  properties:                   NoteAttribute[];
  quantity:                     number;
  requires_shipping:            boolean;
  sku:                          string;
  taxable:                      boolean;
  title:                        string;
  total_discount:               string;
  total_discount_set:           Set;
  variant_id:                   number;
  variant_inventory_management: string;
  variant_title:                string;
  vendor:                       null;
  tax_lines:                    PurpleTaxLine[];
  duties:                       any[];
  discount_allocations:         DiscountAllocation[];
}

export interface DiscountAllocation {
  amount:                     string;
  discount_application_index: number;
  amount_set:                 Set;
}

export interface NoteAttribute {
  name:  string;
  value: string;
}

export interface PurpleTaxLine {
  channel_liable: null;
  price:          string;
  price_set:      Set;
  rate:           number;
  title:          string;
}

export interface Receipt {
  testcase:      boolean;
  authorization: string;
}

export interface OrderLineItem {
  id:                            number;
  variant_id:                    number | null;
  product_id:                    number | null;
  title:                         string;
  variant_title:                 null | string;
  sku:                           null | string;
  vendor:                        null | string;
  quantity:                      number;
  requires_shipping:             boolean;
  taxable:                       boolean;
  gift_card:                     boolean;
  fulfillment_service:           string;
  grams:                         number;
  tax_lines:                     FluffyTaxLine[];
  applied_discount?:             null;
  name:                          string;
  properties:                    NoteAttribute[];
  custom?:                       boolean;
  price:                         string;
  admin_graphql_api_id?:         string;
  attributed_staffs?:            AttributedStaff[];
  fulfillable_quantity?:         number;
  fulfillment_status?:           null | string;
  current_quantity?:             number;
  price_set?:                    Set;
  total_discount?:               string;
  total_discount_set?:           Set;
  discount_allocations?:         DiscountAllocation[];
  origin_location?:              OriginLocation;
  duties?:                       Duty[];
  product_exists?:               boolean;
  variant_inventory_management?: null | string;
}

export interface AttributedStaff {
  id:       string;
  quantity: number;
}

export interface Duty {
  id:                     string;
  harmonized_system_code: string;
  country_code_of_origin: string;
  shop_money:             Money;
  presentment_money:      Money;
  tax_lines:              DutyTaxLine[];
  admin_graphql_api_id:   string;
}

export interface DutyTaxLine {
  title:          string;
  price:          string;
  rate:           number;
  price_set:      Set;
  channel_liable: boolean;
}

export interface OriginLocation {
  id:            number;
  country_code:  string;
  province_code: string;
  name:          string;
  address1:      string;
  address2:      string;
  city:          string;
  zip:           string;
}

export interface FluffyTaxLine {
  title:          string;
  price:          string;
  price_set:      Set;
  channel_liable: boolean | null;
  rate:           number;
}

export interface OrderStatusURLClass {
  order_status_url: string;
}

export interface OriginalTotalDutiesSet {
  original_total_duties_set: Set;
}

export interface PaymentTerms {
  id?:                number;
  payment_terms_name: string;
  payment_terms_type: string;
  due_in_days:        number;
  created_at?:        string;
  updated_at?:        string;
  payment_schedules:  PaymentSchedule[];
  amount?:            number;
  currency?:          string;
}

export interface PaymentSchedule {
  id?:                      number;
  created_at?:              string;
  updated_at?:              string;
  payment_terms_id?:        number;
  reference_id?:            null;
  reference_type?:          null;
  issued_at:                string;
  due_at:                   string;
  completed_at:             string;
  amount:                   number | string;
  currency:                 string;
  expected_payment_method?: string;
}

export interface Refund {
  id:                         number;
  order_id:                   number;
  created_at:                 string;
  note:                       null | string;
  user_id:                    number | null;
  processed_at:               string;
  refund_line_items:          RefundLineItem[];
  transactions:               Transaction[];
  order_adjustments:          any[];
  admin_graphql_api_id?:      string;
  restock?:                   boolean;
  total_additional_fees_set?: Set;
  total_duties_set?:          Set;
  duties?:                    any[];
  additional_fees?:           any[];
}

export interface RefundLineItem {
  id:            number;
  line_item_id:  number;
  location_id:   number;
  quantity:      number;
  restock_type:  string;
  subtotal:      number;
  subtotal_set:  Set;
  total_tax:     number;
  total_tax_set: Set;
  line_item:     FulfillmentLineItem;
}

export interface Transaction {
  id:                   number;
  admin_graphql_api_id: string;
  amount:               string;
  authorization:        string;
  created_at:           string;
  currency:             string;
  device_id:            null;
  error_code:           null;
  gateway:              string;
  kind:                 string;
  location_id:          null;
  message:              null;
  order_id:             number;
  parent_id:            number;
  payment_id:           string;
  processed_at:         string;
  receipt:              Addresses;
  source_name:          string;
  status:               string;
  test:                 boolean;
  user_id:              null;
}

export interface PurpleShippingLine {
  title:  string;
  custom: boolean;
  handle: null;
  price:  string;
}

export interface ShippingLineElement {
  code:                             string;
  price:                            string;
  price_set:                        Set;
  discounted_price:                 string;
  discounted_price_set:             Set;
  source:                           string;
  title:                            string;
  tax_lines:                        any[];
  carrier_identifier:               null | string;
  requested_fulfillment_service_id: null | string;
  is_removed:                       boolean;
  id?:                              number;
  phone?:                           null;
  discount_allocations?:            any[];
}

export interface OrderTaxLine {
  rate:            number;
  title:           string;
  price:           number | string;
  channel_liable?: boolean | null;
  price_set?:      Set;
}
