import { ISubscriptionPlanCoupon } from "./SubscriptionPlanCoupon";

export interface ISubscriptionPlan {
  id: bigint;
  name: string;
  slug: string;
  status: boolean;
  type: string;
  interval: string;
  package_info: any;
  meta: any;
  trial_days: bigint;
  currency: string;
  price_save_description: string;
  coupon_id?: bigint;
  test: boolean;
  price: number;
  discount_type: string;
  discount: number;
  final_price: number;
  created_at: Date;
  updated_at: Date;
  coupon?: ISubscriptionPlanCoupon;

  is_subscribed?: boolean;
  is_upgradable?: boolean;
  is_free?: boolean;
  is_monthly?: boolean;
  is_yearly?: boolean;
  interval_text?: string;
  description?: string;

  duration?: number;
  is_hundred_percent_coupon?: boolean;
  is_discount_applicable?: boolean;
}
