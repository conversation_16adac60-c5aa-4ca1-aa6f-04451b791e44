/**
 * Generated from examples at https://shopify.dev/docs/api/admin-rest/2024-04/resources/webhook
 * and https://shopify.dev/docs/api/admin-rest/2024-04/resources/refund
 * using https://app.quicktype.io/
 */

export interface Refund {
  id:                         number;
  order_id?:                  number;
  created_at:                 string;
  note:                       string;
  user_id:                    number | null;
  processed_at:               string;
  restock:                    boolean;
  duties:                     any[] | DutiesClass;
  total_duties_set?:          TotalAdditionalFeesSetClass;
  return?:                    null;
  refund_shipping_lines?:     any[];
  admin_graphql_api_id?:      string;
  order_adjustments:          OrderAdjustment[];
  refund_line_items:          RefundLineItem[];
  transactions:               Transaction[];
  refund_duties?:             RefundDuty[];
  additional_fees?:           any[];
  total_additional_fees_set?: TotalAdditionalFeesSetClass;
}

export interface DutiesClass {
  duties: Duty[];
}

export interface Duty {
  duty_id:    number;
  amount_set: TotalAdditionalFeesSetClass;
}

export interface TotalAdditionalFeesSetClass {
  shop_money:        TotalAdditionalFeesSetPresentmentMoney;
  presentment_money: TotalAdditionalFeesSetPresentmentMoney;
}

export interface TotalAdditionalFeesSetPresentmentMoney {
  amount:        string;
  currency_code: string;
}

export interface OrderAdjustment {
  id:             number;
  order_id:       number;
  refund_id:      number;
  amount:         string;
  tax_amount:     string;
  kind:           string;
  reason:         string;
  amount_set:     AmountSet;
  tax_amount_set: AmountSet;
}

export interface AmountSet {
  shop_money:        TaxAmountSetPresentmentMoney;
  presentment_money: TaxAmountSetPresentmentMoney;
}

export interface TaxAmountSetPresentmentMoney {
  amount:        number;
  currency_code: string;
}

export interface RefundDuty {
  duty_id:     number;
  refund_type: string;
}

export interface RefundLineItem {
  id:            number;
  quantity:      number;
  line_item_id:  number;
  location_id:   number | null;
  restock_type:  string;
  subtotal:      number;
  total_tax:     number;
  subtotal_set:  SubtotalSetClass;
  total_tax_set: SubtotalSetClass;
  line_item:     LineItem;
}

export interface LineItem {
  id?:                           number;
  variant_id?:                   number;
  title?:                        string;
  quantity?:                     number;
  sku?:                          string;
  variant_title?:                null | string;
  vendor?:                       null;
  fulfillment_service?:          string;
  product_id?:                   number;
  requires_shipping?:            boolean;
  taxable?:                      boolean;
  gift_card?:                    boolean;
  name?:                         string;
  variant_inventory_management?: string;
  properties?:                   Property[];
  product_exists?:               boolean;
  fulfillable_quantity?:         number;
  grams?:                        number;
  price?:                        string;
  total_discount?:               string;
  fulfillment_status?:           null;
  price_set?:                    TotalAdditionalFeesSetClass;
  total_discount_set?:           TotalAdditionalFeesSetClass;
  discount_allocations?:         DiscountAllocation[];
  duties?:                       any[];
  admin_graphql_api_id?:         string;
  tax_lines?:                    TaxLine[];
}

export interface DiscountAllocation {
  amount:                     string;
  discount_application_index: number;
  amount_set:                 TotalAdditionalFeesSetClass;
}

export interface Property {
  name:  string;
  value: string;
}

export interface TaxLine {
  title:          string;
  price:          string;
  rate:           number;
  channel_liable: null;
  price_set:      TotalAdditionalFeesSetClass;
}

export interface SubtotalSetClass {
  shop_money:        SubtotalSetPresentmentMoney;
  presentment_money: SubtotalSetPresentmentMoney;
}

export interface SubtotalSetPresentmentMoney {
  amount:        number | string;
  currency_code: string;
}

export interface Transaction {
  id:                      number;
  order_id:                number;
  amount:                  string;
  kind:                    string;
  gateway:                 string;
  status:                  string;
  message:                 null | string;
  created_at:              string;
  test:                    boolean;
  authorization:           null | string;
  currency:                string;
  location_id:             null;
  user_id:                 null;
  parent_id:               number;
  device_id:               null;
  receipt:                 Receipt;
  error_code:              null;
  source_name:             string;
  processed_at?:           string;
  payment_id?:             string;
  total_unsettled_set?:    TotalUnsettledSet;
  manual_payment_gateway?: boolean;
  admin_graphql_api_id?:   string;
}

export interface Receipt {
}

export interface TotalUnsettledSet {
  presentment_money: TotalUnsettledSetPresentmentMoney;
  shop_money:        TotalUnsettledSetPresentmentMoney;
}

export interface TotalUnsettledSetPresentmentMoney {
  amount:   string;
  currency: string;
}
