declare namespace Express {
  export interface Request {
    shopDetails?: IShopDetails;
  }
}

// https://stackoverflow.com/a/49286056/2058437
type ValueOf<T> = T[keyof T];

// https://stackoverflow.com/a/59774743/2058437
type AsyncReturnType<T extends (..._args: any) => Promise<any>> = Awaited<ReturnType<T>>;

// https://stackoverflow.com/a/53229567/2058437
type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };
type XOR<T, U> = T | U extends object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;

type SessionParams = import("@shopify/shopify-api").SessionParams;

type OrderBase = import("./order.js").Order;
type Order = Omit<OrderBase, "customer" | "billing_address" | "shipping_address" | "customer_locale" | "refunds"> & {
  customer: OrderBase["customer"] | null;
  billing_address: OrderBase["billing_address"] | null;
  shipping_address: OrderBase["shipping_address"] | null;
  customer_locale: OrderBase["customer_locale"] | null;
  refunds: import("./refund.js").Refund[];
};

type SelectedItems = import("@shopify/polaris/build/ts/src/utilities/resource-list").ResourceListSelectedItems;

type UserErrors = { message: string; field?: string[] }[];

type ProductOptionType =
  | "Checkbox"
  | "Dropdown"
  | "Image swatch"
  | "Radio button"
  | "Button"
  | "Text box"
  | "Multi-line text box"
  | "Number field"
  | "Date picker"
  | "Color swatch"
  | "File upload"
  | "HTML";

interface IProductOption {
  id: number;
  title: string;
  type: ProductOptionType;
  nickname?: string;
  inCartName?: string;
  placeholderText?: string;
  description?: string;
  isRequired?: boolean;
  isMultiSelect?: boolean;
  values: {
    title?: string;
    description?: string;
    price?: number;
    addonType?: "existing" | "new";
    addonProduct?: { id: string; variantId: string; handle: string };
    isDefault?: boolean;
    isApplyDiscount?: boolean;
    image?: { fileId: string; url: string };
    isEnlargeOnHover?: boolean;
    color?: string;
    htmlContent?: string;
    // Properties for HTML content chunking
    htmlContentChunked?: boolean;
    htmlContentChunksCount?: number;
    htmlContentChunk?: boolean;
    chunkIndex?: number;
    chunkContent?: string;
  }[];
  productIds: string[];
  createdAt?: string;
  minimumSelectionCount?: number;
  maximumSelectionCount?: number;
  isShowSelectedValues?: boolean;
  minimumValue?: number;
  maximumValue?: number;
  allowedFileTypes?: string[];
}

interface IProductOptionRuleCondition {
  optionId: number | string;
  isShopifyOption?: boolean;
  relation: "in" | "not-in";
  values: string[];
}

interface IProductOptionRuleAction {
  type: "hide" | "show";
  optionId: number;
  values: string[] | "All";
}

interface IProductOptionRule {
  name: string;
  isAllConditionsRequired?: boolean;
  conditions: IProductOptionRuleCondition[];
  actions: IProductOptionRuleAction[];
}

type ProductSelectionMethod = "manual" | "collection" | "tag" | "vendor";

interface IProductOptionSet {
  id: number;
  title: string;
  rank?: number;
  optionIds: number[];
  productIds: string[];
  productSelectionMethod?: ProductSelectionMethod;
  productSelectionValues?: string[];
  rules?: IProductOptionRule[];
  createdAt?: string;
}

interface IShopDetails {
  shop: string;
  currency: string;
  /**
   * Note: This field is only used for not showing the enable extension banner for previous users.
   * Replaced by app block check at `/api/appBlock/enabled` endpoint.
   */
  isExtensionEnabled: boolean;
  isGrandfathered: boolean;
  isAddonsFree: boolean;
  isPartnerDevelopment: boolean;
  isSubscriptionActive: boolean | undefined;
  isOn18DollarPlan: boolean;
  isFeatureLimited: boolean;
}

type StyleOptions = typeof import("../defaults.js").defaultStyleOptions & {
  cssCustomizations: Record<string, Record<string, string | undefined> | undefined>;
};
