# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "f08acbb1474e3d8959abb778fb2dd664"
name = "EasyFlow"
handle = "product-options-35"
application_url = "https://app.easy-flow.app"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_themes,write_files,write_metaobject_definitions,write_metaobjects,write_products,write_publications"

[auth]
redirect_urls = [
  "https://app.easy-flow.app/auth/callback",
  "https://app.easy-flow.app/auth/shopify/callback",
  "https://app.easy-flow.app/api/auth/callback"
]

[webhooks]
api_version = "2025-01"

  [[webhooks.subscriptions]]
  uri = "/api/webhooks"
  compliance_topics = [ "customers/data_request", "customers/redact", "shop/redact" ]

[app_proxy]
url = "https://app.easy-flow.app/appProxy"
subpath = "appProxy"
prefix = "apps"

[pos]
embedded = false
