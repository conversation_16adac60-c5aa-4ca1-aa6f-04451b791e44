{
  "eslint.validate": ["javascript", "javascriptreact"],
  "sort-imports.default-sort-style": "module-scoped",
  "js/ts.implicitProjectConfig.checkJs": true,
  "files.autoSave": "off",

  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true
  },
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    // "source.removeUnusedImports": "always",
    "source.sortImports": "always"
  },
  "typescript.tsserver.maxTsServerMemory": 5072,
  "editor.defaultFormatter": "esbenp.prettier-vscode"
}
